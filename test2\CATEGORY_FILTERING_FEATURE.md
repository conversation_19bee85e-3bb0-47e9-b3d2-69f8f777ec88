# ميزة فلترة التصنيفات في تطبيق زاد اليمن
## Category Filtering Feature

## 🎯 نظرة عامة
تم إضافة ميزة فلترة المحتوى حسب التصنيفات في الصفحة الرئيسية وصفحة المتاجر، مما يتيح للمستخدمين تصفح المنتجات والمتاجر بسهولة حسب الفئة المطلوبة.

## ✅ الميزات المضافة

### 🏠 **الصفحة الرئيسية (HomePages.dart)**

#### **1. تحويل إلى StatefulWidget:**
```dart
class HomePage extends StatefulWidget {
  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String selectedCategory = "الكل"; // التصنيف المختار
  
  // باقي الكود...
}
```

#### **2. ربط التصنيفات بالفلترة:**
```dart
CategoriesWidget(
  selectedCategory: selectedCategory,
  onCategorySelected: (category) {
    setState(() {
      selectedCategory = category;
    });
  },
),
```

#### **3. ربط العناصر بالفلترة:**
```dart
ItemsWidgets(selectedCategory: selectedCategory),
```

### 🎨 **مكون التصنيفات (CategoriesWidget.dart)**

#### **1. إضافة المعاملات المطلوبة:**
```dart
class CategoriesWidget extends StatelessWidget {
  final String selectedCategory;
  final Function(String) onCategorySelected;

  const CategoriesWidget({
    Key? key,
    required this.selectedCategory,
    required this.onCategorySelected,
  }) : super(key: key);
```

#### **2. قائمة التصنيفات المحدثة:**
```dart
List<Map<String, dynamic>> categories = [
  {"image": "images/1.png", "name": "الكل"},
  {"image": "images/2.png", "name": "مطاعم"},
  {"image": "images/3.png", "name": "بقالة"},
  {"image": "images/4.png", "name": "صيدليات"},
  {"image": "images/5.png", "name": "ملابس"},
  {"image": "images/6.png", "name": "إلكترونيات"},
  {"image": "images/7.png", "name": "مخابز"},
  {"image": "images/8.png", "name": "حلويات"},
];
```

#### **3. التفاعل مع النقر:**
```dart
GestureDetector(
  onTap: () {
    onCategorySelected(categories[i]["name"]);
  },
  child: Container(
    decoration: BoxDecoration(
      color: selectedCategory == categories[i]["name"] 
          ? Color(0xFF4C53A5)  // لون مختار
          : Colors.white,      // لون عادي
      // باقي التصميم...
    ),
  ),
)
```

### 🛍️ **مكون العناصر (ItemsWidgets.dart)**

#### **1. إضافة معامل الفلترة:**
```dart
class ItemsWidgets extends StatelessWidget {
  final String selectedCategory;

  const ItemsWidgets({
    Key? key,
    required this.selectedCategory,
  }) : super(key: key);
```

#### **2. قائمة المنتجات مع التصنيفات:**
```dart
List<Map<String, dynamic>> allItems = [
  {"id": 1, "name": "برجر لحم", "category": "مطاعم", "price": "25", "description": "برجر لحم طازج"},
  {"id": 2, "name": "أرز بسمتي", "category": "بقالة", "price": "15", "description": "أرز بسمتي فاخر"},
  {"id": 3, "name": "بنادول", "category": "صيدليات", "price": "8", "description": "مسكن للألم"},
  {"id": 4, "name": "قميص قطني", "category": "ملابس", "price": "45", "description": "قميص قطني مريح"},
  {"id": 5, "name": "هاتف ذكي", "category": "إلكترونيات", "price": "800", "description": "هاتف ذكي حديث"},
  {"id": 6, "name": "خبز طازج", "category": "مخابز", "price": "3", "description": "خبز طازج يومياً"},
  {"id": 7, "name": "كنافة", "category": "حلويات", "price": "20", "description": "كنافة بالجبن"},
  // المزيد من المنتجات...
];
```

#### **3. منطق الفلترة:**
```dart
List<Map<String, dynamic>> filteredItems = selectedCategory == "الكل" 
    ? allItems 
    : allItems.where((item) => item["category"] == selectedCategory).toList();
```

#### **4. عرض البيانات الحقيقية:**
```dart
return GridView.builder(
  itemCount: filteredItems.length,
  itemBuilder: (context, index) {
    final item = filteredItems[index];
    return Container(
      child: Column(
        children: [
          // عرض اسم المنتج الحقيقي
          Text(item["name"]),
          // عرض الوصف الحقيقي
          Text(item["description"]),
          // عرض السعر الحقيقي
          Text("${item["price"]} ر.ي"),
        ],
      ),
    );
  },
);
```

### 🛍️ **صفحة المتاجر (StoresPage.dart)**

#### **1. تحويل إلى StatefulWidget:**
```dart
class StoresPage extends StatefulWidget {
  @override
  _StoresPageState createState() => _StoresPageState();
}

class _StoresPageState extends State<StoresPage> {
  String selectedCategory = "الكل";
```

#### **2. تصنيفات المتاجر المحدثة:**
```dart
List<Map<String, dynamic>> categories = [
  {"icon": Icons.apps, "name": "الكل"},
  {"icon": Icons.fastfood, "name": "مطاعم"},
  {"icon": Icons.local_grocery_store, "name": "بقالة"},
  {"icon": Icons.local_pharmacy, "name": "صيدليات"},
  {"icon": Icons.shopping_bag, "name": "ملابس"},
  {"icon": Icons.devices, "name": "إلكترونيات"},
  {"icon": Icons.bakery_dining, "name": "مخابز"},
  {"icon": Icons.cake, "name": "حلويات"},
];
```

#### **3. قائمة المتاجر مع التصنيفات:**
```dart
List<Map<String, dynamic>> allStores = [
  {"name": "مطعم الشرق", "category": "مطاعم", "rating": 4.8, "deliveryTime": "30-45 دقيقة"},
  {"name": "سوبرماركت الأمانة", "category": "بقالة", "rating": 4.5, "deliveryTime": "20-30 دقيقة"},
  {"name": "صيدلية الصحة", "category": "صيدليات", "rating": 4.7, "deliveryTime": "15-25 دقيقة"},
  {"name": "متجر الأناقة", "category": "ملابس", "rating": 4.3, "deliveryTime": "40-60 دقيقة"},
  {"name": "متجر التقنية", "category": "إلكترونيات", "rating": 4.6, "deliveryTime": "35-50 دقيقة"},
  {"name": "مخبز الأسرة", "category": "مخابز", "rating": 4.4, "deliveryTime": "15-20 دقيقة"},
  {"name": "حلويات دمشق", "category": "حلويات", "rating": 4.9, "deliveryTime": "25-35 دقيقة"},
  // المزيد من المتاجر...
];
```

#### **4. فلترة المتاجر:**
```dart
List<Map<String, dynamic>> filteredStores = selectedCategory == "الكل" 
    ? allStores 
    : allStores.where((store) => store["category"] == selectedCategory).toList();
```

## 🎨 **التحسينات البصرية**

### **1. تمييز التصنيف المختار:**
- **لون الخلفية:** يتغير إلى `Color(0xFF4C53A5)` عند الاختيار
- **لون النص:** يتغير إلى أبيض عند الاختيار
- **الحدود:** تظهر حدود ملونة للتصنيف المختار

### **2. تأثيرات التفاعل:**
- **GestureDetector:** للتفاعل مع النقر
- **setState:** لتحديث الواجهة فوراً
- **انتقالات سلسة:** بين التصنيفات

## 🔧 **كيفية العمل**

### **في الصفحة الرئيسية:**
1. المستخدم ينقر على تصنيف معين
2. يتم استدعاء `onCategorySelected`
3. يتم تحديث `selectedCategory` في الحالة
4. يتم إعادة بناء `ItemsWidgets` مع الفلتر الجديد
5. تظهر المنتجات المفلترة فقط

### **في صفحة المتاجر:**
1. المستخدم ينقر على تصنيف معين
2. يتم تحديث `selectedCategory` في `setState`
3. يتم فلترة قائمة `allStores`
4. تظهر المتاجر المفلترة فقط

## 📊 **البيانات المتاحة**

### **التصنيفات:**
- 🏪 **الكل** - جميع المنتجات/المتاجر
- 🍔 **مطاعم** - المطاعم والوجبات السريعة
- 🛒 **بقالة** - المواد الغذائية والمنزلية
- 💊 **صيدليات** - الأدوية والمستلزمات الطبية
- 👕 **ملابس** - الملابس والأزياء
- 📱 **إلكترونيات** - الأجهزة الإلكترونية
- 🍞 **مخابز** - المخبوزات والخبز
- 🍰 **حلويات** - الحلويات والكيك

### **المنتجات (14 منتج):**
- برجر لحم، بيتزا مارجريتا (مطاعم)
- أرز بسمتي، زيت زيتون (بقالة)
- بنادول، فيتامين سي (صيدليات)
- قميص قطني، جينز أزرق (ملابس)
- هاتف ذكي، سماعات (إلكترونيات)
- خبز طازج، كرواسان (مخابز)
- كنافة، تشيز كيك (حلويات)

### **المتاجر (8 متاجر):**
- مطعم الشرق، مطعم البرجر (مطاعم)
- سوبرماركت الأمانة (بقالة)
- صيدلية الصحة (صيدليات)
- متجر الأناقة (ملابس)
- متجر التقنية (إلكترونيات)
- مخبز الأسرة (مخابز)
- حلويات دمشق (حلويات)

## 🚀 **الفوائد المحققة**

### **1. تجربة مستخدم محسنة:**
- تصفح أسرع للمحتوى
- العثور على المنتجات بسهولة
- تنظيم أفضل للمعلومات

### **2. أداء محسن:**
- عرض المحتوى ذي الصلة فقط
- تقليل التمرير غير الضروري
- استجابة فورية للتفاعل

### **3. سهولة الاستخدام:**
- واجهة بديهية وواضحة
- تمييز بصري للتصنيف المختار
- انتقالات سلسة بين التصنيفات

## 🎯 **كيفية الاستخدام**

### **للمستخدم:**
1. افتح الصفحة الرئيسية أو صفحة المتاجر
2. انقر على التصنيف المطلوب من الشريط العلوي
3. شاهد المحتوى يتم فلترته تلقائياً
4. انقر على "الكل" لعرض جميع العناصر

### **للمطور:**
```dart
// إضافة منتج جديد
{"id": 15, "name": "منتج جديد", "category": "التصنيف", "price": "السعر", "description": "الوصف"}

// إضافة متجر جديد
{"name": "اسم المتجر", "category": "التصنيف", "rating": 4.5, "deliveryTime": "الوقت", "image": "المسار"}

// إضافة تصنيف جديد
{"image": "images/icon.png", "name": "التصنيف الجديد"}
{"icon": Icons.new_icon, "name": "التصنيف الجديد"}
```

## 🎉 **النتيجة النهائية**

تطبيق "زاد اليمن" أصبح الآن يحتوي على:
- ✅ **فلترة ذكية** للمنتجات والمتاجر
- ✅ **تفاعل سلس** مع التصنيفات
- ✅ **تصميم جميل** مع تمييز بصري
- ✅ **أداء محسن** مع عرض المحتوى ذي الصلة
- ✅ **سهولة استخدام** للمستخدمين

🚀 **الميزة جاهزة للاستخدام في كلا الصفحتين!**
