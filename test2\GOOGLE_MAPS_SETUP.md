# إعداد Google Maps للتطبيق

## 🎯 المطلوب

لتشغيل نظام تتبع الطلبات مع الخريطة التفاعلية، تحتاج لإعداد Google Maps API.

## 📋 خطوات الإعداد

### 1. الحصول على Google Maps API Key

#### أ. إنشاء مشروع في Google Cloud Console:
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعل Google Maps SDK for Android
4. فعل Google Maps SDK for iOS
5. أنشئ API Key جديد

#### ب. تقييد API Key (مهم للأمان):
```
Application restrictions:
- Android apps: أضف package name للتطبيق
- iOS apps: أضف bundle identifier للتطبيق

API restrictions:
- Maps SDK for Android
- Maps SDK for iOS
```

### 2. إعداد Android

#### أ. إضافة API Key في android/app/src/main/AndroidManifest.xml:
```xml
<application
    android:label="test2"
    android:name="${applicationName}"
    android:icon="@mipmap/ic_launcher">
    
    <!-- إضافة Google Maps API Key -->
    <meta-data android:name="com.google.android.geo.API_KEY"
               android:value="YOUR_ANDROID_API_KEY_HERE"/>
    
    <activity
        android:name=".MainActivity"
        android:exported="true"
        android:launchMode="singleTop"
        android:theme="@style/LaunchTheme"
        android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
        android:hardwareAccelerated="true"
        android:windowSoftInputMode="adjustResize">
        <!-- باقي الكود... -->
    </activity>
</application>
```

#### ب. إضافة الأذونات في android/app/src/main/AndroidManifest.xml:
✅ **تم إضافتها بالفعل!** الأذونات التالية موجودة الآن في الملف:
```xml
<!-- أذونات الموقع المطلوبة لتتبع الطلبات -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### 3. إعداد iOS

#### أ. إضافة API Key في ios/Runner/AppDelegate.swift:
```swift
import UIKit
import Flutter
import GoogleMaps

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // إضافة Google Maps API Key
    GMSServices.provideAPIKey("YOUR_IOS_API_KEY_HERE")
    
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
```

#### ب. إضافة الأذونات في ios/Runner/Info.plist:
✅ **تم إضافتها بالفعل!** الأذونات التالية موجودة الآن في الملف:
```xml
<!-- أذونات الموقع المطلوبة لتتبع الطلبات -->
<key>NSLocationWhenInUseUsageDescription</key>
<string>يحتاج التطبيق للوصول إلى موقعك لتتبع طلبك وعرضه على الخريطة مع موقع سائق التوصيل</string>

<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>يحتاج التطبيق للوصول إلى موقعك لتتبع طلبك وعرضه على الخريطة مع موقع سائق التوصيل</string>

<key>NSLocationAlwaysUsageDescription</key>
<string>يحتاج التطبيق للوصول إلى موقعك لتتبع طلبك وعرضه على الخريطة مع موقع سائق التوصيل</string>
```

### 4. تحديث pubspec.yaml

تأكد من وجود المكتبات المطلوبة:
```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # مكتبات الخرائط والموقع
  google_maps_flutter: ^2.5.0
  geolocator: ^11.0.0
  permission_handler: ^11.3.1
  flutter_polyline_points: ^2.0.0
```

### 5. تشغيل الأوامر

```bash
# تحديث المكتبات
flutter pub get

# تنظيف المشروع
flutter clean

# إعادة البناء
flutter run
```

## 🔧 إعداد متقدم (اختياري)

### 1. تخصيص مظهر الخريطة

إنشاء ملف `assets/map_style.json`:
```json
[
  {
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#f5f5f5"
      }
    ]
  },
  {
    "elementType": "labels.icon",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  }
]
```

### 2. إضافة أيقونات مخصصة

```dart
// في OrderTrackingPage.dart
BitmapDescriptor? customUserIcon;
BitmapDescriptor? customDriverIcon;

@override
void initState() {
  super.initState();
  _loadCustomMarkers();
}

Future<void> _loadCustomMarkers() async {
  customUserIcon = await BitmapDescriptor.fromAssetImage(
    ImageConfiguration(size: Size(48, 48)),
    'assets/user_marker.png',
  );
  
  customDriverIcon = await BitmapDescriptor.fromAssetImage(
    ImageConfiguration(size: Size(48, 48)),
    'assets/driver_marker.png',
  );
}
```

## 🚨 مشاكل شائعة وحلولها

### 1. خطأ "API key not found"
**الحل:**
- تأكد من إضافة API Key في المكان الصحيح
- تأكد من عدم وجود مسافات إضافية
- أعد بناء التطبيق بالكامل

### 2. خطأ "This app is not authorized"
**الحل:**
- تأكد من إضافة package name/bundle identifier في Google Cloud Console
- تأكد من تفعيل Maps SDK للمنصة المطلوبة

### 3. الخريطة لا تظهر
**الحل:**
- تأكد من الاتصال بالإنترنت
- تأكد من صحة API Key
- تحقق من console للأخطاء

### 4. أذونات الموقع لا تعمل
**الحل:**
- تأكد من إضافة الأذونات في AndroidManifest.xml و Info.plist
- تأكد من طلب الأذونات في الكود
- اختبر على جهاز حقيقي وليس محاكي

## 📱 اختبار الإعداد

### 1. اختبار أساسي:
```dart
// في أي صفحة
GoogleMap(
  onMapCreated: (GoogleMapController controller) {
    print('تم تحميل الخريطة بنجاح!');
  },
  initialCameraPosition: CameraPosition(
    target: LatLng(15.3694, 44.1910), // صنعاء
    zoom: 14.0,
  ),
)
```

### 2. اختبار الموقع:
```dart
// في أي صفحة
Future<void> testLocation() async {
  Position position = await Geolocator.getCurrentPosition();
  print('الموقع الحالي: ${position.latitude}, ${position.longitude}');
}
```

## 💡 نصائح مهمة

### 1. الأمان:
- **لا تشارك API Key** في الكود المفتوح
- **استخدم متغيرات البيئة** للمفاتيح الحساسة
- **قيد API Key** للتطبيق فقط

### 2. الأداء:
- **استخدم دقة متوسطة** للموقع لتوفير البطارية
- **أوقف التتبع** عند عدم الحاجة
- **استخدم cache** للخرائط المحملة

### 3. تجربة المستخدم:
- **اطلب الأذونات** بوضوح
- **اشرح سبب الحاجة** للموقع
- **وفر بدائل** عند رفض الأذونات

## ✅ قائمة المراجعة

- [ ] إنشاء Google Cloud Project
- [ ] الحصول على API Keys (Android + iOS)
- [ ] إضافة API Key في AndroidManifest.xml
- [ ] إضافة API Key في AppDelegate.swift
- [ ] إضافة أذونات الموقع (Android + iOS)
- [ ] تحديث pubspec.yaml
- [ ] تشغيل flutter pub get
- [ ] اختبار الخريطة
- [ ] اختبار الموقع
- [ ] اختبار على جهاز حقيقي

## 🎉 النتيجة

بعد إكمال هذه الخطوات، ستكون قادراً على:
- ✅ عرض خرائط Google في التطبيق
- ✅ الحصول على موقع المستخدم
- ✅ عرض علامات على الخريطة
- ✅ تتبع الطلبات بصرياً

نظام تتبع الطلبات جاهز للعمل! 🚀
