import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:test2/utils/AppColors.dart';

/// مجموعة رسائل SnackBar جميلة ومخصصة
class CustomSnackBars {
  
  /// عرض رسالة نجاح جميلة
  static void showSuccess(
    BuildContext context, {
    required String message,
    String? subtitle,
    Duration? duration,
    VoidCallback? onAction,
    String? actionLabel,
  }) {
    _showCustomSnackBar(
      context,
      message: message,
      subtitle: subtitle,
      icon: Icons.check_circle,
      backgroundColor: Colors.green,
      iconColor: Colors.white,
      duration: duration ?? Duration(seconds: 3),
      onAction: onAction,
      actionLabel: actionLabel,
    );
  }

  /// عرض رسالة خطأ جميلة
  static void showError(
    BuildContext context, {
    required String message,
    String? subtitle,
    Duration? duration,
    VoidCallback? onAction,
    String? actionLabel,
  }) {
    _showCustomSnackBar(
      context,
      message: message,
      subtitle: subtitle,
      icon: Icons.error,
      backgroundColor: Colors.red,
      iconColor: Colors.white,
      duration: duration ?? Duration(seconds: 4),
      onAction: onAction,
      actionLabel: actionLabel,
    );
  }

  /// عرض رسالة تحذير جميلة
  static void showWarning(
    BuildContext context, {
    required String message,
    String? subtitle,
    Duration? duration,
    VoidCallback? onAction,
    String? actionLabel,
  }) {
    _showCustomSnackBar(
      context,
      message: message,
      subtitle: subtitle,
      icon: Icons.warning,
      backgroundColor: Colors.orange,
      iconColor: Colors.white,
      duration: duration ?? Duration(seconds: 4),
      onAction: onAction,
      actionLabel: actionLabel,
    );
  }

  /// عرض رسالة معلومات جميلة
  static void showInfo(
    BuildContext context, {
    required String message,
    String? subtitle,
    Duration? duration,
    VoidCallback? onAction,
    String? actionLabel,
  }) {
    _showCustomSnackBar(
      context,
      message: message,
      subtitle: subtitle,
      icon: Icons.info,
      backgroundColor: Colors.blue,
      iconColor: Colors.white,
      duration: duration ?? Duration(seconds: 3),
      onAction: onAction,
      actionLabel: actionLabel,
    );
  }

  /// عرض رسالة نجاح GPS
  static void showGPSSuccess(BuildContext context) {
    showSuccess(
      context,
      message: 'تم تفعيل خدمة الموقع بنجاح!',
      subtitle: 'يمكنك الآن تتبع طلبك على الخريطة',
      actionLabel: 'رائع',
    );
  }

  /// عرض رسالة خطأ GPS
  static void showGPSError(BuildContext context, String error) {
    showError(
      context,
      message: 'خطأ في خدمة الموقع',
      subtitle: error,
      actionLabel: 'إعادة المحاولة',
    );
  }

  /// عرض رسالة تحذير GPS
  static void showGPSWarning(BuildContext context, String warning) {
    showWarning(
      context,
      message: 'تنبيه خدمة الموقع',
      subtitle: warning,
      actionLabel: 'فهمت',
    );
  }

  /// الدالة الأساسية لعرض SnackBar مخصص
  static void _showCustomSnackBar(
    BuildContext context, {
    required String message,
    String? subtitle,
    required IconData icon,
    required Color backgroundColor,
    required Color iconColor,
    required Duration duration,
    VoidCallback? onAction,
    String? actionLabel,
  }) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: CustomSnackBarContent(
          message: message,
          subtitle: subtitle,
          icon: icon,
          iconColor: iconColor,
        ),
        backgroundColor: backgroundColor,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        margin: EdgeInsets.all(16.w),
        action: (onAction != null && actionLabel != null)
            ? SnackBarAction(
                label: actionLabel,
                textColor: Colors.white,
                onPressed: onAction,
              )
            : null,
      ),
    );
  }
}

/// محتوى SnackBar مخصص
class CustomSnackBarContent extends StatelessWidget {
  final String message;
  final String? subtitle;
  final IconData icon;
  final Color iconColor;

  const CustomSnackBarContent({
    Key? key,
    required this.message,
    this.subtitle,
    required this.icon,
    required this.iconColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // الأيقونة
        Container(
          width: 40.w,
          height: 40.h,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: 24.sp,
          ),
        ),
        
        SizedBox(width: 12.w),
        
        // النص
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                message,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (subtitle != null) ...[
                SizedBox(height: 4.h),
                Text(
                  subtitle!,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}

/// رسائل Toast خفيفة وجميلة
class CustomToasts {
  
  /// عرض toast نجاح
  static void showSuccess(BuildContext context, String message) {
    _showToast(
      context,
      message: message,
      icon: Icons.check_circle,
      backgroundColor: Colors.green,
    );
  }

  /// عرض toast خطأ
  static void showError(BuildContext context, String message) {
    _showToast(
      context,
      message: message,
      icon: Icons.error,
      backgroundColor: Colors.red,
    );
  }

  /// عرض toast معلومات
  static void showInfo(BuildContext context, String message) {
    _showToast(
      context,
      message: message,
      icon: Icons.info,
      backgroundColor: Colors.blue,
    );
  }

  /// الدالة الأساسية لعرض Toast
  static void _showToast(
    BuildContext context, {
    required String message,
    required IconData icon,
    required Color backgroundColor,
  }) {
    final overlay = Overlay.of(context);
    final overlayEntry = OverlayEntry(
      builder: (context) => ToastWidget(
        message: message,
        icon: icon,
        backgroundColor: backgroundColor,
      ),
    );

    overlay.insert(overlayEntry);

    // إزالة Toast بعد 2 ثانية
    Future.delayed(Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }
}

/// مكون Toast مخصص
class ToastWidget extends StatefulWidget {
  final String message;
  final IconData icon;
  final Color backgroundColor;

  const ToastWidget({
    Key? key,
    required this.message,
    required this.icon,
    required this.backgroundColor,
  }) : super(key: key);

  @override
  _ToastWidgetState createState() => _ToastWidgetState();
}

class _ToastWidgetState extends State<ToastWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 100.h,
      left: 20.w,
      right: 20.w,
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, -50 * (1 - _animation.value)),
            child: Opacity(
              opacity: _animation.value,
              child: Material(
                color: Colors.transparent,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 12.h,
                  ),
                  decoration: BoxDecoration(
                    color: widget.backgroundColor,
                    borderRadius: BorderRadius.circular(12.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 10,
                        offset: Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        widget.icon,
                        color: Colors.white,
                        size: 24.sp,
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Text(
                          widget.message,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
