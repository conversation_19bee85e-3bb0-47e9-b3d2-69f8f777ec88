import 'package:flutter/material.dart';
import 'package:test2/pages/RegistrationPage.dart';
import 'package:test2/pages/HomePages.dart';
import 'package:test2/services/UserService.dart';
import 'package:test2/utils/ResponsiveHelper.dart';
import 'package:test2/widgets/ResponsiveText.dart';
import 'package:test2/widgets/ResponsiveButton.dart';
import 'package:test2/widgets/ResponsiveContainer.dart';

class WelcomePage extends StatefulWidget {
  @override
  _WelcomePageState createState() => _WelcomePageState();
}

class _WelcomePageState extends State<WelcomePage>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final UserService _userService = UserService();

  @override
  void initState() {
    super.initState();

    // إعداد الانيميشن
    _fadeController = AnimationController(
      duration: Duration(milliseconds: 1500),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));

    // بدء الانيميشن
    _fadeController.forward();
    Future.delayed(Duration(milliseconds: 300), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF4C53A5),
                  Color(0xFF6366F1),
                  Color(0xFF8B5CF6),
                ],
              ),
            ),
            // child: SafeArea(
            //   child: SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: MediaQuery.of(context).size.height -
                        MediaQuery.of(context).padding.top -
                        MediaQuery.of(context).padding.bottom,
                  ),
                  child: Padding(
                    padding: ResponsiveHelper.responsiveEdgeInsets(
                      context,
                      mobile: 16,
                      tablet: 24,
                      desktop: 32,
                    ),
                    child: Column(
                      children: [
                        Expanded(
                          flex: 3,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // شعار التطبيق
                                ResponsiveContainer(
                                  mobileWidth: 100,
                                  tabletWidth: 120,
                                  desktopWidth: 140,
                                  mobileHeight: 100,
                                  tabletHeight: 120,
                                  desktopHeight: 140,
                                  backgroundColor: Colors.white,
                                  borderRadius: BorderRadius.circular(
                                    ResponsiveHelper.responsiveBorderRadius(
                                      context,
                                      mobile: 25,
                                      tablet: 30,
                                      desktop: 35,
                                    ),
                                  ),
                                  boxShadow: BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    spreadRadius:
                                        ResponsiveHelper.responsiveSize(
                                      context,
                                      mobile: 3,
                                      tablet: 5,
                                      desktop: 7,
                                    ),
                                    blurRadius: ResponsiveHelper.responsiveSize(
                                      context,
                                      mobile: 10,
                                      tablet: 15,
                                      desktop: 20,
                                    ),
                                    offset: Offset(0, 5),
                                  ),
                                  child: Icon(
                                    Icons.shopping_bag,
                                    size: ResponsiveHelper.responsiveIconSize(
                                      context,
                                      mobile: 50,
                                      tablet: 60,
                                      desktop: 70,
                                    ),
                                    color: Color(0xFF4C53A5),
                                  ),
                                ),

                                ResponsiveSizedBox(
                                  mobileHeight: 24,
                                  tabletHeight: 32,
                                  desktopHeight: 40,
                                ),

                                // اسم التطبيق
                                ResponsiveText(
                                  'زاد اليمن',
                                  mobileFontSize: 28,
                                  tabletFontSize: 36,
                                  desktopFontSize: 44,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    shadows: [
                                      Shadow(
                                        offset: Offset(2, 2),
                                        blurRadius: 4,
                                        color: Colors.black.withOpacity(0.3),
                                      ),
                                    ],
                                  ),
                                ),

                                ResponsiveSizedBox(
                                  mobileHeight: 12,
                                  tabletHeight: 16,
                                  desktopHeight: 20,
                                ),

                                // وصف التطبيق
                                ResponsiveText(
                                  'أفضل المتاجر والعروض\nفي مكان واحد',
                                  mobileFontSize: 16,
                                  tabletFontSize: 18,
                                  desktopFontSize: 20,
                                  color: Colors.white.withOpacity(0.9),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(height: 1.5),
                                ),
                              ],
                            ),
                          ),
                        ),

                        Expanded(
                            flex: 2,
                            child: SingleChildScrollView(
                              child: SlideTransition(
                                position: _slideAnimation,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // رسالة ترحيبية
                                    ResponsiveContainer(
                                      mobilePadding: 16,
                                      tabletPadding: 20,
                                      desktopPadding: 24,
                                      backgroundColor:
                                          Colors.white.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(
                                        ResponsiveHelper.responsiveBorderRadius(
                                          context,
                                          mobile: 12,
                                          tablet: 16,
                                          desktop: 20,
                                        ),
                                      ),
                                      border: Border.all(
                                        color: Colors.white.withOpacity(0.3),
                                        width: ResponsiveHelper
                                            .responsiveBorderWidth(context),
                                      ),
                                      child: ResponsiveColumn(
                                        spacing:
                                            ResponsiveHelper.responsiveSpacing(
                                          context,
                                          mobile: 8,
                                          tablet: 12,
                                          desktop: 16,
                                        ),
                                        children: [
                                          Icon(
                                            Icons.waving_hand,
                                            color: Colors.amber,
                                            size: ResponsiveHelper
                                                .responsiveIconSize(
                                              context,
                                              mobile: 28,
                                              tablet: 32,
                                              desktop: 36,
                                            ),
                                          ),
                                          ResponsiveTitle(
                                            'مرحباً بك!',
                                            color: Colors.white,
                                            textAlign: TextAlign.center,
                                          ),
                                          ResponsiveBody(
                                            'للحصول على تجربة أفضل، يمكنك تسجيل حساب جديد أو التصفح كضيف',
                                            color:
                                                Colors.white.withOpacity(0.9),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    ),

                                    ResponsiveSizedBox(
                                      mobileHeight: 24,
                                      tabletHeight: 32,
                                      desktopHeight: 40,
                                    ),

                                    // أزرار الخيارات
                                    ResponsiveColumn(
                                      spacing:
                                          ResponsiveHelper.responsiveSpacing(
                                        context,
                                        mobile: 12,
                                        tablet: 16,
                                        desktop: 20,
                                      ),
                                      children: [
                                        // زر التسجيل
                                        ResponsiveButton(
                                          text: 'إنشاء حساب جديد',
                                          onPressed: _goToRegistration,
                                          backgroundColor: Colors.white,
                                          textColor: Color(0xFF4C53A5),
                                          mobileWidth: double.infinity,
                                          tabletWidth: double.infinity,
                                          desktopWidth: double.infinity,
                                          mobileHeight: 48,
                                          tabletHeight: 52,
                                          desktopHeight: 56,
                                          mobileFontSize: 16,
                                          tabletFontSize: 18,
                                          desktopFontSize: 20,
                                          icon: Icons.person_add,
                                        ),

                                        // زر التصفح كضيف
                                        ResponsiveOutlinedButton(
                                          text: 'تصفح كضيف',
                                          onPressed: _browseAsGuest,
                                          borderColor:
                                              Colors.white.withOpacity(0.8),
                                          textColor: Colors.white,
                                          mobileWidth: double.infinity,
                                          tabletWidth: double.infinity,
                                          desktopWidth: double.infinity,
                                          mobileHeight: 48,
                                          tabletHeight: 52,
                                          desktopHeight: 56,
                                          mobileFontSize: 16,
                                          tabletFontSize: 18,
                                          desktopFontSize: 20,
                                          icon: Icons.visibility,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            )),

                        // معلومات إضافية
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: Padding(
                            padding:
                                ResponsiveHelper.responsiveEdgeInsetsCustom(
                              context,
                              top: 12,
                            ),
                            child: ResponsiveCaption(
                              'يمكنك تسجيل حساب لاحقاً من الإعدادات',
                              color: Colors.white.withOpacity(0.7),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            //   ),
            // )
            )
            );
  }

  void _goToRegistration() {
    Navigator.pushReplacement(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            RegistrationPage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: Duration(milliseconds: 300),
      ),
    );
  }

  void _browseAsGuest() async {
    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Center(
        child: Container(
          padding: EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(color: Color(0xFF4C53A5)),
              SizedBox(height: 5),
              Text('جاري التحضير...'),
            ],
          ),
        ),
      ),
    );

    // تسجيل دخول كضيف
    final success = await _userService.loginAsGuest();

    // إغلاق مؤشر التحميل
    Navigator.of(context).pop();

    if (success) {
      // الانتقال للصفحة الرئيسية
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => HomePage(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          transitionDuration: Duration(milliseconds: 500),
        ),
      );
    } else {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء التحضير. يرجى المحاولة مرة أخرى.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
