import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/OffersManager.dart';
import '../utils/AppColors.dart';
import '../widgets/SearchWidget.dart';
import '../widgets/CustomBottomNavBar.dart';
import '../pages/StoreDetailsPage.dart';
import '../utils/DataManager.dart';

class OffersPage extends StatefulWidget {
  @override
  _OffersPageState createState() => _OffersPageState();
}

class _OffersPageState extends State<OffersPage> {
  String searchQuery = "";
  String selectedType = "الكل";

  final List<String> offerTypes = [
    "الكل",
    "خصم",
    "منتج جديد",
    "طبق جديد",
    "عرض خاص",
    "توصيل مجاني",
    "اشتري واحد واحصل على آخر",
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // شريط البحث والفلاتر
          Container(
            color: AppColors.primaryColor,
            padding: EdgeInsets.all(15.w),
            child: Column(
              children: [
                // شريط البحث
                SearchWidget(
                  hintText: "ابحث في العروض...",
                  onSearchChanged: (query) {
                    setState(() {
                      searchQuery = query;
                    });
                  },
                  suffixIcon: searchQuery.isNotEmpty ? Icons.clear : null,
                ),

                SizedBox(height: 15.h),

                // فلاتر أنواع العروض
                _buildTypeFilters(),
              ],
            ),
          ),

          // قائمة العروض
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.backgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(25.r),
                  topRight: Radius.circular(25.r),
                ),
              ),
              child: _buildOffersList(),
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: Offset(0, -2),
              ),
            ],
          ),
          child: FlatBottomNavBar(
            currentIndex: 2, // العروض
            onTap: (index) {
              // التنقل البسيط بدلاً من NavigationHelper
              switch (index) {
                case 0:
                  Navigator.pushReplacementNamed(context, '/profile');
                  break;
                case 1:
                  Navigator.pushReplacementNamed(context, '/stores');
                  break;
                case 2:
                  Navigator.pushReplacementNamed(context, '/home');
                  break;
                case 3:
                  Navigator.pushReplacementNamed(context, '/cart');
                  break;
                case 4:
                  Navigator.pushReplacementNamed(context, '/orders');
                  break;
              }
            },
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryColor,
      elevation: 0,
      centerTitle: true,
      title: Text(
        "العروض والتخفيضات",
        style: TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.whiteColor,
        ),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
      actions: [
        IconButton(
          icon: Icon(
            Icons.filter_list,
            color: AppColors.whiteColor,
          ),
          onPressed: () {
            _showFilterDialog();
          },
        ),
      ],
    );
  }

  Widget _buildTypeFilters() {
    return SizedBox(
      height: 40.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: offerTypes.length,
        itemBuilder: (context, index) {
          final type = offerTypes[index];
          final isSelected = selectedType == type;

          return GestureDetector(
            onTap: () {
              setState(() {
                selectedType = type;
              });
            },
            child: Container(
              margin: EdgeInsets.only(right: 10.w),
              padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.whiteColor : Colors.transparent,
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(
                  color: AppColors.whiteColor,
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  type,
                  style: TextStyle(
                    color: isSelected
                        ? AppColors.primaryColor
                        : AppColors.whiteColor,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildOffersList() {
    // الحصول على العروض المفلترة
    List<Map<String, dynamic>> offers = _getFilteredOffers();

    if (offers.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: EdgeInsets.all(15.w),
      itemCount: offers.length,
      itemBuilder: (context, index) {
        final offer = offers[index];
        return _buildOfferListItem(offer);
      },
    );
  }

  List<Map<String, dynamic>> _getFilteredOffers() {
    List<Map<String, dynamic>> offers;

    // البحث النصي
    if (searchQuery.isNotEmpty) {
      offers = OffersManager.searchOffers(searchQuery);
    } else {
      offers = OffersManager.getValidOffers();
    }

    // فلترة حسب النوع
    if (selectedType != "الكل") {
      final typeMap = {
        "خصم": OffersManager.DISCOUNT,
        "منتج جديد": OffersManager.NEW_ITEM,
        "طبق جديد": OffersManager.NEW_DISH,
        "عرض خاص": OffersManager.SPECIAL_OFFER,
        "توصيل مجاني": OffersManager.FREE_DELIVERY,
        "اشتري واحد واحصل على آخر": OffersManager.BUY_ONE_GET_ONE,
      };

      final filterType = typeMap[selectedType];
      if (filterType != null) {
        offers = offers.where((offer) => offer['type'] == filterType).toList();
      }
    }

    return OffersManager.sortOffersByPriority(offers);
  }

  Widget _buildOfferListItem(Map<String, dynamic> offer) {
    final offerType = offer['type'] ?? '';
    final backgroundColor =
        offer['backgroundColor'] ?? OffersManager.getOfferColor(offerType);
    final daysRemaining = OffersManager.getDaysRemaining(offer);

    return Container(
      margin: EdgeInsets.only(bottom: 15.h),
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(15.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 8,
            spreadRadius: 1,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showOfferDetails(offer),
          borderRadius: BorderRadius.circular(15.r),
          child: Padding(
            padding: EdgeInsets.all(15.w),
            child: Row(
              textDirection: TextDirection.rtl,
              children: [
                // صورة العرض
                Container(
                  width: 80.w,
                  height: 80.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    gradient: LinearGradient(
                      colors: [
                        backgroundColor.withOpacity(0.2),
                        backgroundColor.withOpacity(0.1),
                      ],
                    ),
                  ),
                  child: Icon(
                    offer['icon'] ?? OffersManager.getOfferIcon(offerType),
                    color: backgroundColor,
                    size: 40.sp,
                  ),
                ),

                SizedBox(width: 15.w),

                // معلومات العرض
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    textDirection: TextDirection.rtl,
                    children: [
                      // نوع العرض والأيام المتبقية
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        textDirection: TextDirection.rtl,
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8.w, vertical: 4.h),
                            decoration: BoxDecoration(
                              color: backgroundColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Text(
                              OffersManager.getOfferTypeText(offerType),
                              style: TextStyle(
                                color: backgroundColor,
                                fontSize: 10.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (daysRemaining >= 0)
                            Text(
                              daysRemaining == 0
                                  ? "ينتهي اليوم"
                                  : "$daysRemaining أيام",
                              style: TextStyle(
                                color: daysRemaining <= 2
                                    ? Colors.red
                                    : AppColors.textSecondaryColor,
                                fontSize: 10.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                        ],
                      ),

                      SizedBox(height: 8.h),

                      // عنوان العرض
                      Text(
                        offer['title'] ?? '',
                        style: AppTextStyles.titleSmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      SizedBox(height: 5.h),

                      // اسم المتجر
                      Text(
                        offer['storeName'] ?? '',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: backgroundColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

                // سهم للتفاصيل
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.textSecondaryColor,
                  size: 16.sp,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_offer_outlined,
            size: 80.sp,
            color: AppColors.textSecondaryColor,
          ),
          SizedBox(height: 20.h),
          Text(
            searchQuery.isNotEmpty
                ? "لا توجد عروض تطابق البحث"
                : "لا توجد عروض متاحة",
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.textSecondaryColor,
            ),
          ),
          SizedBox(height: 10.h),
          Text(
            searchQuery.isNotEmpty
                ? "جرب كلمات بحث أخرى"
                : "تحقق مرة أخرى لاحقاً",
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  void _showOfferDetails(Map<String, dynamic> offer) {
    showDialog(
      context: context,
      builder: (context) => OfferDetailsDialog(offer: offer),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text("فلترة العروض"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text("اختر نوع العرض:"),
            SizedBox(height: 10),
            ...offerTypes.map((type) {
              return RadioListTile<String>(
                title: Text(type),
                value: type,
                groupValue: selectedType,
                onChanged: (value) {
                  setState(() {
                    selectedType = value!;
                  });
                  Navigator.pop(context);
                },
              );
            }).toList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text("إلغاء"),
          ),
        ],
      ),
    );
  }
}

// حوار تفاصيل العرض الكامل
class OfferDetailsDialog extends StatelessWidget {
  final Map<String, dynamic> offer;

  const OfferDetailsDialog({
    Key? key,
    required this.offer,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final offerType = offer['type'] ?? '';
    final backgroundColor =
        offer['backgroundColor'] ?? OffersManager.getOfferColor(offerType);
    final daysRemaining = OffersManager.getDaysRemaining(offer);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              backgroundColor.withOpacity(0.1),
              backgroundColor.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          textDirection: TextDirection.rtl,
          children: [
            // رأس الحوار
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              textDirection: TextDirection.rtl,
              children: [
                Row(
                  textDirection: TextDirection.rtl,
                  children: [
                    Icon(
                      offer['icon'] ?? OffersManager.getOfferIcon(offerType),
                      color: backgroundColor,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      OffersManager.getOfferTypeText(offerType),
                      style: TextStyle(
                        color: backgroundColor,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const SizedBox(height: 15),

            // عنوان العرض
            Text(
              offer['title'] ?? '',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
              textAlign: TextAlign.right,
            ),

            const SizedBox(height: 10),

            // وصف العرض
            Text(
              offer['description'] ?? '',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.right,
            ),

            const SizedBox(height: 15),

            // معلومات إضافية
            if (offer['discountPercentage'] != null)
              _buildInfoRow(
                Icons.local_offer,
                "نسبة الخصم",
                "${offer['discountPercentage']}%",
                backgroundColor,
              ),

            if (offer['minOrderAmount'] != null)
              _buildInfoRow(
                Icons.shopping_cart,
                "الحد الأدنى للطلب",
                "${offer['minOrderAmount']} ريال",
                backgroundColor,
              ),

            _buildInfoRow(
              Icons.store,
              "المتجر",
              offer['storeName'] ?? '',
              backgroundColor,
            ),

            if (daysRemaining >= 0)
              _buildInfoRow(
                Icons.access_time,
                "ينتهي خلال",
                daysRemaining == 0 ? "اليوم" : "$daysRemaining أيام",
                daysRemaining <= 2 ? Colors.red : backgroundColor,
              ),

            const SizedBox(height: 20),

            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // الانتقال إلى المتجر
                      _navigateToStore(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: backgroundColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      "زيارة المتجر",
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // مشاركة العرض
                      _shareOffer(context);
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: backgroundColor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      "مشاركة",
                      style: TextStyle(color: backgroundColor),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Row(
        textDirection: TextDirection.rtl,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 10),
          Text(
            "$label: ",
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToStore(BuildContext context) {
    // البحث عن المتجر في DataManager باستخدام storeId
    final storeId = offer['storeId'];
    if (storeId != null) {
      // البحث عن المتجر في قائمة المتاجر
      final stores = DataManager.allStores;
      final store = stores.firstWhere(
        (s) => s['id'] == storeId || s['name'] == offer['storeName'],
        orElse: () => {
          'id': storeId,
          'name': offer['storeName'] ?? 'متجر غير معروف',
          'category': 'عام',
          'image': offer['image'] ?? 'images/1.png',
          'rating': '4.5',
          'deliveryTime': '30 دقيقة',
          'description': 'متجر يقدم عروض رائعة',
        },
      );

      // الانتقال إلى صفحة تفاصيل المتجر
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StoreDetailsPage(store: store),
        ),
      );
    } else {
      // في حالة عدم وجود معرف المتجر، عرض رسالة
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("عذراً، لا يمكن الوصول إلى ${offer['storeName']}"),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _shareOffer(BuildContext context) {
    // مشاركة العرض
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("تم نسخ رابط العرض"),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
