# ميزة العروض والتخفيضات في تطبيق زاد اليمن
## Offers and Discounts Feature

## 🎯 نظرة عامة
تم إضافة قسم شامل للعروض والتخفيضات في الصفحة الرئيسية، يعرض جميع أنواع العروض من المتاجر المختلفة بتصميم جذاب وتفاعلي.

## ✅ الميزات المضافة

### 🏗️ **1. مدير العروض (OffersManager.dart)**

```dart
class OffersManager {
  // أنواع العروض المدعومة
  static const String DISCOUNT = 'discount';           // خصم
  static const String NEW_ITEM = 'new_item';          // منتج جديد
  static const String NEW_DISH = 'new_dish';          // طبق جديد
  static const String SPECIAL_OFFER = 'special_offer'; // عرض خاص
  static const String FREE_DELIVERY = 'free_delivery'; // توصيل مجاني
  static const String BUY_ONE_GET_ONE = 'bogo';       // اشتري واحد واحصل على آخر
}
```

**الوظائف الرئيسية:**
- ✅ **إدارة العروض** مع أنواع مختلفة
- ✅ **فلترة العروض** حسب النوع والمتجر
- ✅ **البحث في العروض** نصياً
- ✅ **التحقق من صلاحية العروض** والتواريخ
- ✅ **ترتيب العروض** حسب الأولوية
- ✅ **حساب الأيام المتبقية** للعروض

### 🎨 **2. مكون عرض العروض (OffersWidget.dart)**

#### **العرض الأفقي للعروض:**
```dart
class OffersWidget extends StatelessWidget {
  final String? searchQuery;

  // عرض أفقي للعروض مع حد أقصى 5 عروض
  Container(
    height: 180,
    child: ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: offers.length > 5 ? 5 : offers.length,
      itemBuilder: (context, index) {
        return OfferCard(offer: offers[index]);
      },
    ),
  ),
}
```

#### **بطاقة العرض (OfferCard):**
```dart
class OfferCard extends StatelessWidget {
  // تصميم متدرج جميل
  decoration: BoxDecoration(
    gradient: LinearGradient(
      colors: [backgroundColor, backgroundColor.withOpacity(0.8)],
    ),
    borderRadius: BorderRadius.circular(20),
    boxShadow: [BoxShadow(...)],
  ),
  
  // محتوى البطاقة:
  // - نوع العرض مع أيقونة
  // - الأيام المتبقية
  // - عنوان العرض
  // - وصف العرض
  // - اسم المتجر
  // - صورة العرض
}
```

### 📱 **3. صفحة العروض الكاملة (OffersPage.dart)**

```dart
class OffersPage extends StatefulWidget {
  // شريط بحث متقدم
  SearchWidget(
    hintText: "ابحث في العروض...",
    onSearchChanged: (query) => setState(() => searchQuery = query),
  ),
  
  // فلاتر أنواع العروض
  _buildTypeFilters(),
  
  // قائمة العروض العمودية
  _buildOffersList(),
}
```

**الميزات:**
- 🔍 **بحث متقدم** في العروض
- 🏷️ **فلترة حسب النوع** (خصم، منتج جديد، إلخ)
- 📋 **عرض تفصيلي** لكل عرض
- 📱 **تصميم متجاوب** وجميل

## 🎨 **أنواع العروض المدعومة**

### **1. خصم (DISCOUNT):**
```dart
{
  "title": "خصم 50% على جميع البرجر",
  "type": "discount",
  "discountPercentage": 50,
  "backgroundColor": Colors.red,
  "icon": Icons.local_offer,
}
```

### **2. طبق جديد (NEW_DISH):**
```dart
{
  "title": "طبق جديد: مندي الدجاج",
  "type": "new_dish",
  "backgroundColor": Colors.green,
  "icon": Icons.restaurant_menu,
}
```

### **3. توصيل مجاني (FREE_DELIVERY):**
```dart
{
  "title": "توصيل مجاني",
  "type": "free_delivery",
  "minOrderAmount": 100,
  "backgroundColor": Colors.blue,
  "icon": Icons.delivery_dining,
}
```

### **4. اشتري واحد واحصل على آخر (BUY_ONE_GET_ONE):**
```dart
{
  "title": "اشتري واحد واحصل على آخر مجاناً",
  "type": "bogo",
  "backgroundColor": Colors.orange,
  "icon": Icons.local_drink,
}
```

### **5. منتج جديد (NEW_ITEM):**
```dart
{
  "title": "منتج جديد: عسل طبيعي",
  "type": "new_item",
  "backgroundColor": Colors.amber,
  "icon": Icons.new_releases,
}
```

### **6. عرض خاص (SPECIAL_OFFER):**
```dart
{
  "title": "عرض خاص: خصم 30%",
  "type": "special_offer",
  "discountPercentage": 30,
  "backgroundColor": Colors.teal,
  "icon": Icons.medical_services,
}
```

## 🎯 **التكامل مع الصفحة الرئيسية**

### **الموقع في الصفحة:**
```dart
// في HomePages.dart
children: [
  // شريط البحث
  SearchWidget(...),
  
  // التصنيفات
  if (!isSearching) CategoriesWidget(...),
  
  // قسم العروض (جديد!)
  if (!isSearching) ...[
    _buildSectionTitle("العروض والتخفيضات"),
    OffersWidget(searchQuery: searchQuery),
  ],
  
  // المنتجات والمتاجر
  _buildSectionTitle("المطاعم الاكثر مبيعاً"),
  ItemsWidgets(...),
],
```

**الفوائد:**
- 📍 **موقع استراتيجي** بين التصنيفات والمنتجات
- 👁️ **رؤية عالية** للمستخدمين
- 🔄 **إخفاء عند البحث** لتجنب التشويش
- 🎯 **جذب الانتباه** للعروض الحالية

## 🎨 **التصميم والواجهة**

### **بطاقة العرض:**
```dart
// تدرج لوني جميل
gradient: LinearGradient(
  begin: Alignment.topLeft,
  end: Alignment.bottomRight,
  colors: [backgroundColor, backgroundColor.withOpacity(0.8)],
),

// ظلال متقدمة
boxShadow: [
  BoxShadow(
    color: backgroundColor.withOpacity(0.3),
    spreadRadius: 1,
    blurRadius: 8,
    offset: Offset(0, 4),
  ),
],
```

### **عناصر البطاقة:**
- 🏷️ **علامة نوع العرض** مع أيقونة
- ⏰ **عداد الأيام المتبقية** (أحمر إذا قارب الانتهاء)
- 📝 **عنوان العرض** بخط عريض
- 📄 **وصف مختصر** للعرض
- 🏪 **اسم المتجر** مع تمييز
- 🖼️ **صورة العرض** مع حواف منحنية

### **الألوان المميزة:**
- 🔴 **أحمر** للخصومات
- 🟢 **أخضر** للأطباق الجديدة
- 🔵 **أزرق** للتوصيل المجاني
- 🟠 **برتقالي** لعروض اشتري واحد واحصل على آخر
- 🟡 **أصفر** للمنتجات الجديدة
- 🟣 **بنفسجي** للعروض الخاصة

## 🔧 **الوظائف التفاعلية**

### **1. النقر على العرض:**
```dart
onTap: () => showDialog(
  context: context,
  builder: (context) => OfferDetailsDialog(offer: offer),
);
```

### **2. حوار تفاصيل العرض:**
```dart
class OfferDetailsDialog extends StatelessWidget {
  // عرض تفاصيل كاملة للعرض
  // - العنوان والوصف
  // - نسبة الخصم (إن وجدت)
  // - الحد الأدنى للطلب (إن وجد)
  // - اسم المتجر
  // - الأيام المتبقية
  // - أزرار الإجراءات (زيارة المتجر، مشاركة)
}
```

### **3. زر عرض المزيد:**
```dart
if (offers.length > 5)
  OutlinedButton(
    onPressed: () => Navigator.pushNamed(context, '/offers'),
    child: Text("عرض جميع العروض"),
  ),
```

## 📊 **إدارة البيانات**

### **بنية بيانات العرض:**
```dart
{
  "id": "offer_1",                    // معرف فريد
  "title": "خصم 50% على البرجر",      // عنوان العرض
  "description": "خصم كبير...",       // وصف مفصل
  "type": "discount",                 // نوع العرض
  "discountPercentage": 50,           // نسبة الخصم (اختياري)
  "storeName": "مطعم الشرق",          // اسم المتجر
  "storeId": "store_1",              // معرف المتجر
  "image": "images/1.png",           // صورة العرض
  "validUntil": "2024-12-31",        // تاريخ انتهاء الصلاحية
  "isActive": true,                  // حالة العرض
  "backgroundColor": Colors.red,      // لون الخلفية
  "textColor": Colors.white,         // لون النص
  "icon": Icons.local_offer,         // أيقونة العرض
}
```

### **الفلترة والبحث:**
```dart
// البحث النصي
static List<Map<String, dynamic>> searchOffers(String query) {
  return _offers.where((offer) {
    final title = offer['title']?.toString().toLowerCase() ?? '';
    final description = offer['description']?.toString().toLowerCase() ?? '';
    final storeName = offer['storeName']?.toString().toLowerCase() ?? '';
    
    return (title.contains(searchQuery) || 
            description.contains(searchQuery) ||
            storeName.contains(searchQuery)) &&
           offer['isActive'] == true;
  }).toList();
}

// فلترة متقدمة
static List<Map<String, dynamic>> filterOffers({
  String? type,
  String? storeId,
  bool? isActive,
  bool validOnly = true,
}) {
  // منطق الفلترة المتقدمة
}
```

## ⚡ **تحسينات الأداء**

### **1. التحميل الذكي:**
```dart
// عرض 5 عروض فقط في الصفحة الرئيسية
itemCount: offers.length > 5 ? 5 : offers.length,

// تحميل كامل في صفحة العروض المنفصلة
itemCount: offers.length,
```

### **2. التخزين المؤقت:**
```dart
// ترتيب العروض مرة واحدة
offers = OffersManager.sortOffersByPriority(offers);

// فلترة محسنة
static List<Map<String, dynamic>> getValidOffers() {
  return _offers.where((offer) => isOfferValid(offer)).toList();
}
```

### **3. مفاتيح فريدة:**
```dart
// لكل بطاقة عرض
key: ValueKey('offer_${offer["id"]}'),
```

## 🚀 **تجربة المستخدم**

### **في الصفحة الرئيسية:**
1. **تصفح العروض** أفقياً
2. **النقر على عرض** لرؤية التفاصيل
3. **النقر على "عرض المزيد"** للانتقال لصفحة العروض

### **في صفحة العروض:**
1. **البحث** في العروض نصياً
2. **فلترة** حسب نوع العرض
3. **عرض تفصيلي** لكل عرض
4. **التنقل** إلى المتجر أو مشاركة العرض

## 📱 **الاستجابة والتوافق**

### **التصميم المتجاوب:**
```dart
// استخدام ScreenUtil للأبعاد
width: 280.w,
height: 180.h,
padding: EdgeInsets.all(15.w),
fontSize: 16.sp,
```

### **دعم الاتجاهات:**
```dart
// دعم RTL للنصوص العربية
textDirection: TextDirection.rtl,
crossAxisAlignment: CrossAxisAlignment.start,
```

## 🎯 **النتائج المحققة**

### **قبل إضافة العروض:**
- 📱 **لا يوجد قسم للعروض** في التطبيق
- 🛍️ **صعوبة اكتشاف العروض** من المتاجر
- ⏰ **عدم معرفة العروض المحدودة** زمنياً
- 📊 **فقدان فرص تسويقية** للمتاجر

### **بعد إضافة العروض:**
- ✅ **قسم مخصص للعروض** في الصفحة الرئيسية
- ✅ **عرض جذاب ومنظم** لجميع العروض
- ✅ **تنبيهات للعروض المحدودة** زمنياً
- ✅ **زيادة التفاعل** مع المتاجر والمنتجات
- ✅ **تحسين تجربة التسوق** للمستخدمين

## 📊 **إحصائيات العروض**

### **العروض المتاحة:**
- 🎯 **6 عروض** متنوعة
- 🏪 **6 متاجر** مختلفة
- 🏷️ **6 أنواع** من العروض
- ⏰ **تواريخ انتهاء** متنوعة

### **أنواع العروض:**
- 🔴 **خصومات** (50%, 30%)
- 🟢 **أطباق جديدة** (مندي الدجاج)
- 🔵 **توصيل مجاني** (فوق 100 ريال)
- 🟠 **اشتري واحد واحصل على آخر**
- 🟡 **منتجات جديدة** (عسل طبيعي)
- 🟣 **عروض خاصة** (أدوية ومكملات)

## 🚀 **كيفية الاستخدام**

### **للمستخدم:**
```bash
1. افتح الصفحة الرئيسية
2. تصفح قسم "العروض والتخفيضات"
3. اضغط على أي عرض لرؤية التفاصيل
4. اضغط "عرض جميع العروض" للمزيد
5. ابحث وفلتر العروض حسب النوع
```

### **للمطور:**
```bash
cd test2
flutter run
```

## 🎉 **الخلاصة**

تطبيق "زاد اليمن" أصبح الآن يحتوي على:
- 🎯 **قسم عروض شامل** في الصفحة الرئيسية
- 🎨 **تصميم جذاب ومتدرج** للعروض
- 🔍 **بحث وفلترة متقدمة** للعروض
- ⏰ **تنبيهات للعروض المحدودة** زمنياً
- 📱 **صفحة منفصلة** لجميع العروض
- 🛍️ **تجربة تسوق محسنة** للمستخدمين

🎉 **ميزة العروض والتخفيضات جاهزة وتعمل بكفاءة عالية!**

الآن يمكن للمستخدمين اكتشاف جميع العروض والتخفيضات من المتاجر المختلفة بسهولة، مع إمكانية البحث والفلترة والحصول على تفاصيل كاملة لكل عرض.
