# تطبيق التبويبات في الصفحة الرئيسية
## Tabs Implementation in Home Page

## 🎯 **نظرة عامة**
تم استبدال عنوان "المطاعم الأكثر مبيعاً" بتبويبات تفاعلية تحتوي على: "الكل"، "الأقرب"، "الجديد"، و"المفضلة".

## ✨ **التبويبات الجديدة**

### **📑 التبويبات المتاحة:**
1. **🌟 الكل:** جميع المنتجات والمتاجر
2. **📍 الأقرب:** المتاجر والمنتجات الأقرب للمستخدم
3. **🆕 الجديد:** المنتجات والمتاجر الجديدة
4. **❤️ المفضلة:** المنتجات والمتاجر المفضلة

### **🎨 التصميم:**
- **شكل:** أزرار دائرية مع حدود
- **اللون المختار:** أحمر (#C3243B) مع نص أبيض
- **اللون غير المختار:** شفاف مع حدود رمادية ونص أسود
- **الانتقال:** متدرج وسلس (200ms)

## 🔧 **التطبيق التقني**

### **1. متغيرات التحكم:**
```dart
class _HomePageState extends State<HomePage> {
  // متغيرات التبويبات الجديدة
  String selectedTab = "الكل"; // التبويب المختار
  final List<String> tabs = ["الكل", "الأقرب", "الجديد", "المفضلة"];
  
  // متغيرات العناوين والتبويبات
  double _offersTitleOpacity = 1.0; // شفافية عنوان العروض
  double _tabsOpacity = 0.0; // شفافية التبويبات
  bool _showTabs = false; // إظهار التبويبات في المكان الثابت
}
```

### **2. دالة بناء التبويبات:**
```dart
Widget _buildTabsWidget() {
  return Container(
    margin: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      textDirection: TextDirection.rtl,
      children: tabs.map((tab) {
        final isSelected = selectedTab == tab;
        return GestureDetector(
          onTap: () {
            setState(() {
              selectedTab = tab;
            });
          },
          child: AnimatedContainer(
            duration: Duration(milliseconds: 200),
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            decoration: BoxDecoration(
              color: isSelected ? Color(0xFFC3243B) : Colors.transparent,
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: isSelected ? Color(0xFFC3243B) : Colors.grey.shade400,
                width: 1.5,
              ),
            ),
            child: Text(
              tab,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black,
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    ),
  );
}
```

### **3. التبويبات المتداخلة:**
```dart
Stack(
  children: [
    // عنوان العروض مع شفافية متدرجة
    AnimatedOpacity(
      duration: Duration(milliseconds: 150),
      opacity: _offersTitleOpacity,
      child: _buildSectionTitle("العروض والتخفيضات"),
    ),

    // التبويبات في نفس المكان (تظهر عندما تختفي العروض)
    AnimatedOpacity(
      duration: Duration(milliseconds: 150),
      opacity: _tabsOpacity,
      child: Container(
        color: Color(0xFFEDECF2), // نفس لون الخلفية
        child: _buildTabsWidget(),
      ),
    ),
  ],
),
```

### **4. التبويبات في الأسفل:**
```dart
// التبويبات (تختفي عندما تظهر في الأعلى)
if (!isSearching)
  AnimatedOpacity(
    duration: Duration(milliseconds: 150),
    opacity: _showTabs ? 0.0 : 1.0,
    child: _buildTabsWidget(),
  ),
```

## 🔄 **منطق الفلترة**

### **1. تمرير التبويب المختار:**
```dart
ItemsWidgets(
  selectedCategory: isSearching ? "الكل" : selectedCategory,
  searchQuery: searchQuery,
  selectedTab: isSearching ? "الكل" : selectedTab,
),
```

### **2. فلترة البيانات في ItemsWidgets:**
```dart
switch (selectedTab) {
  case "الكل":
    filteredItems = items;
    filteredStores = stores;
    break;
  case "الأقرب":
    // فلترة المتاجر الأقرب
    filteredItems = items.take(6).toList();
    filteredStores = stores.take(3).toList();
    break;
  case "الجديد":
    // فلترة المنتجات والمتاجر الجديدة
    filteredItems = items.where((item) => 
      item['id'] == '1' || item['id'] == '2' || item['id'] == '3'
    ).toList();
    filteredStores = stores.take(2).toList();
    break;
  case "المفضلة":
    // فلترة المفضلة
    filteredItems = items.where((item) => 
      item['id'] == '1' || item['id'] == '4' || item['id'] == '7'
    ).toList();
    filteredStores = stores.where((store) => 
      store['name'] == 'مطعم الشرق' || store['name'] == 'سوبرماركت الأمانة'
    ).toList();
    break;
}
```

## 📊 **محتوى كل تبويب**

### **🌟 تبويب "الكل":**
- **المنتجات:** جميع المنتجات حسب التصنيف المختار
- **المتاجر:** جميع المتاجر المتاحة
- **العدد:** غير محدود

### **📍 تبويب "الأقرب":**
- **المنتجات:** أول 6 منتجات (محاكاة للأقرب)
- **المتاجر:** أول 3 متاجر (محاكاة للأقرب)
- **ملاحظة:** يمكن تطويرها لاحقاً بناءً على الموقع الجغرافي

### **🆕 تبويب "الجديد":**
- **المنتجات:** المنتجات ذات ID: 1, 2, 3
- **المتاجر:** أول متجرين
- **المعيار:** المنتجات والمتاجر المضافة حديثاً

### **❤️ تبويب "المفضلة":**
- **المنتجات:** المنتجات ذات ID: 1, 4, 7
- **المتاجر:** مطعم الشرق، سوبرماركت الأمانة
- **ملاحظة:** يمكن ربطها بقاعدة بيانات المفضلة لاحقاً

## 🎮 **سلوك التفاعل**

### **📱 عند التمرير:**
1. **البداية:** التبويبات ظاهرة في الأسفل
2. **التمرير لأعلى:** التبويبات تظهر في الأعلى تدريجياً
3. **نقطة التحول:** التبويبات تثبت في الأعلى
4. **التبويبات السفلية:** تختفي عندما تظهر في الأعلى

### **🔍 عند البحث:**
```dart
if (isSearching) {
  _tabsOpacity = 1.0;
  _showTabs = true;
  selectedTab = "الكل"; // إعادة تعيين للكل
}
```

### **🏷️ عند تغيير التصنيف:**
```dart
_tabsOpacity = 0.0;
_showTabs = false;
// التبويبات تعود للأسفل
```

### **📑 عند تغيير التبويب:**
```dart
setState(() {
  selectedTab = tab; // تحديث التبويب المختار
});
// إعادة فلترة المحتوى تلقائياً
```

## 🎨 **التصميم البصري**

### **الألوان:**
- **المختار:** `Color(0xFFC3243B)` (أحمر التطبيق)
- **غير المختار:** `Colors.transparent` (شفاف)
- **الحدود:** `Colors.grey.shade400` (رمادي فاتح)

### **الخط:**
- **المختار:** أبيض، عريض
- **غير المختار:** أسود، عادي
- **الحجم:** 14px

### **الانتقالات:**
- **المدة:** 200ms للتبويبات، 150ms للشفافية
- **المنحنى:** افتراضي (سلس)

## 🔄 **إدارة الحالات**

### **الحالات المختلفة:**
1. **الوضع العادي:** التبويبات في الأسفل
2. **بعد التمرير:** التبويبات في الأعلى
3. **وضع البحث:** التبويبات في الأعلى، "الكل" مختار
4. **تغيير التصنيف:** التبويبات تعود للأسفل

### **إعادة التعيين:**
```dart
// عند تغيير التصنيف أو إلغاء البحث
_tabsOpacity = 0.0;
_showTabs = false;
selectedTab = "الكل"; // اختياري
```

## 🚀 **التحسينات المستقبلية**

### **1. تبويب "الأقرب":**
- ربط بـ GPS للموقع الحقيقي
- حساب المسافة الفعلية
- ترتيب حسب القرب

### **2. تبويب "الجديد":**
- ربط بتاريخ الإضافة
- فلترة حسب آخر 7 أيام
- تحديث تلقائي

### **3. تبويب "المفضلة":**
- ربط بقاعدة بيانات المستخدم
- حفظ المفضلة محلياً
- مزامنة مع الخادم

### **4. تحسينات عامة:**
- إضافة أيقونات للتبويبات
- تأثيرات بصرية إضافية
- إعدادات قابلة للتخصيص

## 🎯 **النتائج المحققة**

### **قبل التحديث:**
- ❌ **عنوان ثابت:** "المطاعم الأكثر مبيعاً"
- ❌ **محتوى واحد:** نفس المنتجات دائماً
- ❌ **عدم تفاعل:** لا توجد خيارات فلترة

### **بعد التحديث:**
- ✅ **تبويبات تفاعلية:** 4 خيارات مختلفة
- ✅ **محتوى متنوع:** فلترة حسب التبويب
- ✅ **تجربة غنية:** خيارات متعددة للمستخدم
- ✅ **تصميم جذاب:** انتقالات سلسة ومتدرجة

## 🎉 **الخلاصة**

تطبيق "زاد اليمن" أصبح الآن يوفر:
- ✅ **تبويبات تفاعلية** بدلاً من العنوان الثابت
- ✅ **فلترة ذكية** للمحتوى حسب التبويب
- ✅ **تجربة مستخدم متقدمة** مع خيارات متنوعة
- ✅ **تصميم متسق** مع باقي التطبيق
- ✅ **انتقالات سلسة** بين التبويبات والحالات

🎯 **التبويبات جاهزة وتعمل بكفاءة عالية!**

الآن يمكن للمستخدمين تصفح المحتوى بطرق مختلفة: الكل، الأقرب، الجديد، والمفضلة، مما يوفر تجربة تصفح أكثر تنوعاً وتخصيصاً.
