import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/DataManager.dart';
import '../utils/SearchManager.dart';
import '../widgets/OptimizedImage.dart';
import '../pages/StoreDetailsPage.dart';
import '../providers/CartProvider.dart';
import '../models/Product.dart';
import '../widgets/CustomSnackBars.dart';

class ItemsWidgets extends StatelessWidget {
  final String selectedCategory;
  final String? searchQuery;
  final String? selectedTab;

  const ItemsWidgets({
    Key? key,
    required this.selectedCategory,
    this.searchQuery,
    this.selectedTab,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // استخدام SearchManager أو DataManager حسب وجود البحث
    List<Map<String, dynamic>> allResults = [];

    if (searchQuery != null && searchQuery!.isNotEmpty) {
      // البحث العام - الحصول على المنتجات والمتاجر
      final searchResults = SearchManager.searchGeneral(searchQuery!);
      final items = searchResults['items'] ?? [];
      final stores = searchResults['stores'] ?? [];

      // دمج المنتجات والمتاجر مع إضافة نوع لكل عنصر
      allResults = [
        ...items.map((item) => {...item, 'type': 'item'}),
        ...stores.map((store) => {...store, 'type': 'store'}),
      ];
    } else {
      // العرض العادي - المنتجات والمتاجر حسب التبويب
      final items = DataManager.getFilteredItems(selectedCategory);
      final stores = DataManager.allStores;

      // فلترة حسب التبويب المختار
      List<Map<String, dynamic>> filteredItems = [];
      List<Map<String, dynamic>> filteredStores = [];

      switch (selectedTab) {
        case "الكل":
          filteredItems = items;
          filteredStores = stores;
          break;
        case "الأقرب":
          // فلترة المتاجر الأقرب (يمكن تطويرها لاحقاً بناءً على الموقع)
          filteredItems = items.take(6).toList();
          filteredStores = stores.take(3).toList();
          break;
        case "الجديد":
          // فلترة المنتجات والمتاجر الجديدة
          filteredItems = items
              .where((item) =>
                  item['id'] == '1' || item['id'] == '2' || item['id'] == '3')
              .toList();
          filteredStores = stores.take(2).toList();
          break;
        case "المفضلة":
          // فلترة المفضلة (يمكن ربطها بقاعدة بيانات المفضلة لاحقاً)
          filteredItems = items
              .where((item) =>
                  item['id'] == '1' || item['id'] == '4' || item['id'] == '7')
              .toList();
          filteredStores = stores
              .where((store) =>
                  store['name'] == 'مطعم الشرق' ||
                  store['name'] == 'سوبرماركت الأمانة')
              .toList();
          break;
        default:
          filteredItems = items;
          filteredStores = stores;
      }

      // دمج المنتجات والمتاجر
      allResults = [
        ...filteredItems.map((item) => {...item, 'type': 'item'}),
        ...filteredStores.map((store) => {...store, 'type': 'store'}),
      ];
    }

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.85,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: allResults.length,
      itemBuilder: (context, index) {
        final item = allResults[index];
        final isStore = item['type'] == 'store';

        return isStore
            ? StoreCard(
                key: ValueKey('store_${item["name"]}'),
                store: item,
              )
            : ItemCard(
                key: ValueKey('item_${item["id"]}'),
                item: item,
              );
      },
    );
  }
}

// مكون منفصل للبطاقة لتحسين الأداء
class ItemCard extends StatelessWidget {
  final Map<String, dynamic> item;

  const ItemCard({
    Key? key,
    required this.item,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 15, right: 15, top: 10),
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          // الصف العلوي - الخصم والمفضلة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: const Color.fromARGB(255, 245, 170, 73),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Text(
                  "-50%",
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Icon(
                Icons.favorite_border,
                color: Colors.red,
              )
            ],
          ),

          // صورة المنتج
          Expanded(
            child: InkWell(
              onTap: () {
                Navigator.pushNamed(
                  context,
                  "itemsPage",
                  arguments: {'id': item["id"], 'name': item["name"]},
                );
              },
              child: Container(
                margin: const EdgeInsets.all(10),
                child: OptimizedImage(
                  imagePath: "images/${item["id"]}.png",
                  width: 50,
                  height: 50,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),

          // معلومات المنتج
          Container(
            padding: const EdgeInsets.only(bottom: 8),
            alignment: Alignment.centerRight,
            child: Text(
              item["name"],
              style: const TextStyle(
                fontSize: 15,
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          Container(
            alignment: Alignment.centerRight,
            child: Text(
              item["description"],
              style: const TextStyle(fontSize: 11, color: Colors.black),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // السعر وزر الإضافة
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "${item["price"]} ر.ي",
                  style: const TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                Consumer<CartProvider>(
                  builder: (context, cartProvider, child) {
                    final productId = item["id"]?.toString() ?? '';
                    final isInCart = cartProvider.isProductInCart(productId);
                    final quantity = cartProvider.getProductQuantity(productId);

                    return GestureDetector(
                      onTap: () => _addToCart(context, item, cartProvider),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: isInCart
                              ? Colors.green
                              : const Color.fromARGB(255, 245, 170, 73),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              isInCart
                                  ? Icons.check
                                  : Icons.shopping_cart_checkout,
                              color: Colors.white,
                              size: 12,
                            ),
                            if (isInCart && quantity > 0) ...[
                              const SizedBox(width: 4),
                              Text(
                                quantity.toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  /// إضافة المنتج إلى السلة
  void _addToCart(BuildContext context, Map<String, dynamic> item,
      CartProvider cartProvider) {
    try {
      // تحويل البيانات إلى Product مع إضافة الحقول المفقودة
      final productData = {
        'id': item['id']?.toString() ?? '',
        'name': item['name']?.toString() ?? '',
        'description': item['description']?.toString() ?? '',
        'price': _parsePrice(item['price']),
        'imageUrl': 'images/${item['id']}.png',
        'category': item['category']?.toString() ?? '',
        'rating': 4.5,
        'reviewCount': 10,
        'isAvailable': true,
        'isFeatured': false,
      };

      final product = Product.fromMap(productData);

      // إضافة المنتج إلى السلة
      cartProvider.addToCart(product).then((success) {
        if (success) {
          // عرض رسالة نجاح
          CustomSnackBars.showSuccess(
            context,
            message: 'تم إضافة ${product.name} إلى السلة',
            subtitle: 'يمكنك مراجعة سلتك من الأسفل',
          );
        } else {
          // عرض رسالة خطأ
          CustomSnackBars.showError(
            context,
            message: 'فشل في إضافة المنتج',
            subtitle: 'يرجى المحاولة مرة أخرى',
          );
        }
      });
    } catch (e) {
      // عرض رسالة خطأ
      CustomSnackBars.showError(
        context,
        message: 'خطأ في إضافة المنتج',
        subtitle: e.toString(),
      );
    }
  }

  /// تحويل السعر من String إلى double
  double _parsePrice(dynamic price) {
    if (price == null) return 0.0;
    if (price is double) return price;
    if (price is int) return price.toDouble();
    if (price is String) {
      return double.tryParse(price) ?? 0.0;
    }
    return 0.0;
  }
}

// مكون منفصل لبطاقة المتجر
class StoreCard extends StatelessWidget {
  final Map<String, dynamic> store;

  const StoreCard({
    Key? key,
    required this.store,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => StoreDetailsPage(store: store),
            ),
          );
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.only(left: 15, right: 15, top: 10),
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: const Color(0xFF4C53A5).withOpacity(0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            children: [
              // الصف العلوي - نوع المتجر وحالة المحل والمفضلة
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      // نوع المتجر
                      Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          color: const Color(0xFF4C53A5),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Text(
                          "متجر",
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                      const SizedBox(width: 8),

                      // حالة المحل (مفتوح/مغلق)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: (store["isOpen"] ?? false)
                              ? Colors.green // أخضر للمفتوح
                              : Colors.red, // أحمر للمغلق
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: Text(
                          (store["isOpen"] ?? false) ? "مفتوح" : "مغلق",
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.white, // نص أبيض
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const Icon(
                    Icons.favorite_border,
                    color: Colors.red,
                  )
                ],
              ),

              // صورة المتجر
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(10),
                  child: OptimizedImage(
                    imagePath: store["image"] ?? "images/1.png",
                    width: 50,
                    height: 50,
                    fit: BoxFit.contain,
                  ),
                ),
              ),

              // معلومات المتجر
              Container(
                padding: const EdgeInsets.only(bottom: 8),
                alignment: Alignment.centerRight,
                child: Text(
                  store["name"],
                  style: const TextStyle(
                    fontSize: 15,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              Container(
                alignment: Alignment.centerRight,
                child: Text(
                  store["category"] ?? "",
                  style: const TextStyle(fontSize: 11, color: Colors.black),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // التقييم ووقت التوصيل
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.star,
                          color: Colors.orange,
                          size: 12,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          "${store["rating"] ?? "4.5"}",
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      store["deliveryTime"] ?? "30 دقيقة",
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
