import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/UserProfileProvider.dart';
import '../models/Address.dart';
import '../widgets/CustomSnackBars.dart';

class AddressManagementPage extends StatefulWidget {
  @override
  _AddressManagementPageState createState() => _AddressManagementPageState();
}

class _AddressManagementPageState extends State<AddressManagementPage> {
  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  void _loadAddresses() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<UserProfileProvider>(context, listen: false).loadUserProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: AppBar(
        backgroundColor: Color(0xFF4C53A5),
        elevation: 0,
        centerTitle: true,
        title: Text(
          'إدارة العناوين',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Consumer<UserProfileProvider>(
        builder: (context, userProvider, child) {
          if (userProvider.isLoading) {
            return Center(
              child: CircularProgressIndicator(
                color: Color(0xFF4C53A5),
              ),
            );
          }

          final addresses = userProvider.addresses;

          if (addresses.isEmpty) {
            return _buildEmptyState();
          }

          return ListView.builder(
            padding: EdgeInsets.all(16),
            itemCount: addresses.length,
            itemBuilder: (context, index) {
              final address = addresses[index];
              return _buildAddressCard(address, userProvider);
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddAddressDialog(),
        backgroundColor: Color(0xFF4C53A5),
        child: Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 80,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد عناوين محفوظة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'أضف عنوان جديد للبدء',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          SizedBox(height: 20),
          ElevatedButton(
            onPressed: () => _showAddAddressDialog(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFF4C53A5),
              padding: EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            ),
            child: Text(
              'إضافة عنوان',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressCard(Address address, UserProfileProvider userProvider) {
    final isDefault = address.isDefault;

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: isDefault 
            ? Border.all(color: Color(0xFF4C53A5), width: 2)
            : null,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  AddressType.getIcon(address.title),
                  style: TextStyle(fontSize: 20),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    address.title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF4C53A5),
                    ),
                  ),
                ),
                if (isDefault)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Color(0xFF4C53A5),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'افتراضي',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleAddressAction(value, address, userProvider),
                  itemBuilder: (context) => [
                    if (!isDefault)
                      PopupMenuItem(
                        value: 'default',
                        child: Row(
                          children: [
                            Icon(Icons.star, size: 20),
                            SizedBox(width: 8),
                            Text('جعل افتراضي'),
                          ],
                        ),
                      ),
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 20),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 20, color: Colors.red),
                          SizedBox(width: 8),
                          Text('حذف', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 12),
            Text(
              address.formattedAddress,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
            ),
            if (address.landmark != null && address.landmark!.isNotEmpty) ...[
              SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.place, size: 16, color: Colors.grey[500]),
                  SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      'علامة مميزة: ${address.landmark}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _handleAddressAction(String action, Address address, UserProfileProvider userProvider) {
    switch (action) {
      case 'default':
        _setDefaultAddress(address.id, userProvider);
        break;
      case 'edit':
        _showEditAddressDialog(address);
        break;
      case 'delete':
        _showDeleteConfirmation(address, userProvider);
        break;
    }
  }

  void _setDefaultAddress(String addressId, UserProfileProvider userProvider) {
    userProvider.setDefaultAddress(addressId).then((success) {
      if (success) {
        CustomSnackBars.showSuccess(
          context,
          message: 'تم تعيين العنوان كافتراضي',
          subtitle: 'سيتم استخدام هذا العنوان في الطلبات',
        );
      } else {
        CustomSnackBars.showError(
          context,
          message: 'فشل في تعيين العنوان',
          subtitle: 'يرجى المحاولة مرة أخرى',
        );
      }
    });
  }

  void _showDeleteConfirmation(Address address, UserProfileProvider userProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف العنوان'),
        content: Text('هل أنت متأكد من حذف عنوان "${address.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteAddress(address.id, userProvider);
            },
            child: Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _deleteAddress(String addressId, UserProfileProvider userProvider) {
    userProvider.removeAddress(addressId).then((success) {
      if (success) {
        CustomSnackBars.showSuccess(
          context,
          message: 'تم حذف العنوان بنجاح',
          subtitle: 'العنوان لم يعد متاحاً',
        );
      } else {
        CustomSnackBars.showError(
          context,
          message: 'فشل في حذف العنوان',
          subtitle: 'يرجى المحاولة مرة أخرى',
        );
      }
    });
  }

  void _showAddAddressDialog() {
    // TODO: إضافة حوار إضافة عنوان جديد
    CustomSnackBars.showInfo(
      context,
      message: 'قريباً',
      subtitle: 'سيتم إضافة صفحة إضافة العناوين قريباً',
    );
  }

  void _showEditAddressDialog(Address address) {
    // TODO: إضافة حوار تعديل العنوان
    CustomSnackBars.showInfo(
      context,
      message: 'قريباً',
      subtitle: 'سيتم إضافة صفحة تعديل العناوين قريباً',
    );
  }
}
