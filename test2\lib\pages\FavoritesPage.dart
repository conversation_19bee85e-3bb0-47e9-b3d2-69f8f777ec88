import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:test2/utils/AppColors.dart';
import 'package:test2/widgets/CustomBottomNavBar.dart';
import 'package:test2/widgets/SearchWidget.dart';
import 'package:test2/utils/SearchManager.dart';

class FavoritesPage extends StatefulWidget {
  @override
  _FavoritesPageState createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage> {
  List<Map<String, dynamic>> favoriteStores = [
    {
      "id": "1",
      "name": "مطعم الشرق",
      "category": "مطاعم",
      "rating": 4.8,
      "deliveryTime": "30-45 دقيقة",
      "image": "images/1.png",
      "description": "أشهى الأطباق الشرقية الأصيلة",
      "isFavorite": true,
    },
    {
      "id": "2",
      "name": "سوبرماركت الأمانة",
      "category": "بقالة",
      "rating": 4.5,
      "deliveryTime": "20-30 دقيقة",
      "image": "images/2.png",
      "description": "جميع احتياجاتك اليومية",
      "isFavorite": true,
    },
    {
      "id": "3",
      "name": "صيدلية الصحة",
      "category": "صيدليات",
      "rating": 4.7,
      "deliveryTime": "15-25 دقيقة",
      "image": "images/3.png",
      "description": "أدوية وعناية صحية شاملة",
      "isFavorite": true,
    },
  ];

  List<Map<String, dynamic>> favoriteItems = [
    {
      "id": "1",
      "name": "برجر لحم مشوي",
      "price": 25.0,
      "image": "images/4.png",
      "restaurant": "مطعم الشرق",
      "description": "برجر لحم طازج مع الخضار",
      "isFavorite": true,
    },
    {
      "id": "2",
      "name": "بيتزا مارجريتا",
      "price": 35.0,
      "image": "images/5.png",
      "restaurant": "مطعم الشرق",
      "description": "بيتزا كلاسيكية بالجبن والطماطم",
      "isFavorite": true,
    },
  ];

  int selectedTab = 0; // 0 للمتاجر، 1 للأطباق
  String searchQuery = ""; // نص البحث

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // تبويبات
          _buildTabs(),

          // المحتوى
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.backgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppDimensions.borderRadiusXLarge),
                  topRight: Radius.circular(AppDimensions.borderRadiusXLarge),
                ),
              ),
              child: Column(
                children: [
                  // شريط البحث
                  Padding(
                    padding: EdgeInsets.all(15.w),
                    child: SearchWidget(
                      hintText: selectedTab == 0
                          ? "البحث في المتاجر المفضلة..."
                          : "البحث في الأطباق المفضلة...",
                      onSearchChanged: (query) {
                        setState(() {
                          searchQuery = query;
                        });
                      },
                      suffixIcon: searchQuery.isNotEmpty ? Icons.clear : null,
                    ),
                  ),

                  // المحتوى المفلتر
                  Expanded(
                    child: selectedTab == 0
                        ? _buildStoresList()
                        : _buildItemsList(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: FlatBottomNavBar(
        currentIndex: 0, // المفضلة
        onTap: (index) {
          NavigationHelper.navigateToPage(context, index);
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryColor,
      elevation: 0,
      centerTitle: true,
      title: Text(
        "المفضلة",
        style: AppTextStyles.appBarTitle,
      ),
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.whiteColor,
        ),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
      actions: [
        IconButton(
          icon: Icon(
            Icons.search,
            color: AppColors.whiteColor,
          ),
          onPressed: () {
            // فتح البحث في المفضلة
          },
        ),
      ],
    );
  }

  Widget _buildTabs() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: AppDimensions.marginLarge),
      child: Row(
        children: [
          Expanded(
            child: _buildTabButton(
              title: "المتاجر",
              isSelected: selectedTab == 0,
              onTap: () => setState(() => selectedTab = 0),
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            child: _buildTabButton(
              title: "الأطباق",
              isSelected: selectedTab == 1,
              onTap: () => setState(() => selectedTab = 1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton({
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.whiteColor : AppColors.transparentColor,
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusSmall),
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: isSelected ? AppColors.primaryColor : AppColors.whiteColor,
          ),
        ),
      ),
    );
  }

  Widget _buildStoresList() {
    // فلترة المتاجر حسب البحث
    final filteredStores =
        SearchManager.searchFavorites(searchQuery, favoriteStores);

    if (filteredStores.isEmpty) {
      return _buildEmptyState(
          searchQuery.isNotEmpty
              ? "لا توجد نتائج للبحث"
              : "لا توجد متاجر مفضلة",
          searchQuery.isNotEmpty
              ? "جرب كلمات بحث أخرى"
              : "ابدأ بإضافة متاجرك المفضلة");
    }

    return ListView.builder(
      padding: EdgeInsets.all(AppDimensions.paddingMedium),
      itemCount: filteredStores.length,
      itemBuilder: (context, index) {
        return _buildStoreCard(filteredStores[index]);
      },
    );
  }

  Widget _buildItemsList() {
    // فلترة الأطباق حسب البحث
    final filteredItems =
        SearchManager.searchFavorites(searchQuery, favoriteItems);

    if (filteredItems.isEmpty) {
      return _buildEmptyState(
          searchQuery.isNotEmpty
              ? "لا توجد نتائج للبحث"
              : "لا توجد أطباق مفضلة",
          searchQuery.isNotEmpty
              ? "جرب كلمات بحث أخرى"
              : "ابدأ بإضافة أطباقك المفضلة");
    }

    return ListView.builder(
      padding: EdgeInsets.all(AppDimensions.paddingMedium),
      itemCount: filteredItems.length,
      itemBuilder: (context, index) {
        return _buildItemCard(filteredItems[index]);
      },
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 80.sp,
            color: AppColors.textSecondaryColor,
          ),
          SizedBox(height: 20.h),
          Text(
            title,
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.textSecondaryColor,
            ),
          ),
          SizedBox(height: 10.h),
          Text(
            subtitle,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStoreCard(Map<String, dynamic> store) {
    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.marginMedium),
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 8,
            spreadRadius: 1,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingMedium),
        child: Row(
          textDirection: TextDirection.rtl,
          children: [
            // صورة المتجر
            Container(
              width: 80.w,
              height: 80.h,
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(AppDimensions.borderRadiusSmall),
                image: DecorationImage(
                  image: AssetImage(store["image"]),
                  fit: BoxFit.cover,
                ),
              ),
            ),

            SizedBox(width: 15.w),

            // معلومات المتجر
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                textDirection: TextDirection.rtl,
                children: [
                  Text(
                    store["name"],
                    style: AppTextStyles.titleSmall,
                  ),
                  SizedBox(height: 5.h),
                  Text(
                    store["description"],
                    style: AppTextStyles.bodySmall,
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    textDirection: TextDirection.rtl,
                    children: [
                      Icon(Icons.star, color: Colors.amber, size: 16.sp),
                      SizedBox(width: 5.w),
                      Text(
                        "${store["rating"]}",
                        style: AppTextStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(width: 15.w),
                      Icon(Icons.access_time,
                          color: AppColors.textSecondaryColor, size: 16.sp),
                      SizedBox(width: 5.w),
                      Text(
                        store["deliveryTime"],
                        style: AppTextStyles.bodySmall,
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // زر إزالة من المفضلة
            IconButton(
              onPressed: () => _removeFromFavorites(store["id"], true),
              icon: Icon(
                Icons.favorite,
                color: AppColors.primaryColor,
                size: 24.sp,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemCard(Map<String, dynamic> item) {
    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.marginMedium),
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 8,
            spreadRadius: 1,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingMedium),
        child: Row(
          textDirection: TextDirection.rtl,
          children: [
            // صورة الطبق
            Container(
              width: 80.w,
              height: 80.h,
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(AppDimensions.borderRadiusSmall),
                image: DecorationImage(
                  image: AssetImage(item["image"]),
                  fit: BoxFit.cover,
                ),
              ),
            ),

            SizedBox(width: 15.w),

            // معلومات الطبق
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                textDirection: TextDirection.rtl,
                children: [
                  Text(
                    item["name"],
                    style: AppTextStyles.titleSmall,
                  ),
                  SizedBox(height: 5.h),
                  Text(
                    item["restaurant"],
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.primaryColor,
                    ),
                  ),
                  SizedBox(height: 5.h),
                  Text(
                    item["description"],
                    style: AppTextStyles.bodySmall,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    "${item["price"]} ر.ي",
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ],
              ),
            ),

            // أزرار الإجراءات
            Column(
              children: [
                IconButton(
                  onPressed: () => _removeFromFavorites(item["id"], false),
                  icon: Icon(
                    Icons.favorite,
                    color: AppColors.primaryColor,
                    size: 24.sp,
                  ),
                ),
                SizedBox(height: 5.h),
                ElevatedButton(
                  onPressed: () => _addToCart(item),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    minimumSize: Size(60.w, 30.h),
                    padding: EdgeInsets.symmetric(horizontal: 8.w),
                  ),
                  child: Text(
                    "إضافة",
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.whiteColor,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _removeFromFavorites(String id, bool isStore) {
    setState(() {
      if (isStore) {
        favoriteStores.removeWhere((store) => store["id"] == id);
      } else {
        favoriteItems.removeWhere((item) => item["id"] == id);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text("تم إزالة العنصر من المفضلة"),
        backgroundColor: AppColors.successColor,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _addToCart(Map<String, dynamic> item) {
    // إضافة إلى السلة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text("تم إضافة ${item["name"]} إلى السلة"),
        backgroundColor: AppColors.successColor,
        duration: Duration(seconds: 2),
      ),
    );
  }
}
