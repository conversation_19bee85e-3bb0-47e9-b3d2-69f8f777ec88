import 'package:flutter/material.dart';

// إدارة حالة التطبيق البسيطة
class AppState extends ChangeNotifier {
  // حالة المستخدم
  bool _isLoggedIn = false;
  Map<String, dynamic>? _userData;
  
  // حالة السلة
  List<Map<String, dynamic>> _cartItems = [];
  double _cartTotal = 0.0;
  
  // حالة الطلبات
  List<Map<String, dynamic>> _orders = [];
  
  // حالة الإعدادات
  bool _notificationsEnabled = true;
  bool _locationEnabled = true;
  bool _darkModeEnabled = false;
  String _selectedLanguage = 'العربية';
  
  // حالة التطبيق العامة
  bool _isLoading = false;
  String? _errorMessage;
  
  // Getters
  bool get isLoggedIn => _isLoggedIn;
  Map<String, dynamic>? get userData => _userData;
  List<Map<String, dynamic>> get cartItems => _cartItems;
  double get cartTotal => _cartTotal;
  List<Map<String, dynamic>> get orders => _orders;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get locationEnabled => _locationEnabled;
  bool get darkModeEnabled => _darkModeEnabled;
  String get selectedLanguage => _selectedLanguage;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  int get cartItemsCount => _cartItems.length;
  
  // إدارة المستخدم
  void login(Map<String, dynamic> userData) {
    _isLoggedIn = true;
    _userData = userData;
    notifyListeners();
  }
  
  void logout() {
    _isLoggedIn = false;
    _userData = null;
    _cartItems.clear();
    _cartTotal = 0.0;
    notifyListeners();
  }
  
  void updateUserData(Map<String, dynamic> userData) {
    _userData = userData;
    notifyListeners();
  }
  
  // إدارة السلة
  void addToCart(Map<String, dynamic> item) {
    // البحث عن العنصر في السلة
    int existingIndex = _cartItems.indexWhere(
      (cartItem) => cartItem['id'] == item['id']
    );
    
    if (existingIndex >= 0) {
      // زيادة الكمية إذا كان العنصر موجود
      _cartItems[existingIndex]['quantity'] += 1;
    } else {
      // إضافة عنصر جديد
      item['quantity'] = 1;
      _cartItems.add(item);
    }
    
    _calculateCartTotal();
    notifyListeners();
  }
  
  void removeFromCart(String itemId) {
    _cartItems.removeWhere((item) => item['id'] == itemId);
    _calculateCartTotal();
    notifyListeners();
  }
  
  void updateCartItemQuantity(String itemId, int quantity) {
    int index = _cartItems.indexWhere((item) => item['id'] == itemId);
    if (index >= 0) {
      if (quantity <= 0) {
        _cartItems.removeAt(index);
      } else {
        _cartItems[index]['quantity'] = quantity;
      }
      _calculateCartTotal();
      notifyListeners();
    }
  }
  
  void clearCart() {
    _cartItems.clear();
    _cartTotal = 0.0;
    notifyListeners();
  }
  
  void _calculateCartTotal() {
    _cartTotal = _cartItems.fold(0.0, (total, item) {
      return total + (item['price'] * item['quantity']);
    });
  }
  
  // إدارة الطلبات
  void addOrder(Map<String, dynamic> order) {
    _orders.insert(0, order); // إضافة في المقدمة
    notifyListeners();
  }
  
  void updateOrderStatus(String orderId, String status) {
    int index = _orders.indexWhere((order) => order['id'] == orderId);
    if (index >= 0) {
      _orders[index]['status'] = status;
      notifyListeners();
    }
  }
  
  // إدارة الإعدادات
  void toggleNotifications(bool enabled) {
    _notificationsEnabled = enabled;
    notifyListeners();
  }
  
  void toggleLocation(bool enabled) {
    _locationEnabled = enabled;
    notifyListeners();
  }
  
  void toggleDarkMode(bool enabled) {
    _darkModeEnabled = enabled;
    notifyListeners();
  }
  
  void changeLanguage(String language) {
    _selectedLanguage = language;
    notifyListeners();
  }
  
  // إدارة حالة التحميل والأخطاء
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  // إعادة تعيين الحالة
  void reset() {
    _isLoggedIn = false;
    _userData = null;
    _cartItems.clear();
    _cartTotal = 0.0;
    _orders.clear();
    _notificationsEnabled = true;
    _locationEnabled = true;
    _darkModeEnabled = false;
    _selectedLanguage = 'العربية';
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }
}

// مثيل واحد للحالة العامة
final AppState appState = AppState();

// دوال مساعدة للوصول السريع
class AppStateHelper {
  static void showLoading() {
    appState.setLoading(true);
  }
  
  static void hideLoading() {
    appState.setLoading(false);
  }
  
  static void showError(String message) {
    appState.setError(message);
  }
  
  static void clearError() {
    appState.clearError();
  }
  
  static bool get isCartEmpty => appState.cartItems.isEmpty;
  
  static String get cartItemsCountText {
    int count = appState.cartItemsCount;
    return count > 99 ? '99+' : count.toString();
  }
  
  static String get formattedCartTotal {
    return '${appState.cartTotal.toStringAsFixed(2)} ر.ي';
  }
}
