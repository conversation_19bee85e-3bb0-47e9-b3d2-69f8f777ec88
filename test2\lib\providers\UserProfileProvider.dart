import 'package:flutter/foundation.dart';
import '../models/UserProfile.dart';
import '../models/Address.dart';
import '../services/UserProfileService.dart';

/// مزود حالة الملف الشخصي للمستخدم
class UserProfileProvider extends ChangeNotifier {
  UserProfile? _userProfile;
  bool _isLoading = false;
  String? _error;

  /// الحصول على الملف الشخصي
  UserProfile? get userProfile => _userProfile;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get error => _error;

  /// التحقق من تسجيل الدخول
  bool get isLoggedIn => _userProfile != null;

  /// الحصول على الاسم الكامل
  String get fullName => _userProfile?.fullName ?? '';

  /// الحصول على البريد الإلكتروني
  String get email => _userProfile?.email ?? '';

  /// الحصول على رقم الهاتف
  String get phone => _userProfile?.phone ?? '';

  /// الحصول على العناوين
  List<Address> get addresses => _userProfile?.addresses ?? [];

  /// الحصول على العنوان الافتراضي
  Address? get defaultAddress => _userProfile?.defaultAddress;

  /// التحقق من اكتمال الملف الشخصي
  bool get isProfileComplete => _userProfile?.isProfileComplete ?? false;

  /// تحميل الملف الشخصي
  Future<void> loadUserProfile() async {
    _setLoading(true);
    _setError(null);

    try {
      _userProfile = await UserProfileService.getUserProfile();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل الملف الشخصي: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث المعلومات الأساسية
  Future<bool> updateBasicInfo({
    required String firstName,
    required String lastName,
    required String email,
    required String phone,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await UserProfileService.updateBasicInfo(
        firstName: firstName,
        lastName: lastName,
        email: email,
        phone: phone,
      );

      if (success) {
        await loadUserProfile(); // إعادة تحميل البيانات
        return true;
      } else {
        _setError('فشل في تحديث المعلومات');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تحديث المعلومات: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// إضافة عنوان جديد
  Future<bool> addAddress(Address address) async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await UserProfileService.addAddress(address);

      if (success) {
        await loadUserProfile(); // إعادة تحميل البيانات
        return true;
      } else {
        _setError('فشل في إضافة العنوان');
        return false;
      }
    } catch (e) {
      _setError('خطأ في إضافة العنوان: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث عنوان
  Future<bool> updateAddress(Address address) async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await UserProfileService.updateAddress(address);

      if (success) {
        await loadUserProfile(); // إعادة تحميل البيانات
        return true;
      } else {
        _setError('فشل في تحديث العنوان');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تحديث العنوان: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف عنوان
  Future<bool> removeAddress(String addressId) async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await UserProfileService.removeAddress(addressId);

      if (success) {
        await loadUserProfile(); // إعادة تحميل البيانات
        return true;
      } else {
        _setError('فشل في حذف العنوان');
        return false;
      }
    } catch (e) {
      _setError('خطأ في حذف العنوان: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تعيين عنوان كافتراضي
  Future<bool> setDefaultAddress(String addressId) async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await UserProfileService.setDefaultAddress(addressId);

      if (success) {
        await loadUserProfile(); // إعادة تحميل البيانات
        return true;
      } else {
        _setError('فشل في تعيين العنوان الافتراضي');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تعيين العنوان الافتراضي: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على عنوان بالمعرف
  Address? getAddressById(String addressId) {
    return addresses.where((addr) => addr.id == addressId).firstOrNull;
  }

  /// الحصول على عناوين حسب النوع
  List<Address> getAddressesByType(String type) {
    return addresses.where((addr) => addr.title == type).toList();
  }

  /// مسح جميع البيانات
  Future<bool> clearUserData() async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await UserProfileService.clearUserData();

      if (success) {
        _userProfile = null;
        notifyListeners();
        return true;
      } else {
        _setError('فشل في مسح البيانات');
        return false;
      }
    } catch (e) {
      _setError('خطأ في مسح البيانات: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// إنشاء عنوان جديد
  Address createNewAddress({
    required String title,
    required String fullAddress,
    required String city,
    required String district,
    required String street,
    required String buildingNumber,
    String apartmentNumber = '',
    String? landmark,
    String? additionalInfo,
    double? latitude,
    double? longitude,
    bool isDefault = false,
  }) {
    return UserProfileService.createNewAddress(
      title: title,
      fullAddress: fullAddress,
      city: city,
      district: district,
      street: street,
      buildingNumber: buildingNumber,
      apartmentNumber: apartmentNumber,
      landmark: landmark,
      additionalInfo: additionalInfo,
      latitude: latitude,
      longitude: longitude,
      isDefault: isDefault,
    );
  }

  /// التحقق من صحة رقم الهاتف
  bool isValidPhone(String phone) {
    return UserProfileService.isValidSaudiPhone(phone);
  }

  /// التحقق من صحة البريد الإلكتروني
  bool isValidEmail(String email) {
    return UserProfileService.isValidEmail(email);
  }

  /// تنسيق رقم الهاتف
  String formatPhone(String phone) {
    return UserProfileService.formatSaudiPhone(phone);
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
