import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:test2/services/SettingsService.dart';
import 'package:test2/models/LocationPermissionResult.dart';

/// خدمة إدارة الموقع والـ GPS - مسؤولة عن تفعيل وإلغاء تفعيل GPS حسب الإعدادات
class LocationService {
  // إنشاء مثيل واحد من الخدمة (Singleton Pattern)
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  // خدمة الإعدادات للتحقق من حالة تفعيل الموقع
  final SettingsService _settingsService = SettingsService();

  // متغيرات لحفظ حالة الموقع
  Position? _currentPosition; // الموقع الحالي
  bool _isLocationServiceEnabled = false; // حالة خدمة الموقع
  bool _hasLocationPermission = false; // حالة إذن الموقع

  // Getters للحصول على المعلومات
  Position? get currentPosition => _currentPosition;
  bool get isLocationServiceEnabled => _isLocationServiceEnabled;
  bool get hasLocationPermission => _hasLocationPermission;

  /// تهيئة خدمة الموقع وتطبيق الإعدادات
  Future<void> initialize() async {
    try {
      print('بدء تهيئة خدمة الموقع...');

      // تحميل إعدادات التطبيق
      await _settingsService.loadSettings();

      // تطبيق إعدادات الموقع
      await _applyLocationSettings();

      print('تم تهيئة خدمة الموقع بنجاح');
    } catch (e) {
      print('خطأ في تهيئة خدمة الموقع: $e');
    }
  }

  /// تطبيق إعدادات الموقع (تفعيل أو إلغاء تفعيل GPS)
  Future<void> _applyLocationSettings() async {
    try {
      // التحقق من إعدادات الموقع في التطبيق
      bool locationEnabledInSettings = _settingsService.locationEnabled;

      if (locationEnabledInSettings) {
        print('إعدادات الموقع مفعلة - محاولة تفعيل GPS...');
        await _enableLocationServices();
      } else {
        print('إعدادات الموقع معطلة - إلغاء تفعيل GPS...');
        await _disableLocationServices();
      }
    } catch (e) {
      print('خطأ في تطبيق إعدادات الموقع: $e');
    }
  }

  /// طلب أذونات الموقع مع واجهة تفاعلية للمستخدم
  Future<LocationPermissionResult>
      requestLocationPermissionInteractive() async {
    try {
      print('بدء طلب أذونات الموقع التفاعلي...');

      // 1. التحقق من تفعيل خدمة الموقع في النظام
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        print('خدمة الموقع غير مفعلة في النظام');
        return LocationPermissionResult.needsSystemSettings(
            'خدمة الموقع غير مفعلة في جهازك.\nيرجى تفعيلها من إعدادات الجهاز أولاً.');
      }

      // 2. فحص الإذن الحالي
      LocationPermission permission = await Geolocator.checkPermission();
      print('حالة الإذن الحالية: $permission');

      // 3. طلب الإذن إذا كان مرفوض
      if (permission == LocationPermission.denied) {
        print('طلب إذن الموقع من المستخدم...');
        permission = await Geolocator.requestPermission();
        print('نتيجة طلب الإذن: $permission');

        if (permission == LocationPermission.denied) {
          return LocationPermissionResult.failure(
              'تم رفض إذن الوصول للموقع.\nيرجى منح الإذن لتتبع طلبك على الخريطة.');
        }
      }

      // 4. التعامل مع الإذن المرفوض نهائياً
      if (permission == LocationPermission.deniedForever) {
        print('إذن الموقع مرفوض نهائياً');
        return LocationPermissionResult.needsAppSettings(
            'إذن الموقع مرفوض نهائياً.\nيرجى تفعيله من إعدادات التطبيق.');
      }

      // 5. التحقق من نجاح الحصول على الإذن
      if (permission == LocationPermission.whileInUse ||
          permission == LocationPermission.always) {
        // تحديث حالة الخدمة
        _isLocationServiceEnabled = true;
        _hasLocationPermission = true;

        // محاولة الحصول على الموقع للتأكد
        try {
          await _getCurrentLocation();
          print('تم الحصول على أذونات الموقع بنجاح');

          return LocationPermissionResult.success(
              'تم تفعيل خدمة الموقع بنجاح!\nيمكنك الآن تتبع طلبك على الخريطة.');
        } catch (e) {
          print('خطأ في الحصول على الموقع بعد منح الإذن: $e');
          return LocationPermissionResult.failure(
              'تم منح الإذن ولكن حدث خطأ في الحصول على الموقع.\nيرجى المحاولة مرة أخرى.');
        }
      }

      // 6. حالة غير متوقعة
      return LocationPermissionResult.failure(
          'حدث خطأ غير متوقع في طلب أذونات الموقع.\nيرجى المحاولة مرة أخرى.');
    } catch (e) {
      print('خطأ في طلب أذونات الموقع: $e');
      return LocationPermissionResult.failure(
          'حدث خطأ في طلب أذونات الموقع: $e');
    }
  }

  /// تفعيل خدمات الموقع والـ GPS
  Future<bool> _enableLocationServices() async {
    try {
      print('محاولة تفعيل خدمات الموقع...');

      // 1. التحقق من تفعيل خدمة الموقع في النظام
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        print('خدمة الموقع غير مفعلة في النظام');
        // محاولة فتح إعدادات الموقع (لا يعمل في الويب)
        try {
          await Geolocator.openLocationSettings();
          print('تم فتح إعدادات الموقع للمستخدم');
        } catch (e) {
          print('لا يمكن فتح إعدادات الموقع: $e');
        }
        _isLocationServiceEnabled = false;
        return false;
      }

      // 2. طلب إذن الموقع
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        print('إذن الموقع مرفوض - طلب الإذن...');
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          print('تم رفض إذن الموقع من المستخدم');
          _hasLocationPermission = false;
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        print('إذن الموقع مرفوض نهائياً - فتح إعدادات التطبيق...');
        await openAppSettings();
        _hasLocationPermission = false;
        return false;
      }

      // 3. تحديث حالة الخدمة
      _isLocationServiceEnabled = true;
      _hasLocationPermission = true;

      // 4. الحصول على الموقع الحالي
      await _getCurrentLocation();

      print('تم تفعيل خدمات الموقع بنجاح');
      return true;
    } catch (e) {
      print('خطأ في تفعيل خدمات الموقع: $e');
      _isLocationServiceEnabled = false;
      _hasLocationPermission = false;
      return false;
    }
  }

  /// إلغاء تفعيل خدمات الموقع
  Future<void> _disableLocationServices() async {
    try {
      print('إلغاء تفعيل خدمات الموقع...');

      // مسح الموقع الحالي
      _currentPosition = null;

      // تحديث حالة الخدمة
      _isLocationServiceEnabled = false;

      print('تم إلغاء تفعيل خدمات الموقع');

      // ملاحظة: لا يمكن إلغاء الأذونات برمجياً، لكن يمكن تجاهل استخدامها
    } catch (e) {
      print('خطأ في إلغاء تفعيل خدمات الموقع: $e');
    }
  }

  /// الحصول على الموقع الحالي
  Future<Position?> _getCurrentLocation() async {
    try {
      if (!_isLocationServiceEnabled || !_hasLocationPermission) {
        print('خدمة الموقع أو الإذن غير متاح');
        return null;
      }

      print('الحصول على الموقع الحالي...');

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: Duration(seconds: 10),
      );

      _currentPosition = position;
      print(
          'تم الحصول على الموقع: ${position.latitude}, ${position.longitude}');

      return position;
    } catch (e) {
      print('خطأ في الحصول على الموقع: $e');
      return null;
    }
  }

  /// تحديث إعدادات الموقع (يتم استدعاؤها عند تغيير الإعدادات)
  Future<void> updateLocationSettings(bool enabled) async {
    try {
      print('تحديث إعدادات الموقع: ${enabled ? 'تفعيل' : 'إلغاء تفعيل'}');

      // تطبيق الإعداد الجديد مباشرة (الحفظ يتم في SettingsService)
      await _applyLocationSettings();
    } catch (e) {
      print('خطأ في تحديث إعدادات الموقع: $e');
    }
  }

  /// إعادة تحميل الإعدادات وتطبيقها
  Future<void> reloadAndApplySettings() async {
    try {
      print('إعادة تحميل وتطبيق إعدادات الموقع...');

      // إعادة تحميل الإعدادات
      await _settingsService.loadSettings();

      // تطبيق الإعدادات المحدثة
      await _applyLocationSettings();

      print('تم إعادة تحميل وتطبيق إعدادات الموقع بنجاح');
    } catch (e) {
      print('خطأ في إعادة تحميل إعدادات الموقع: $e');
    }
  }

  /// الحصول على الموقع الحالي (للاستخدام الخارجي)
  Future<Position?> getCurrentLocation() async {
    try {
      // التحقق من الإعدادات أولاً
      if (!_settingsService.locationEnabled) {
        print('الموقع معطل في الإعدادات');
        return null;
      }

      // التحقق من حالة الخدمة
      if (!_isLocationServiceEnabled) {
        print('خدمة الموقع غير مفعلة - محاولة التفعيل...');
        bool enabled = await _enableLocationServices();
        if (!enabled) {
          return null;
        }
      }

      // الحصول على الموقع
      return await _getCurrentLocation();
    } catch (e) {
      print('خطأ في الحصول على الموقع: $e');
      return null;
    }
  }

  /// مراقبة تغييرات الموقع (للاستخدام المتقدم)
  Stream<Position>? watchPosition() {
    try {
      // التحقق من الإعدادات والأذونات
      if (!_settingsService.locationEnabled ||
          !_isLocationServiceEnabled ||
          !_hasLocationPermission) {
        print('الموقع غير متاح للمراقبة');
        return null;
      }

      print('بدء مراقبة تغييرات الموقع...');

      return Geolocator.getPositionStream(
        locationSettings: LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10, // تحديث كل 10 متر
        ),
      );
    } catch (e) {
      print('خطأ في مراقبة الموقع: $e');
      return null;
    }
  }

  /// التحقق من حالة خدمة الموقع
  Future<Map<String, bool>> checkLocationStatus() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      LocationPermission permission = await Geolocator.checkPermission();
      bool hasPermission = permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse;

      return {
        'serviceEnabled': serviceEnabled,
        'hasPermission': hasPermission,
        'settingsEnabled': _settingsService.locationEnabled,
        'fullyEnabled':
            serviceEnabled && hasPermission && _settingsService.locationEnabled,
      };
    } catch (e) {
      print('خطأ في التحقق من حالة الموقع: $e');
      return {
        'serviceEnabled': false,
        'hasPermission': false,
        'settingsEnabled': false,
        'fullyEnabled': false,
      };
    }
  }

  /// الحصول على المسافة بين نقطتين
  double getDistanceBetween(double startLatitude, double startLongitude,
      double endLatitude, double endLongitude) {
    return Geolocator.distanceBetween(
        startLatitude, startLongitude, endLatitude, endLongitude);
  }

  /// تنظيف الموارد
  void dispose() {
    _currentPosition = null;
    _isLocationServiceEnabled = false;
    _hasLocationPermission = false;
    print('تم تنظيف موارد خدمة الموقع');
  }
}
