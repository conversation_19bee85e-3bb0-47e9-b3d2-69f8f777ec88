com.example.test2.app-jetified-play-services-base-18.3.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\07c8d82ce007794779a5c5ed16d7cd9e\transformed\jetified-play-services-base-18.3.0\res
com.example.test2.app-core-runtime-2.2.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\0add3b154bb46c4e8da2ba168810302e\transformed\core-runtime-2.2.0\res
com.example.test2.app-jetified-android-maps-utils-3.6.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\0d053047d2b84b3a6f7afaef38f7da9f\transformed\jetified-android-maps-utils-3.6.0\res
com.example.test2.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\1ab3e8c4a152beb861f662fdb35db0b4\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.test2.app-lifecycle-livedata-core-2.7.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\227c23377be660dc70d8378f53efb034\transformed\lifecycle-livedata-core-2.7.0\res
com.example.test2.app-jetified-play-services-maps-18.2.0-5 C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\res
com.example.test2.app-appcompat-1.1.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\2fb1c87eb9bd4c095f30b9ec565cd4a6\transformed\appcompat-1.1.0\res
com.example.test2.app-jetified-core-1.0.0-7 C:\Users\<USER>\.gradle\caches\transforms-3\38caba5646d48ec3133d0930d3a170ec\transformed\jetified-core-1.0.0\res
com.example.test2.app-recyclerview-1.0.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\3d03fe9731b2e1b01913ae2b47f6c626\transformed\recyclerview-1.0.0\res
com.example.test2.app-transition-1.4.1-9 C:\Users\<USER>\.gradle\caches\transforms-3\4560e8224758ac8d8fc066fe7120503d\transformed\transition-1.4.1\res
com.example.test2.app-preference-1.2.1-10 C:\Users\<USER>\.gradle\caches\transforms-3\47ac933c65727cbf2c22795a6a720497\transformed\preference-1.2.1\res
com.example.test2.app-slidingpanelayout-1.2.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\4807259684d3baf304e9884745c81594\transformed\slidingpanelayout-1.2.0\res
com.example.test2.app-coordinatorlayout-1.0.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\483a6b61ae086fccd0e7a62adbbb50b3\transformed\coordinatorlayout-1.0.0\res
com.example.test2.app-jetified-savedstate-ktx-1.2.1-13 C:\Users\<USER>\.gradle\caches\transforms-3\4b77aa1c7939b4b334a376f77fa0af71\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.test2.app-core-1.13.1-14 C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\res
com.example.test2.app-jetified-tracing-1.2.0-15 C:\Users\<USER>\.gradle\caches\transforms-3\5ca79bf7669beb32647679bbc596366f\transformed\jetified-tracing-1.2.0\res
com.example.test2.app-lifecycle-livedata-2.7.0-16 C:\Users\<USER>\.gradle\caches\transforms-3\64d2a37639d696b7a8a5becd2482ef8c\transformed\lifecycle-livedata-2.7.0\res
com.example.test2.app-fragment-1.7.1-17 C:\Users\<USER>\.gradle\caches\transforms-3\6d8587edf13ce113db373e0ea22bdae2\transformed\fragment-1.7.1\res
com.example.test2.app-media-1.1.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\71303a5a371ce3bcd89a8ecc89eb33fe\transformed\media-1.1.0\res
com.example.test2.app-jetified-datastore-release-19 C:\Users\<USER>\.gradle\caches\transforms-3\779943c452b4a7806949500cb43a0aaa\transformed\jetified-datastore-release\res
com.example.test2.app-lifecycle-viewmodel-2.7.0-20 C:\Users\<USER>\.gradle\caches\transforms-3\7d00bd86c9728072d21116e569b66936\transformed\lifecycle-viewmodel-2.7.0\res
com.example.test2.app-jetified-core-ktx-1.13.1-21 C:\Users\<USER>\.gradle\caches\transforms-3\7d665add48a3ce4929d25692cd71eff9\transformed\jetified-core-ktx-1.13.1\res
com.example.test2.app-jetified-profileinstaller-1.3.1-22 C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\res
com.example.test2.app-jetified-fragment-ktx-1.7.1-23 C:\Users\<USER>\.gradle\caches\transforms-3\820c230d1cdbcc586c1be9a296922dcb\transformed\jetified-fragment-ktx-1.7.1\res
com.example.test2.app-jetified-play-services-basement-18.3.0-24 C:\Users\<USER>\.gradle\caches\transforms-3\88e3d0d424506a1c1cef1c33bd4edbb3\transformed\jetified-play-services-basement-18.3.0\res
com.example.test2.app-jetified-lifecycle-viewmodel-ktx-2.7.0-25 C:\Users\<USER>\.gradle\caches\transforms-3\944036875dfb5b1ad75660bb08cf8f21\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.test2.app-jetified-annotation-experimental-1.4.0-26 C:\Users\<USER>\.gradle\caches\transforms-3\946cc3183aee3e178513dcb0cbecada4\transformed\jetified-annotation-experimental-1.4.0\res
com.example.test2.app-jetified-appcompat-resources-1.1.0-27 C:\Users\<USER>\.gradle\caches\transforms-3\988e907a890df94d30db83a31844424d\transformed\jetified-appcompat-resources-1.1.0\res
com.example.test2.app-jetified-lifecycle-runtime-ktx-2.7.0-28 C:\Users\<USER>\.gradle\caches\transforms-3\a2795d897b67a444aea28574e2c90ed9\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.test2.app-jetified-lifecycle-process-2.7.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\res
com.example.test2.app-lifecycle-runtime-2.7.0-30 C:\Users\<USER>\.gradle\caches\transforms-3\a6944d895559a9e64d476809b757492a\transformed\lifecycle-runtime-2.7.0\res
com.example.test2.app-jetified-window-1.2.0-31 C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\res
com.example.test2.app-jetified-activity-ktx-1.8.1-32 C:\Users\<USER>\.gradle\caches\transforms-3\bd63252b171cb9b1eb2d7179d962a84d\transformed\jetified-activity-ktx-1.8.1\res
com.example.test2.app-jetified-window-java-1.2.0-33 C:\Users\<USER>\.gradle\caches\transforms-3\be2424c7a9e7c73f88a03fb3b2d1f940\transformed\jetified-window-java-1.2.0\res
com.example.test2.app-jetified-datastore-core-release-34 C:\Users\<USER>\.gradle\caches\transforms-3\c263179e5929eba9c0411e38dc36b189\transformed\jetified-datastore-core-release\res
com.example.test2.app-jetified-datastore-preferences-release-35 C:\Users\<USER>\.gradle\caches\transforms-3\ccd4ca03ece161446ae024d77dca503c\transformed\jetified-datastore-preferences-release\res
com.example.test2.app-jetified-startup-runtime-1.1.1-36 C:\Users\<USER>\.gradle\caches\transforms-3\cf97fcc817d260f850a9811dd743a790\transformed\jetified-startup-runtime-1.1.1\res
com.example.test2.app-jetified-lifecycle-livedata-core-ktx-2.7.0-37 C:\Users\<USER>\.gradle\caches\transforms-3\d1bae4ef94112e6854fa16186d294c8a\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.test2.app-jetified-savedstate-1.2.1-38 C:\Users\<USER>\.gradle\caches\transforms-3\df93ef38b220bb54f4bc5fec3f1dd8ec\transformed\jetified-savedstate-1.2.1\res
com.example.test2.app-jetified-activity-1.8.1-39 C:\Users\<USER>\.gradle\caches\transforms-3\fe89009948d46fc15d9f02484b776d7a\transformed\jetified-activity-1.8.1\res
com.example.test2.app-debug-40 F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\debug\res
com.example.test2.app-main-41 F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\res
com.example.test2.app-pngs-42 F:\FlutterApps\augmentsAPP\appss\test2\build\app\generated\res\pngs\debug
com.example.test2.app-resValues-43 F:\FlutterApps\augmentsAPP\appss\test2\build\app\generated\res\resValues\debug
com.example.test2.app-mergeDebugResources-44 F:\FlutterApps\augmentsAPP\appss\test2\build\app\intermediates\incremental\debug\mergeDebugResources\merged.dir
com.example.test2.app-mergeDebugResources-45 F:\FlutterApps\augmentsAPP\appss\test2\build\app\intermediates\incremental\debug\mergeDebugResources\stripped.dir
com.example.test2.app-merged_res-46 F:\FlutterApps\augmentsAPP\appss\test2\build\app\intermediates\merged_res\debug
com.example.test2.app-packaged_res-47 F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_local_notifications\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-48 F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-49 F:\FlutterApps\augmentsAPP\appss\test2\build\geocoding_android\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-50 F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-51 F:\FlutterApps\augmentsAPP\appss\test2\build\google_maps_flutter_android\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-52 F:\FlutterApps\augmentsAPP\appss\test2\build\permission_handler_android\intermediates\packaged_res\debug
com.example.test2.app-packaged_res-53 F:\FlutterApps\augmentsAPP\appss\test2\build\shared_preferences_android\intermediates\packaged_res\debug
