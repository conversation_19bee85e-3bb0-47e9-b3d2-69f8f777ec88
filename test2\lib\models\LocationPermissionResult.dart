/// نموذج نتيجة طلب أذونات الموقع
class LocationPermissionResult {
  final bool success;
  final String message;
  final bool needsSystemSettings;
  final bool needsAppSettings;

  LocationPermissionResult({
    required this.success,
    required this.message,
    this.needsSystemSettings = false,
    this.needsAppSettings = false,
  });

  /// إنشاء نتيجة نجاح
  factory LocationPermissionResult.success(String message) {
    return LocationPermissionResult(
      success: true,
      message: message,
    );
  }

  /// إنشاء نتيجة فشل مع الحاجة لإعدادات النظام
  factory LocationPermissionResult.needsSystemSettings(String message) {
    return LocationPermissionResult(
      success: false,
      message: message,
      needsSystemSettings: true,
    );
  }

  /// إنشاء نتيجة فشل مع الحاجة لإعدادات التطبيق
  factory LocationPermissionResult.needsAppSettings(String message) {
    return LocationPermissionResult(
      success: false,
      message: message,
      needsAppSettings: true,
    );
  }

  /// إنشاء نتيجة فشل عادية
  factory LocationPermissionResult.failure(String message) {
    return LocationPermissionResult(
      success: false,
      message: message,
    );
  }

  @override
  String toString() {
    return 'LocationPermissionResult(success: $success, message: $message, needsSystemSettings: $needsSystemSettings, needsAppSettings: $needsAppSettings)';
  }
}
