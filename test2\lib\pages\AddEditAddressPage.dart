import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/Address.dart';
import '../providers/AddressProvider.dart';
import '../widgets/CustomSnackBars.dart';

class AddEditAddressPage extends StatefulWidget {
  final Address? address; // null للإضافة، Address للتعديل

  const AddEditAddressPage({Key? key, this.address}) : super(key: key);

  @override
  _AddEditAddressPageState createState() => _AddEditAddressPageState();
}

class _AddEditAddressPageState extends State<AddEditAddressPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _cityController = TextEditingController();
  final _districtController = TextEditingController();
  final _streetController = TextEditingController();
  final _buildingController = TextEditingController();
  final _apartmentController = TextEditingController();
  final _landmarkController = TextEditingController();
  final _additionalInfoController = TextEditingController();

  String _selectedType = 'المنزل';
  bool _isDefault = false;

  bool get _isEditing => widget.address != null;

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    if (_isEditing && widget.address != null) {
      final address = widget.address!;
      _titleController.text = address.title;
      _cityController.text = address.city;
      _districtController.text = address.district;
      _streetController.text = address.street;
      _buildingController.text = address.buildingNumber;
      _apartmentController.text = address.apartmentNumber;
      _landmarkController.text = address.landmark ?? '';
      _additionalInfoController.text = address.additionalInfo ?? '';
      _selectedType = address.title;
      _isDefault = address.isDefault;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: AppBar(
        backgroundColor: Color(0xFF4C53A5),
        elevation: 0,
        centerTitle: true,
        title: Text(
          _isEditing ? 'تعديل العنوان' : 'إضافة عنوان جديد',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Consumer<AddressProvider>(
        builder: (context, addressProvider, child) {
          return Form(
            key: _formKey,
            child: ListView(
              padding: EdgeInsets.all(16),
              children: [
                _buildAddressTypeCard(),
                SizedBox(height: 16),
                _buildLocationDetailsCard(),
                SizedBox(height: 16),
                _buildAdditionalInfoCard(),
                SizedBox(height: 24),
                _buildSaveButton(addressProvider),
                SizedBox(height: 16),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAddressTypeCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.label, color: Color(0xFF4C53A5)),
                SizedBox(width: 8),
                Text(
                  'نوع العنوان',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C53A5),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedType,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: Icon(_getTypeIcon(_selectedType)),
              ),
              items: ['المنزل', 'العمل', 'الأصدقاء', 'العائلة', 'أخرى']
                  .map((type) => DropdownMenuItem(
                        value: type,
                        child: Row(
                          children: [
                            Icon(_getTypeIcon(type), size: 20),
                            SizedBox(width: 8),
                            Text(type),
                          ],
                        ),
                      ))
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                  _titleController.text = value;
                });
              },
            ),
            if (_selectedType == 'أخرى') ...[
              SizedBox(height: 12),
              TextFormField(
                controller: _titleController,
                decoration: InputDecoration(
                  labelText: 'اسم العنوان المخصص',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: Icon(Icons.edit),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اسم العنوان';
                  }
                  return null;
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLocationDetailsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.location_on, color: Color(0xFF4C53A5)),
                SizedBox(width: 8),
                Text(
                  'تفاصيل الموقع',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C53A5),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _cityController,
                    decoration: InputDecoration(
                      labelText: 'المدينة',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: Icon(Icons.location_city),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال المدينة';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _districtController,
                    decoration: InputDecoration(
                      labelText: 'الحي',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: Icon(Icons.map),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال الحي';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            TextFormField(
              controller: _streetController,
              decoration: InputDecoration(
                labelText: 'الشارع',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: Icon(Icons.streetview),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم الشارع';
                }
                return null;
              },
            ),
            SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _buildingController,
                    decoration: InputDecoration(
                      labelText: 'رقم المبنى',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: Icon(Icons.business),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال رقم المبنى';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _apartmentController,
                    decoration: InputDecoration(
                      labelText: 'رقم الشقة (اختياري)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: Icon(Icons.door_front_door),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Color(0xFF4C53A5)),
                SizedBox(width: 8),
                Text(
                  'معلومات إضافية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C53A5),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            TextFormField(
              controller: _landmarkController,
              decoration: InputDecoration(
                labelText: 'علامة مميزة (اختياري)',
                hintText: 'مثل: بجانب مسجد النور',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: Icon(Icons.place),
              ),
            ),
            SizedBox(height: 12),
            TextFormField(
              controller: _additionalInfoController,
              decoration: InputDecoration(
                labelText: 'تفاصيل إضافية (اختياري)',
                hintText: 'أي معلومات أخرى تساعد في الوصول',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: Icon(Icons.note),
              ),
              maxLines: 3,
            ),
            SizedBox(height: 16),
            CheckboxListTile(
              title: Text('جعل هذا العنوان افتراضي'),
              subtitle: Text('سيتم استخدامه في جميع الطلبات'),
              value: _isDefault,
              onChanged: (value) {
                setState(() {
                  _isDefault = value ?? false;
                });
              },
              activeColor: Color(0xFF4C53A5),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton(AddressProvider addressProvider) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: addressProvider.isLoading ? null : () => _saveAddress(addressProvider),
        style: ElevatedButton.styleFrom(
          backgroundColor: Color(0xFF4C53A5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: addressProvider.isLoading
            ? CircularProgressIndicator(color: Colors.white)
            : Text(
                _isEditing ? 'تحديث العنوان' : 'حفظ العنوان',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'المنزل':
        return Icons.home;
      case 'العمل':
        return Icons.work;
      case 'الأصدقاء':
        return Icons.people;
      case 'العائلة':
        return Icons.family_restroom;
      default:
        return Icons.location_on;
    }
  }

  void _saveAddress(AddressProvider addressProvider) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final fullAddress = '${_streetController.text.trim()}, ${_districtController.text.trim()}, ${_cityController.text.trim()}';
    
    final address = Address(
      id: _isEditing ? widget.address!.id : 'addr_${DateTime.now().millisecondsSinceEpoch}',
      title: _selectedType == 'أخرى' ? _titleController.text.trim() : _selectedType,
      fullAddress: fullAddress,
      city: _cityController.text.trim(),
      district: _districtController.text.trim(),
      street: _streetController.text.trim(),
      buildingNumber: _buildingController.text.trim(),
      apartmentNumber: _apartmentController.text.trim(),
      landmark: _landmarkController.text.trim().isNotEmpty ? _landmarkController.text.trim() : null,
      additionalInfo: _additionalInfoController.text.trim().isNotEmpty ? _additionalInfoController.text.trim() : null,
      isDefault: _isDefault,
    );

    bool success;
    if (_isEditing) {
      success = await addressProvider.updateAddress(address);
      if (_isDefault) {
        await addressProvider.setDefaultAddress(address.id);
      }
    } else {
      success = await addressProvider.addAddress(address);
      if (_isDefault) {
        await addressProvider.setDefaultAddress(address.id);
      }
    }

    if (success) {
      CustomSnackBars.showSuccess(
        context,
        message: _isEditing ? 'تم تحديث العنوان بنجاح' : 'تم إضافة العنوان بنجاح',
        subtitle: 'العنوان متاح الآن للاستخدام',
      );
      Navigator.pop(context, true);
    } else {
      CustomSnackBars.showError(
        context,
        message: _isEditing ? 'فشل في تحديث العنوان' : 'فشل في إضافة العنوان',
        subtitle: addressProvider.error ?? 'يرجى المحاولة مرة أخرى',
      );
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _cityController.dispose();
    _districtController.dispose();
    _streetController.dispose();
    _buildingController.dispose();
    _apartmentController.dispose();
    _landmarkController.dispose();
    _additionalInfoController.dispose();
    super.dispose();
  }
}
