# 🔥 خطوات إعداد Firebase - دليل تفصيلي

## 📋 **المرحلة 1: إنشاء مشروع Firebase**

### **1. إنشاء المشروع:**
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على **"إنشاء مشروع"** أو **"Create a project"**
3. أدخل اسم المشروع: `zad-delivery-app`
4. اختر **"تفعيل Google Analytics"** ✅
5. اختر حساب Google Analytics أو أنشئ جديد
6. انقر **"إنشاء المشروع"**

### **2. إعداد المشروع:**
- انتظر حتى يكتمل إنشاء المشروع
- ستظهر رسالة "مشروعك جاهز"
- انقر **"متابعة"**

---

## 📱 **المرحلة 2: إضافة التطبيقات**

### **أ. إضافة تطبيق Android:**

#### **1. إضافة التطبيق:**
- في لوحة تحكم Firebase، انقر على أيقونة **Android**
- أدخل **Package name**: `com.zad.delivery` (أو حسب pubspec.yaml)
- أدخل **App nickname**: `Zad Delivery Android`
- أدخل **SHA-1** (اختياري للآن)
- انقر **"تسجيل التطبيق"**

#### **2. تحميل ملف التكوين:**
- حمل ملف `google-services.json`
- ضعه في: `android/app/google-services.json`

#### **3. إضافة Firebase SDK:**
في `android/build.gradle`:
```gradle
buildscript {
    dependencies {
        classpath 'com.google.gms:google-services:4.4.0'
    }
}
```

في `android/app/build.gradle`:
```gradle
apply plugin: 'com.google.gms.google-services'

dependencies {
    implementation platform('com.google.firebase:firebase-bom:32.7.0')
}
```

### **ب. إضافة تطبيق iOS:**

#### **1. إضافة التطبيق:**
- انقر على أيقونة **iOS**
- أدخل **Bundle ID**: `com.zad.delivery`
- أدخل **App nickname**: `Zad Delivery iOS`
- انقر **"تسجيل التطبيق"**

#### **2. تحميل ملف التكوين:**
- حمل ملف `GoogleService-Info.plist`
- ضعه في: `ios/Runner/GoogleService-Info.plist`

---

## 🔐 **المرحلة 3: تفعيل خدمات Firebase**

### **1. Firebase Authentication:**

#### **في Firebase Console:**
1. اذهب إلى **Authentication** > **Sign-in method**
2. فعل الطرق التالية:
   - ✅ **Email/Password** - للمستخدمين المسجلين
   - ✅ **Phone** - تسجيل دخول بالرقم
   - ✅ **Anonymous** - للضيوف
   - ✅ **Google** - تسجيل دخول بـ Google (اختياري)

#### **إعدادات إضافية:**
- في **Settings** > **Authorized domains**
- أضف النطاقات المطلوبة للويب (إذا كنت تدعم الويب)

### **2. Cloud Firestore:**

#### **إنشاء قاعدة البيانات:**
1. اذهب إلى **Firestore Database**
2. انقر **"Create database"**
3. اختر **"Start in test mode"** (مؤقتاً)
4. اختر الموقع الجغرافي: **asia-south1** (الهند - الأقرب للمنطقة العربية)
5. انقر **"Done"**

#### **إعداد Security Rules:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد المستخدمين
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد الطلبات
    match /orders/{orderId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.userId || 
         request.auth.token.admin == true);
    }
    
    // قواعد المنتجات (قراءة للجميع)
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // قواعد المتاجر (قراءة للجميع)
    match /stores/{storeId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // قواعد الإشعارات
    match /notifications/{userId}/{document=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

### **3. Firebase Cloud Messaging:**

#### **تفعيل FCM:**
1. اذهب إلى **Cloud Messaging**
2. الخدمة مفعلة تلقائياً
3. احفظ **Server Key** للاستخدام لاحقاً

#### **إعداد قنوات الإشعارات:**
```dart
// في NotificationService.dart
final List<NotificationChannel> channels = [
  NotificationChannel(
    id: 'orders_channel',
    name: 'إشعارات الطلبات',
    description: 'تحديثات حالة الطلبات',
    importance: Importance.high,
  ),
  NotificationChannel(
    id: 'offers_channel', 
    name: 'العروض والخصومات',
    description: 'إشعارات العروض الجديدة',
    importance: Importance.defaultImportance,
  ),
  NotificationChannel(
    id: 'general_channel',
    name: 'إشعارات عامة', 
    description: 'أخبار التطبيق والتحديثات',
    importance: Importance.low,
  ),
];
```

### **4. Firebase Analytics:**
- الخدمة مفعلة تلقائياً مع إنشاء المشروع
- لا حاجة لإعدادات إضافية

### **5. Firebase Crashlytics:**

#### **تفعيل Crashlytics:**
1. اذهب إلى **Crashlytics**
2. انقر **"Enable Crashlytics"**
3. اتبع التعليمات لإضافة SDK

---

## 📦 **المرحلة 4: إضافة التبعيات**

### **في pubspec.yaml:**
```yaml
dependencies:
  # Firebase Core (مطلوب لجميع خدمات Firebase)
  firebase_core: ^2.24.2
  
  # المصادقة
  firebase_auth: ^4.15.3
  google_sign_in: ^6.1.6
  
  # قاعدة البيانات
  cloud_firestore: ^4.13.6
  
  # الإشعارات
  firebase_messaging: ^14.7.10
  
  # التحليلات والمراقبة
  firebase_analytics: ^10.7.4
  firebase_crashlytics: ^3.4.8
  firebase_performance: ^0.9.3+8
  
  # الإعدادات البعيدة
  firebase_remote_config: ^4.3.8
  
  # الحماية
  firebase_app_check: ^0.2.1+8
  
  # التخزين (اختياري)
  firebase_storage: ^11.5.6
```

### **تشغيل الأوامر:**
```bash
flutter pub get
flutter clean
flutter pub run flutter_packages get
```

---

## 🔧 **المرحلة 5: تهيئة Firebase في التطبيق**

### **1. تحديث main.dart:**
```dart
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart'; // سيتم إنشاؤه تلقائياً

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  // تهيئة خدمة الإشعارات
  await NotificationService().initialize();
  
  // باقي الكود...
  runApp(MyApp());
}
```

### **2. إنشاء ملف firebase_options.dart:**
```bash
flutter packages pub run firebase_tools:generate-firebase-options \
  --platforms=android,ios,web \
  --out=lib/firebase_options.dart \
  --project=zad-delivery-app
```

---

## 🧪 **المرحلة 6: اختبار التكامل**

### **1. اختبار Firebase Core:**
```dart
// في main.dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase تم تهيئته بنجاح');
  } catch (e) {
    print('❌ خطأ في تهيئة Firebase: $e');
  }
  
  runApp(MyApp());
}
```

### **2. اختبار Authentication:**
```dart
// اختبار تسجيل دخول مجهول
try {
  UserCredential userCredential = await FirebaseAuth.instance.signInAnonymously();
  print('✅ تم تسجيل الدخول كضيف: ${userCredential.user?.uid}');
} catch (e) {
  print('❌ خطأ في تسجيل الدخول: $e');
}
```

### **3. اختبار Firestore:**
```dart
// اختبار كتابة وقراءة البيانات
try {
  await FirebaseFirestore.instance.collection('test').doc('test').set({
    'message': 'Hello Firebase!',
    'timestamp': FieldValue.serverTimestamp(),
  });
  print('✅ تم حفظ البيانات في Firestore');
  
  DocumentSnapshot doc = await FirebaseFirestore.instance.collection('test').doc('test').get();
  print('✅ تم قراءة البيانات: ${doc.data()}');
} catch (e) {
  print('❌ خطأ في Firestore: $e');
}
```

---

## 🚀 **المرحلة 7: النشر والإنتاج**

### **1. تحديث Security Rules:**
```javascript
// قواعد أمان للإنتاج
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if false; // منع الوصول افتراضياً
    }
    
    // قواعد محددة لكل مجموعة...
  }
}
```

### **2. تفعيل App Check:**
```dart
// في main.dart
await FirebaseAppCheck.instance.activate(
  webRecaptchaSiteKey: 'your-recaptcha-site-key',
  androidProvider: AndroidProvider.debug, // للتطوير
  // androidProvider: AndroidProvider.playIntegrity, // للإنتاج
);
```

### **3. إعداد Remote Config:**
```dart
final remoteConfig = FirebaseRemoteConfig.instance;
await remoteConfig.setConfigSettings(RemoteConfigSettings(
  fetchTimeout: const Duration(minutes: 1),
  minimumFetchInterval: const Duration(hours: 1),
));

await remoteConfig.setDefaults({
  'delivery_fee': 5.0,
  'free_delivery_minimum': 50.0,
  'maintenance_mode': false,
});
```

---

## ✅ **قائمة التحقق النهائية:**

### **الإعداد الأساسي:**
- [ ] إنشاء مشروع Firebase
- [ ] إضافة تطبيق Android
- [ ] إضافة تطبيق iOS
- [ ] تحميل ملفات التكوين
- [ ] إضافة Firebase SDK

### **الخدمات:**
- [ ] تفعيل Authentication
- [ ] إنشاء قاعدة بيانات Firestore
- [ ] إعداد Security Rules
- [ ] تفعيل Cloud Messaging
- [ ] تفعيل Analytics
- [ ] تفعيل Crashlytics

### **التطبيق:**
- [ ] إضافة التبعيات
- [ ] تهيئة Firebase في main.dart
- [ ] إنشاء firebase_options.dart
- [ ] اختبار التكامل
- [ ] تحديث الخدمات الموجودة

### **الإنتاج:**
- [ ] تحديث Security Rules
- [ ] تفعيل App Check
- [ ] إعداد Remote Config
- [ ] اختبار شامل
- [ ] نشر التطبيق

---

## 🎯 **النتيجة المتوقعة:**

بعد إكمال هذه الخطوات ستحصل على:
- ✅ **تطبيق متصل بـ Firebase** بالكامل
- ✅ **نظام مصادقة متقدم** وآمن
- ✅ **قاعدة بيانات سحابية** مع مزامنة فورية
- ✅ **إشعارات push** ذكية
- ✅ **تحليلات شاملة** لسلوك المستخدمين
- ✅ **مراقبة الأداء** والأخطاء
- ✅ **بنية تحتية احترافية** قابلة للتوسع

**تطبيقك سيصبح جاهزاً للإنتاج مع أفضل الممارسات!** 🚀🔥💯
