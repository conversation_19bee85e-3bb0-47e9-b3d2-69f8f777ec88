import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/Product.dart';
import '../models/Store.dart';

/// خدمة البحث المتقدم
class SearchService {
  static final SearchService _instance = SearchService._internal();
  factory SearchService() => _instance;
  SearchService._internal();

  // مفاتيح التخزين
  static const String _searchHistoryKey = 'search_history';
  static const String _popularSearchesKey = 'popular_searches';
  static const String _recentSearchesKey = 'recent_searches';

  List<String> _searchHistory = [];
  List<String> _popularSearches = [];
  List<String> _recentSearches = [];

  // Getters
  List<String> get searchHistory => List.unmodifiable(_searchHistory);
  List<String> get popularSearches => List.unmodifiable(_popularSearches);
  List<String> get recentSearches => List.unmodifiable(_recentSearches);

  /// تحميل تاريخ البحث
  Future<void> loadSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحميل تاريخ البحث
      final historyJson = prefs.getStringList(_searchHistoryKey) ?? [];
      _searchHistory = historyJson;

      // تحميل البحثات الشائعة
      final popularJson = prefs.getStringList(_popularSearchesKey) ?? [];
      _popularSearches =
          popularJson.isNotEmpty ? popularJson : _getDefaultPopularSearches();

      // تحميل البحثات الأخيرة
      final recentJson = prefs.getStringList(_recentSearchesKey) ?? [];
      _recentSearches = recentJson;
    } catch (e) {
      print('خطأ في تحميل تاريخ البحث: $e');
      _initializeDefaultData();
    }
  }

  /// إضافة بحث جديد
  Future<void> addSearch(String query) async {
    if (query.trim().isEmpty) return;

    final cleanQuery = query.trim().toLowerCase();

    try {
      // إضافة للتاريخ
      _searchHistory.remove(cleanQuery);
      _searchHistory.insert(0, cleanQuery);

      // الاحتفاظ بآخر 50 بحث
      if (_searchHistory.length > 50) {
        _searchHistory = _searchHistory.take(50).toList();
      }

      // إضافة للبحثات الأخيرة
      _recentSearches.remove(cleanQuery);
      _recentSearches.insert(0, cleanQuery);

      // الاحتفاظ بآخر 10 بحثات
      if (_recentSearches.length > 10) {
        _recentSearches = _recentSearches.take(10).toList();
      }

      // حفظ البيانات
      await _saveSearchData();
    } catch (e) {
      print('خطأ في إضافة البحث: $e');
    }
  }

  /// البحث في المنتجات
  Future<List<Product>> searchProducts(
    String query, {
    FilterOptions? filters,
    SortOption? sortOption,
  }) async {
    if (query.trim().isEmpty) return [];

    try {
      // إضافة البحث للتاريخ
      await addSearch(query);

      // محاكاة البحث في المنتجات (في التطبيق الحقيقي ستكون API call)
      List<Product> allProducts = _getMockProducts();

      // فلترة النتائج حسب النص
      List<Product> filteredProducts = allProducts.where((product) {
        return product.name.toLowerCase().contains(query.toLowerCase()) ||
            product.description.toLowerCase().contains(query.toLowerCase()) ||
            product.category.toLowerCase().contains(query.toLowerCase());
      }).toList();

      // تطبيق الفلاتر
      if (filters != null) {
        filteredProducts = _applyFilters(filteredProducts, filters);
      }

      // تطبيق الترتيب
      if (sortOption != null) {
        filteredProducts = _applySorting(filteredProducts, sortOption);
      }

      return filteredProducts;
    } catch (e) {
      print('خطأ في البحث: $e');
      return [];
    }
  }

  /// البحث في المتاجر
  Future<List<Store>> searchStores(String query) async {
    if (query.trim().isEmpty) return [];

    try {
      await addSearch(query);

      List<Store> allStores = _getMockStores();

      return allStores.where((store) {
        return store.name.toLowerCase().contains(query.toLowerCase()) ||
            store.description.toLowerCase().contains(query.toLowerCase()) ||
            store.category.toLowerCase().contains(query.toLowerCase());
      }).toList();
    } catch (e) {
      print('خطأ في البحث في المتاجر: $e');
      return [];
    }
  }

  /// الحصول على اقتراحات البحث
  List<String> getSearchSuggestions(String query) {
    if (query.trim().isEmpty) return _recentSearches;

    final lowerQuery = query.toLowerCase();
    List<String> suggestions = [];

    // البحث في التاريخ
    suggestions.addAll(_searchHistory
        .where((item) => item.toLowerCase().contains(lowerQuery))
        .take(5));

    // البحث في الشائعة
    suggestions.addAll(_popularSearches
        .where((item) =>
            item.toLowerCase().contains(lowerQuery) &&
            !suggestions.contains(item))
        .take(3));

    // إضافة اقتراحات ذكية
    suggestions.addAll(_getSmartSuggestions(query)
        .where((item) => !suggestions.contains(item))
        .take(2));

    return suggestions.take(10).toList();
  }

  /// مسح تاريخ البحث
  Future<void> clearSearchHistory() async {
    try {
      _searchHistory.clear();
      _recentSearches.clear();
      await _saveSearchData();
    } catch (e) {
      print('خطأ في مسح تاريخ البحث: $e');
    }
  }

  /// حذف بحث محدد
  Future<void> removeSearch(String query) async {
    try {
      _searchHistory.remove(query);
      _recentSearches.remove(query);
      await _saveSearchData();
    } catch (e) {
      print('خطأ في حذف البحث: $e');
    }
  }

  /// تطبيق الفلاتر
  List<Product> _applyFilters(List<Product> products, FilterOptions filters) {
    List<Product> filtered = List.from(products);

    // فلتر السعر
    if (filters.minPrice != null) {
      filtered = filtered.where((p) => p.price >= filters.minPrice!).toList();
    }
    if (filters.maxPrice != null) {
      filtered = filtered.where((p) => p.price <= filters.maxPrice!).toList();
    }

    // فلتر الفئة
    if (filters.categories.isNotEmpty) {
      filtered = filtered
          .where((p) => filters.categories.contains(p.category))
          .toList();
    }

    // فلتر التقييم
    if (filters.minRating != null) {
      filtered = filtered.where((p) => p.rating >= filters.minRating!).toList();
    }

    // فلتر التوفر
    if (filters.availableOnly) {
      filtered = filtered.where((p) => p.isAvailable).toList();
    }

    return filtered;
  }

  /// تطبيق الترتيب
  List<Product> _applySorting(List<Product> products, SortOption sortOption) {
    List<Product> sorted = List.from(products);

    switch (sortOption) {
      case SortOption.priceAsc:
        sorted.sort((a, b) => a.price.compareTo(b.price));
        break;
      case SortOption.priceDesc:
        sorted.sort((a, b) => b.price.compareTo(a.price));
        break;
      case SortOption.ratingDesc:
        sorted.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case SortOption.nameAsc:
        sorted.sort((a, b) => a.name.compareTo(b.name));
        break;
      case SortOption.newest:
        sorted.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case SortOption.popular:
        sorted.sort((a, b) => b.orderCount.compareTo(a.orderCount));
        break;
    }

    return sorted;
  }

  /// حفظ بيانات البحث
  Future<void> _saveSearchData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_searchHistoryKey, _searchHistory);
      await prefs.setStringList(_recentSearchesKey, _recentSearches);
      await prefs.setStringList(_popularSearchesKey, _popularSearches);
    } catch (e) {
      print('خطأ في حفظ بيانات البحث: $e');
    }
  }

  /// تهيئة البيانات الافتراضية
  void _initializeDefaultData() {
    _searchHistory = [];
    _recentSearches = [];
    _popularSearches = _getDefaultPopularSearches();
  }

  /// الحصول على البحثات الشائعة الافتراضية
  List<String> _getDefaultPopularSearches() {
    return [
      'برجر',
      'بيتزا',
      'شاورما',
      'مشروبات',
      'حلويات',
      'سلطات',
      'مأكولات بحرية',
      'دجاج',
      'لحوم',
      'مقبلات',
    ];
  }

  /// الحصول على اقتراحات ذكية
  List<String> _getSmartSuggestions(String query) {
    Map<String, List<String>> smartSuggestions = {
      'برجر': ['برجر لحم', 'برجر دجاج', 'برجر جبن'],
      'بيتزا': ['بيتزا مارجريتا', 'بيتزا بيبروني', 'بيتزا خضار'],
      'شاورما': ['شاورما لحم', 'شاورما دجاج', 'شاورما مشكل'],
      'مشروب': ['عصائر طبيعية', 'مشروبات غازية', 'قهوة'],
      'حلو': ['كيك', 'آيس كريم', 'حلويات شرقية'],
    };

    for (String key in smartSuggestions.keys) {
      if (query.toLowerCase().contains(key)) {
        return smartSuggestions[key]!;
      }
    }

    return [];
  }

  /// محاكاة المنتجات (في التطبيق الحقيقي ستأتي من API)
  List<Product> _getMockProducts() {
    return [
      Product(
        id: '1',
        name: 'برجر لحم كلاسيكي',
        description: 'برجر لحم طازج مع الخضار',
        price: 25.0,
        imageUrl: 'assets/images/burger1.jpg',
        category: 'برجر',
        rating: 4.5,
        isAvailable: true,
        createdAt: DateTime.now().subtract(Duration(days: 1)),
        orderCount: 150,
      ),
      Product(
        id: '2',
        name: 'بيتزا مارجريتا',
        description: 'بيتزا كلاسيكية بالجبن والطماطم',
        price: 35.0,
        imageUrl: 'assets/images/pizza1.jpg',
        category: 'بيتزا',
        rating: 4.8,
        isAvailable: true,
        createdAt: DateTime.now().subtract(Duration(days: 2)),
        orderCount: 200,
      ),
      Product(
        id: '3',
        name: 'شاورما دجاج',
        description: 'شاورما دجاج طازجة مع الخضار',
        price: 18.0,
        imageUrl: 'assets/images/shawarma1.jpg',
        category: 'شاورما',
        rating: 4.3,
        isAvailable: true,
        createdAt: DateTime.now().subtract(Duration(days: 3)),
        orderCount: 120,
      ),
      Product(
        id: '4',
        name: 'عصير برتقال طبيعي',
        description: 'عصير برتقال طازج 100%',
        price: 12.0,
        imageUrl: 'assets/images/juice1.jpg',
        category: 'مشروبات',
        rating: 4.7,
        isAvailable: true,
        createdAt: DateTime.now().subtract(Duration(days: 1)),
        orderCount: 80,
      ),
      Product(
        id: '5',
        name: 'كيك شوكولاتة',
        description: 'كيك شوكولاتة لذيذ',
        price: 45.0,
        imageUrl: 'assets/images/cake1.jpg',
        category: 'حلويات',
        rating: 4.9,
        isAvailable: false,
        createdAt: DateTime.now().subtract(Duration(days: 5)),
        orderCount: 95,
      ),
    ];
  }

  /// محاكاة المتاجر
  List<Store> _getMockStores() {
    return [
      Store(
        id: '1',
        name: 'مطعم البرجر الذهبي',
        description: 'أفضل برجر في المدينة',
        category: 'وجبات سريعة',
        rating: 4.5,
        image: 'assets/images/store1.jpg',
        isOpen: true,
      ),
      Store(
        id: '2',
        name: 'بيتزا إيطاليا',
        description: 'بيتزا إيطالية أصلية',
        category: 'إيطالي',
        rating: 4.7,
        image: 'assets/images/store2.jpg',
        isOpen: true,
      ),
    ];
  }
}

/// خيارات الفلترة
class FilterOptions {
  final double? minPrice;
  final double? maxPrice;
  final List<String> categories;
  final double? minRating;
  final bool availableOnly;

  FilterOptions({
    this.minPrice,
    this.maxPrice,
    this.categories = const [],
    this.minRating,
    this.availableOnly = false,
  });
}

/// خيارات الترتيب
enum SortOption {
  priceAsc, // السعر من الأقل للأعلى
  priceDesc, // السعر من الأعلى للأقل
  ratingDesc, // التقييم من الأعلى للأقل
  nameAsc, // الاسم أبجدياً
  newest, // الأحدث
  popular, // الأكثر شعبية
}
