import 'package:flutter/material.dart';
import 'package:curved_navigation_bar/curved_navigation_bar.dart';
import 'package:test2/utils/AppColors.dart';
import '../utils/ResponsiveHelper.dart';

class CustomBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CurvedNavigationBar(
      backgroundColor: AppColors.transparentColor,
      color: AppColors.primaryColor,
      buttonBackgroundColor: AppColors.primaryColor,
      height: ResponsiveHelper.responsiveSize(
        context,
        mobile: 60,
        tablet: 70,
        desktop: 80,
      ),
      animationDuration: Duration(milliseconds: 300),
      animationCurve: Curves.easeInOut,
      index: currentIndex,
      onTap: onTap,
      items: [
        _buildNavItem(
          context: context,
          icon: Icons.person,
          isSelected: currentIndex == 0,
        ),
        _buildNavItem(
          context: context,
          icon: Icons.store,
          isSelected: currentIndex == 1,
        ),
        _buildNavItem(
          context: context,
          icon: Icons.home,
          isSelected: currentIndex == 2,
        ),
        _buildNavItem(
          context: context,
          icon: Icons.shopping_cart,
          isSelected: currentIndex == 3,
        ),
        _buildNavItem(
          context: context,
          icon: Icons.list_alt,
          isSelected: currentIndex == 4,
        ),
      ],
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required IconData icon,
    required bool isSelected,
  }) {
    return Container(
      padding: ResponsiveHelper.responsiveEdgeInsets(
        context,
        mobile: 6,
        tablet: 8,
        desktop: 10,
      ),
      child: Icon(
        icon,
        size: ResponsiveHelper.responsiveIconSize(
          context,
          mobile: isSelected ? 30 : 26,
          tablet: isSelected ? 35 : 30,
          desktop: isSelected ? 40 : 35,
        ),
        color: AppColors.whiteColor,
      ),
    );
  }
}

// شريط تنقل بديل بتصميم مسطح
class FlatBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const FlatBottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: ResponsiveHelper.responsiveSize(
        context,
        mobile: 60,
        tablet: 70,
        desktop: 80,
      ),
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        // borderRadius: BorderRadius.only(
        //   topLeft: Radius.circular(AppDimensions.borderRadiusLarge),
        //   topRight: Radius.circular(AppDimensions.borderRadiusLarge),
        // ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 10,
            spreadRadius: 3,
            offset: Offset(0, -3),
          ),
        ],
      ),
      child: Expanded(
          child: SingleChildScrollView(
              child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildFlatNavItem(
            context: context,
            icon: Icons.person,
            label: "الملف الشخصي",
            index: 0,
          ),
          _buildFlatNavItem(
            context: context,
            icon: Icons.store,
            label: "المتاجر",
            index: 1,
          ),
          _buildFlatNavItem(
            context: context,
            icon: Icons.home,
            label: "الرئيسية",
            index: 2,
          ),
          _buildFlatNavItem(
            context: context,
            icon: Icons.shopping_cart,
            label: "السلة",
            index: 3,
          ),
          _buildFlatNavItem(
            context: context,
            icon: Icons.list_alt,
            label: "الطلبات",
            index: 4,
          ),
        ],
      ))),
    );
  }

  Widget _buildFlatNavItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required int index,
  }) {
    bool isSelected = currentIndex == index;

    return GestureDetector(
      onTap: () => onTap(index),
      child: Container(
        padding: ResponsiveHelper.responsiveEdgeInsets(
          context,
          mobile: 8,
          tablet: 10,
          desktop: 12,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: ResponsiveHelper.responsiveEdgeInsets(
                context,
                mobile: 6,
                tablet: 8,
                desktop: 10,
              ),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.primaryColor.withOpacity(0.1)
                    : AppColors.transparentColor,
                borderRadius: BorderRadius.circular(
                  ResponsiveHelper.responsiveBorderRadius(
                    context,
                    mobile: 6,
                    tablet: 8,
                    desktop: 10,
                  ),
                ),
              ),
              child: Icon(
                icon,
                color: isSelected
                    ? AppColors.primaryColor
                    : AppColors.textSecondaryColor,
                size: ResponsiveHelper.responsiveIconSize(
                  context,
                  mobile: isSelected ? 24 : 20,
                  tablet: isSelected ? 28 : 24,
                  desktop: isSelected ? 32 : 28,
                ),
              ),
            ),
            SizedBox(
              height: ResponsiveHelper.responsiveSpacing(
                context,
                mobile: 2,
                tablet: 3,
                desktop: 4,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: ResponsiveHelper.responsiveFontSize(
                  context,
                  mobile: 9,
                  tablet: 10,
                  desktop: 11,
                ),
                color: isSelected
                    ? AppColors.primaryColor
                    : AppColors.textSecondaryColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// مساعد للتنقل بين الصفحات
class NavigationHelper {
  static void navigateToPage(BuildContext context, int index) {
    // الحصول على الـ route الحالي
    final currentRoute = ModalRoute.of(context)?.settings.name;

    switch (index) {
      case 0:
        // تجنب إعادة التنقل لنفس الصفحة
        if (currentRoute != "/profile") {
          Navigator.pushReplacementNamed(context, "/profile");
        }
        break;
      case 1:
        if (currentRoute != "/stores") {
          Navigator.pushReplacementNamed(context, "/stores");
        }
        break;
      case 2:
        if (currentRoute != "/home" && currentRoute != "/") {
          Navigator.pushReplacementNamed(context, "/home");
        }
        break;
      case 3:
        if (currentRoute != "/cart") {
          Navigator.pushReplacementNamed(context, "/cart");
        }
        break;
      case 4:
        if (currentRoute != "/orders") {
          Navigator.pushReplacementNamed(context, "/orders");
        }
        break;
    }
  }

  static int getCurrentIndex(String routeName) {
    switch (routeName) {
      case "/profile":
        return 0;
      case "/stores":
        return 1;
      case "/home":
        return 2;
      case "/":
        return 2;
      case "/cart":
        return 3;
      case "cartPage":
        return 3;
      case "/orders":
        return 4;
      default:
        return 2; // الصفحة الرئيسية كافتراضي
    }
  }
}
