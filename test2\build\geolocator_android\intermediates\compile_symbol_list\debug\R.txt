int anim fragment_fast_out_extra_slow_in 0x0
int animator fragment_close_enter 0x0
int animator fragment_close_exit 0x0
int animator fragment_fade_enter 0x0
int animator fragment_fade_exit 0x0
int animator fragment_open_enter 0x0
int animator fragment_open_exit 0x0
int attr activityAction 0x0
int attr activityName 0x0
int attr alpha 0x0
int attr alwaysExpand 0x0
int attr animationBackgroundColor 0x0
int attr buttonSize 0x0
int attr circleCrop 0x0
int attr clearTop 0x0
int attr colorScheme 0x0
int attr finishPrimaryWithPlaceholder 0x0
int attr finishPrimaryWithSecondary 0x0
int attr finishSecondaryWithPrimary 0x0
int attr font 0x0
int attr fontProviderAuthority 0x0
int attr fontProviderCerts 0x0
int attr fontProviderFetchStrategy 0x0
int attr fontProviderFetchTimeout 0x0
int attr fontProviderPackage 0x0
int attr fontProviderQuery 0x0
int attr fontProviderSystemFontFamily 0x0
int attr fontStyle 0x0
int attr fontVariationSettings 0x0
int attr fontWeight 0x0
int attr imageAspectRatio 0x0
int attr imageAspectRatioAdjust 0x0
int attr lStar 0x0
int attr nestedScrollViewStyle 0x0
int attr placeholderActivityName 0x0
int attr primaryActivityName 0x0
int attr queryPatterns 0x0
int attr scopeUris 0x0
int attr secondaryActivityAction 0x0
int attr secondaryActivityName 0x0
int attr shortcutMatchRequired 0x0
int attr splitLayoutDirection 0x0
int attr splitMaxAspectRatioInLandscape 0x0
int attr splitMaxAspectRatioInPortrait 0x0
int attr splitMinHeightDp 0x0
int attr splitMinSmallestWidthDp 0x0
int attr splitMinWidthDp 0x0
int attr splitRatio 0x0
int attr stickyPlaceholder 0x0
int attr tag 0x0
int attr ttcIndex 0x0
int color androidx_core_ripple_material_light 0x0
int color androidx_core_secondary_text_default_material_light 0x0
int color call_notification_answer_color 0x0
int color call_notification_decline_color 0x0
int color common_google_signin_btn_text_dark 0x0
int color common_google_signin_btn_text_dark_default 0x0
int color common_google_signin_btn_text_dark_disabled 0x0
int color common_google_signin_btn_text_dark_focused 0x0
int color common_google_signin_btn_text_dark_pressed 0x0
int color common_google_signin_btn_text_light 0x0
int color common_google_signin_btn_text_light_default 0x0
int color common_google_signin_btn_text_light_disabled 0x0
int color common_google_signin_btn_text_light_focused 0x0
int color common_google_signin_btn_text_light_pressed 0x0
int color common_google_signin_btn_tint 0x0
int color notification_action_color_filter 0x0
int color notification_icon_bg_color 0x0
int color ripple_material_light 0x0
int color secondary_text_default_material_light 0x0
int dimen compat_button_inset_horizontal_material 0x0
int dimen compat_button_inset_vertical_material 0x0
int dimen compat_button_padding_horizontal_material 0x0
int dimen compat_button_padding_vertical_material 0x0
int dimen compat_control_corner_material 0x0
int dimen compat_notification_large_icon_max_height 0x0
int dimen compat_notification_large_icon_max_width 0x0
int dimen notification_action_icon_size 0x0
int dimen notification_action_text_size 0x0
int dimen notification_big_circle_margin 0x0
int dimen notification_content_margin_start 0x0
int dimen notification_large_icon_height 0x0
int dimen notification_large_icon_width 0x0
int dimen notification_main_column_padding_top 0x0
int dimen notification_media_narrow_margin 0x0
int dimen notification_right_icon_size 0x0
int dimen notification_right_side_padding_top 0x0
int dimen notification_small_icon_background_padding 0x0
int dimen notification_small_icon_size_as_large 0x0
int dimen notification_subtext_size 0x0
int dimen notification_top_pad 0x0
int dimen notification_top_pad_large_text 0x0
int drawable common_full_open_on_phone 0x0
int drawable common_google_signin_btn_icon_dark 0x0
int drawable common_google_signin_btn_icon_dark_focused 0x0
int drawable common_google_signin_btn_icon_dark_normal 0x0
int drawable common_google_signin_btn_icon_dark_normal_background 0x0
int drawable common_google_signin_btn_icon_disabled 0x0
int drawable common_google_signin_btn_icon_light 0x0
int drawable common_google_signin_btn_icon_light_focused 0x0
int drawable common_google_signin_btn_icon_light_normal 0x0
int drawable common_google_signin_btn_icon_light_normal_background 0x0
int drawable common_google_signin_btn_text_dark 0x0
int drawable common_google_signin_btn_text_dark_focused 0x0
int drawable common_google_signin_btn_text_dark_normal 0x0
int drawable common_google_signin_btn_text_dark_normal_background 0x0
int drawable common_google_signin_btn_text_disabled 0x0
int drawable common_google_signin_btn_text_light 0x0
int drawable common_google_signin_btn_text_light_focused 0x0
int drawable common_google_signin_btn_text_light_normal 0x0
int drawable common_google_signin_btn_text_light_normal_background 0x0
int drawable googleg_disabled_color_18 0x0
int drawable googleg_standard_color_18 0x0
int drawable ic_call_answer 0x0
int drawable ic_call_answer_low 0x0
int drawable ic_call_answer_video 0x0
int drawable ic_call_answer_video_low 0x0
int drawable ic_call_decline 0x0
int drawable ic_call_decline_low 0x0
int drawable notification_action_background 0x0
int drawable notification_bg 0x0
int drawable notification_bg_low 0x0
int drawable notification_bg_low_normal 0x0
int drawable notification_bg_low_pressed 0x0
int drawable notification_bg_normal 0x0
int drawable notification_bg_normal_pressed 0x0
int drawable notification_icon_background 0x0
int drawable notification_oversize_large_icon_bg 0x0
int drawable notification_template_icon_bg 0x0
int drawable notification_template_icon_low_bg 0x0
int drawable notification_tile_bg 0x0
int drawable notify_panel_notification_icon_bg 0x0
int id accessibility_action_clickable_span 0x0
int id accessibility_custom_action_0 0x0
int id accessibility_custom_action_1 0x0
int id accessibility_custom_action_10 0x0
int id accessibility_custom_action_11 0x0
int id accessibility_custom_action_12 0x0
int id accessibility_custom_action_13 0x0
int id accessibility_custom_action_14 0x0
int id accessibility_custom_action_15 0x0
int id accessibility_custom_action_16 0x0
int id accessibility_custom_action_17 0x0
int id accessibility_custom_action_18 0x0
int id accessibility_custom_action_19 0x0
int id accessibility_custom_action_2 0x0
int id accessibility_custom_action_20 0x0
int id accessibility_custom_action_21 0x0
int id accessibility_custom_action_22 0x0
int id accessibility_custom_action_23 0x0
int id accessibility_custom_action_24 0x0
int id accessibility_custom_action_25 0x0
int id accessibility_custom_action_26 0x0
int id accessibility_custom_action_27 0x0
int id accessibility_custom_action_28 0x0
int id accessibility_custom_action_29 0x0
int id accessibility_custom_action_3 0x0
int id accessibility_custom_action_30 0x0
int id accessibility_custom_action_31 0x0
int id accessibility_custom_action_4 0x0
int id accessibility_custom_action_5 0x0
int id accessibility_custom_action_6 0x0
int id accessibility_custom_action_7 0x0
int id accessibility_custom_action_8 0x0
int id accessibility_custom_action_9 0x0
int id action_container 0x0
int id action_divider 0x0
int id action_image 0x0
int id action_text 0x0
int id actions 0x0
int id adjacent 0x0
int id adjust_height 0x0
int id adjust_width 0x0
int id always 0x0
int id alwaysAllow 0x0
int id alwaysDisallow 0x0
int id androidx_window_activity_scope 0x0
int id async 0x0
int id auto 0x0
int id blocking 0x0
int id bottomToTop 0x0
int id chronometer 0x0
int id dark 0x0
int id dialog_button 0x0
int id edit_text_id 0x0
int id forever 0x0
int id fragment_container_view_tag 0x0
int id hide_ime_id 0x0
int id icon 0x0
int id icon_group 0x0
int id icon_only 0x0
int id info 0x0
int id italic 0x0
int id light 0x0
int id line1 0x0
int id line3 0x0
int id locale 0x0
int id ltr 0x0
int id never 0x0
int id none 0x0
int id normal 0x0
int id notification_background 0x0
int id notification_main_column 0x0
int id notification_main_column_container 0x0
int id report_drawn 0x0
int id right_icon 0x0
int id right_side 0x0
int id rtl 0x0
int id special_effects_controller_view_tag 0x0
int id standard 0x0
int id tag_accessibility_actions 0x0
int id tag_accessibility_clickable_spans 0x0
int id tag_accessibility_heading 0x0
int id tag_accessibility_pane_title 0x0
int id tag_on_apply_window_listener 0x0
int id tag_on_receive_content_listener 0x0
int id tag_on_receive_content_mime_types 0x0
int id tag_screen_reader_focusable 0x0
int id tag_state_description 0x0
int id tag_transition_group 0x0
int id tag_unhandled_key_event_manager 0x0
int id tag_unhandled_key_listeners 0x0
int id tag_window_insets_animation_callback 0x0
int id text 0x0
int id text2 0x0
int id time 0x0
int id title 0x0
int id topToBottom 0x0
int id view_tree_lifecycle_owner 0x0
int id view_tree_on_back_pressed_dispatcher_owner 0x0
int id view_tree_saved_state_registry_owner 0x0
int id view_tree_view_model_store_owner 0x0
int id visible_removing_fragment_view_tag 0x0
int id wide 0x0
int integer google_play_services_version 0x0
int integer status_bar_notification_info_maxnum 0x0
int layout custom_dialog 0x0
int layout ime_base_split_test_activity 0x0
int layout ime_secondary_split_test_activity 0x0
int layout notification_action 0x0
int layout notification_action_tombstone 0x0
int layout notification_template_custom_big 0x0
int layout notification_template_icon_group 0x0
int layout notification_template_part_chronometer 0x0
int layout notification_template_part_time 0x0
int string androidx_startup 0x0
int string call_notification_answer_action 0x0
int string call_notification_answer_video_action 0x0
int string call_notification_decline_action 0x0
int string call_notification_hang_up_action 0x0
int string call_notification_incoming_text 0x0
int string call_notification_ongoing_text 0x0
int string call_notification_screening_text 0x0
int string common_google_play_services_enable_button 0x0
int string common_google_play_services_enable_text 0x0
int string common_google_play_services_enable_title 0x0
int string common_google_play_services_install_button 0x0
int string common_google_play_services_install_text 0x0
int string common_google_play_services_install_title 0x0
int string common_google_play_services_notification_channel_name 0x0
int string common_google_play_services_notification_ticker 0x0
int string common_google_play_services_unknown_issue 0x0
int string common_google_play_services_unsupported_text 0x0
int string common_google_play_services_update_button 0x0
int string common_google_play_services_update_text 0x0
int string common_google_play_services_update_title 0x0
int string common_google_play_services_updating_text 0x0
int string common_google_play_services_wear_update_text 0x0
int string common_open_on_phone 0x0
int string common_signin_button_text 0x0
int string common_signin_button_text_long 0x0
int string status_bar_notification_info_overflow 0x0
int style TextAppearance_Compat_Notification 0x0
int style TextAppearance_Compat_Notification_Info 0x0
int style TextAppearance_Compat_Notification_Line2 0x0
int style TextAppearance_Compat_Notification_Time 0x0
int style TextAppearance_Compat_Notification_Title 0x0
int style Widget_Compat_NotificationActionContainer 0x0
int style Widget_Compat_NotificationActionText 0x0
int[] styleable ActivityFilter { 0x0, 0x0 }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityRule { 0x0, 0x0 }
int styleable ActivityRule_alwaysExpand 0
int styleable ActivityRule_tag 1
int[] styleable Capability { 0x0, 0x0 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x0, 0x101031f, 0x10101a5, 0x1010647, 0x0 }
int styleable ColorStateListItem_alpha 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_color 2
int styleable ColorStateListItem_android_lStar 3
int styleable ColorStateListItem_lStar 4
int[] styleable FontFamily { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontStyle 1
int styleable FontFamilyFont_android_fontVariationSettings 2
int styleable FontFamilyFont_android_fontWeight 3
int styleable FontFamilyFont_android_ttcIndex 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x10100d0, 0x1010003, 0x10100d1 }
int styleable Fragment_android_id 0
int styleable Fragment_android_name 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x1010003, 0x10100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
int styleable GradientColor_android_centerColor 0
int styleable GradientColor_android_centerX 1
int styleable GradientColor_android_centerY 2
int styleable GradientColor_android_endColor 3
int styleable GradientColor_android_endX 4
int styleable GradientColor_android_endY 5
int styleable GradientColor_android_gradientRadius 6
int styleable GradientColor_android_startColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_tileMode 10
int styleable GradientColor_android_type 11
int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LoadingImageView { 0x0, 0x0, 0x0 }
int styleable LoadingImageView_circleCrop 0
int styleable LoadingImageView_imageAspectRatio 1
int styleable LoadingImageView_imageAspectRatioAdjust 2
int[] styleable SignInButton { 0x0, 0x0, 0x0 }
int styleable SignInButton_buttonSize 0
int styleable SignInButton_colorScheme 1
int styleable SignInButton_scopeUris 2
int[] styleable SplitPairFilter { 0x0, 0x0, 0x0 }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable SplitPairRule_animationBackgroundColor 0
int styleable SplitPairRule_clearTop 1
int styleable SplitPairRule_finishPrimaryWithSecondary 2
int styleable SplitPairRule_finishSecondaryWithPrimary 3
int styleable SplitPairRule_splitLayoutDirection 4
int styleable SplitPairRule_splitMaxAspectRatioInLandscape 5
int styleable SplitPairRule_splitMaxAspectRatioInPortrait 6
int styleable SplitPairRule_splitMinHeightDp 7
int styleable SplitPairRule_splitMinSmallestWidthDp 8
int styleable SplitPairRule_splitMinWidthDp 9
int styleable SplitPairRule_splitRatio 10
int styleable SplitPairRule_tag 11
int[] styleable SplitPlaceholderRule { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable SplitPlaceholderRule_animationBackgroundColor 0
int styleable SplitPlaceholderRule_finishPrimaryWithPlaceholder 1
int styleable SplitPlaceholderRule_placeholderActivityName 2
int styleable SplitPlaceholderRule_splitLayoutDirection 3
int styleable SplitPlaceholderRule_splitMaxAspectRatioInLandscape 4
int styleable SplitPlaceholderRule_splitMaxAspectRatioInPortrait 5
int styleable SplitPlaceholderRule_splitMinHeightDp 6
int styleable SplitPlaceholderRule_splitMinSmallestWidthDp 7
int styleable SplitPlaceholderRule_splitMinWidthDp 8
int styleable SplitPlaceholderRule_splitRatio 9
int styleable SplitPlaceholderRule_stickyPlaceholder 10
int styleable SplitPlaceholderRule_tag 11
