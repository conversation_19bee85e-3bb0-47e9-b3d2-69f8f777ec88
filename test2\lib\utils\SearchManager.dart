import 'DataManager.dart';

class SearchManager {
  // Singleton pattern
  static final SearchManager _instance = SearchManager._internal();
  factory SearchManager() => _instance;
  SearchManager._internal();

  // Cache للبحث
  static final Map<String, List<Map<String, dynamic>>> _searchCache = {};

  // البحث في المنتجات
  static List<Map<String, dynamic>> searchItems(String query, {String? category}) {
    if (query.isEmpty) {
      return category != null 
          ? DataManager.getFilteredItems(category)
          : DataManager.allItems;
    }

    final cacheKey = 'items_${query}_${category ?? 'all'}';
    if (_searchCache.containsKey(cacheKey)) {
      return _searchCache[cacheKey]!;
    }

    List<Map<String, dynamic>> items = category != null 
        ? DataManager.getFilteredItems(category)
        : DataManager.allItems;

    List<Map<String, dynamic>> results = items.where((item) {
      final name = item['name']?.toString().toLowerCase() ?? '';
      final description = item['description']?.toString().toLowerCase() ?? '';
      final categoryName = item['category']?.toString().toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();

      return name.contains(searchQuery) || 
             description.contains(searchQuery) ||
             categoryName.contains(searchQuery);
    }).toList();

    _searchCache[cacheKey] = results;
    return results;
  }

  // البحث في المتاجر
  static List<Map<String, dynamic>> searchStores(String query, {String? category}) {
    if (query.isEmpty) {
      return category != null 
          ? DataManager.getFilteredStores(category)
          : DataManager.allStores;
    }

    final cacheKey = 'stores_${query}_${category ?? 'all'}';
    if (_searchCache.containsKey(cacheKey)) {
      return _searchCache[cacheKey]!;
    }

    List<Map<String, dynamic>> stores = category != null 
        ? DataManager.getFilteredStores(category)
        : DataManager.allStores;

    List<Map<String, dynamic>> results = stores.where((store) {
      final name = store['name']?.toString().toLowerCase() ?? '';
      final categoryName = store['category']?.toString().toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();

      return name.contains(searchQuery) || 
             categoryName.contains(searchQuery);
    }).toList();

    _searchCache[cacheKey] = results;
    return results;
  }

  // البحث العام (للصفحة الرئيسية)
  static Map<String, List<Map<String, dynamic>>> searchGeneral(String query) {
    if (query.isEmpty) {
      return {
        'items': DataManager.allItems,
        'stores': DataManager.allStores,
      };
    }

    final cacheKey = 'general_$query';
    if (_searchCache.containsKey(cacheKey)) {
      final cached = _searchCache[cacheKey]!;
      return {
        'items': cached.where((item) => item.containsKey('description')).toList(),
        'stores': cached.where((item) => !item.containsKey('description')).toList(),
      };
    }

    final items = searchItems(query);
    final stores = searchStores(query);

    // حفظ في الذاكرة المؤقتة
    _searchCache[cacheKey] = [...items, ...stores];

    return {
      'items': items,
      'stores': stores,
    };
  }

  // البحث في الطلبات
  static List<Map<String, dynamic>> searchOrders(String query, List<Map<String, dynamic>> orders) {
    if (query.isEmpty) return orders;

    final searchQuery = query.toLowerCase();
    return orders.where((order) {
      final orderId = order['id']?.toString().toLowerCase() ?? '';
      final status = order['status']?.toString().toLowerCase() ?? '';
      final storeName = order['storeName']?.toString().toLowerCase() ?? '';
      final items = order['items']?.toString().toLowerCase() ?? '';

      return orderId.contains(searchQuery) || 
             status.contains(searchQuery) ||
             storeName.contains(searchQuery) ||
             items.contains(searchQuery);
    }).toList();
  }

  // البحث في المفضلة
  static List<Map<String, dynamic>> searchFavorites(String query, List<Map<String, dynamic>> favorites) {
    if (query.isEmpty) return favorites;

    final searchQuery = query.toLowerCase();
    return favorites.where((favorite) {
      final name = favorite['name']?.toString().toLowerCase() ?? '';
      final type = favorite['type']?.toString().toLowerCase() ?? '';
      final category = favorite['category']?.toString().toLowerCase() ?? '';

      return name.contains(searchQuery) || 
             type.contains(searchQuery) ||
             category.contains(searchQuery);
    }).toList();
  }

  // البحث في العناوين
  static List<Map<String, dynamic>> searchAddresses(String query, List<Map<String, dynamic>> addresses) {
    if (query.isEmpty) return addresses;

    final searchQuery = query.toLowerCase();
    return addresses.where((address) {
      final title = address['title']?.toString().toLowerCase() ?? '';
      final address_text = address['address']?.toString().toLowerCase() ?? '';
      final area = address['area']?.toString().toLowerCase() ?? '';

      return title.contains(searchQuery) || 
             address_text.contains(searchQuery) ||
             area.contains(searchQuery);
    }).toList();
  }

  // تنظيف الذاكرة المؤقتة
  static void clearSearchCache() {
    _searchCache.clear();
  }

  // تنظيف ذاكرة مؤقتة محددة
  static void clearCacheForKey(String key) {
    _searchCache.removeWhere((cacheKey, value) => cacheKey.contains(key));
  }

  // دالة مساعدة لإزالة التشكيل العربي
  static String removeArabicDiacritics(String text) {
    return text.replaceAll(RegExp(r'[\u064B-\u0652\u0670\u0640]'), '');
  }

  // دالة بحث محسنة للنصوص العربية
  static bool arabicSearch(String text, String query) {
    final cleanText = removeArabicDiacritics(text.toLowerCase());
    final cleanQuery = removeArabicDiacritics(query.toLowerCase());
    return cleanText.contains(cleanQuery);
  }

  // البحث المتقدم مع دعم النصوص العربية
  static List<Map<String, dynamic>> advancedSearch(
    String query, 
    List<Map<String, dynamic>> data,
    List<String> searchFields
  ) {
    if (query.isEmpty) return data;

    return data.where((item) {
      for (String field in searchFields) {
        final fieldValue = item[field]?.toString() ?? '';
        if (arabicSearch(fieldValue, query)) {
          return true;
        }
      }
      return false;
    }).toList();
  }

  // اقتراحات البحث
  static List<String> getSearchSuggestions(String query, String type) {
    List<String> suggestions = [];
    
    switch (type) {
      case 'items':
        suggestions = DataManager.allItems
            .map((item) => item['name']?.toString() ?? '')
            .where((name) => name.toLowerCase().contains(query.toLowerCase()))
            .take(5)
            .toList();
        break;
      case 'stores':
        suggestions = DataManager.allStores
            .map((store) => store['name']?.toString() ?? '')
            .where((name) => name.toLowerCase().contains(query.toLowerCase()))
            .take(5)
            .toList();
        break;
      case 'categories':
        suggestions = DataManager.categories
            .map((category) => category['name']?.toString() ?? '')
            .where((name) => name.toLowerCase().contains(query.toLowerCase()))
            .take(5)
            .toList();
        break;
    }
    
    return suggestions;
  }
}
