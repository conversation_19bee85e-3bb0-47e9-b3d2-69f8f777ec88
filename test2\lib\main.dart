import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/material.dart';
import 'package:test2/pages/CartPage.dart';
import 'package:test2/pages/HomePages.dart';
import 'package:test2/pages/ItemsPages.dart';
import 'package:test2/pages/OrdersPage.dart';
import 'package:test2/pages/OrderDetailsPage.dart';
import 'package:test2/pages/OrderTrackingPage.dart';
import 'package:test2/pages/StoresPage.dart';
import 'package:test2/pages/SplashScreen.dart';
import 'package:test2/pages/ProfilePage.dart';
import 'package:test2/pages/SettingsPage.dart';
import 'package:test2/pages/LoginPage.dart';
import 'package:test2/pages/RegisterPage.dart';
import 'package:test2/pages/FavoritesPage.dart';
import 'package:test2/pages/AddressesPage.dart';
import 'package:test2/pages/OffersPage.dart';
import 'package:test2/utils/AppColors.dart';
import 'package:test2/services/SettingsService.dart'; // استيراد خدمة الإعدادات
import 'package:test2/services/NotificationService.dart'; // استيراد خدمة الإشعارات
import 'package:test2/utils/PerformanceOptimizer.dart';

void main() async {
  WidgetsFlutterBinding
      .ensureInitialized(); // التأكد من تهيئة Flutter قبل تشغيل التطبيق

  // تطبيق تحسينات الأداء
  PerformanceOptimizer.optimizeApp();

  // تهيئة خدمة الإشعارات
  await NotificationService().initialize();

  // تحميل إعدادات التطبيق من التخزين المحلي
  final settingsService = SettingsService(); // إنشاء مثيل من خدمة الإعدادات
  await settingsService.loadSettings(); // تحميل جميع الإعدادات المحفوظة
  print('تم تحميل إعدادات التطبيق بنجاح'); // طباعة رسالة تأكيد التحميل

  runApp(MyApp()); // تشغيل التطبيق
}

// class MyApp extends StatelessWidget{
//   @override
//   Widget build(BuildContext context){
//     return MaterialApp(
//       debugShowCheckedModeBanner: false,
//       theme: ThemeData(
//         scaffoldBackgroundColor: Colors.white
//       ),
//       routes: {
//         "/":(context) => HomePage(),
//         "cartPage":(context) => CartPage()
//       },
//     );
//   }
// }

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: Size(360, 690), // مقاس التصميم الأساسي
      builder: (context, child) {
        return MaterialApp(
          debugShowCheckedModeBanner: false,
          title: 'زاد',
          theme: ThemeData(
            scaffoldBackgroundColor: AppColors.whiteColor,
            primarySwatch: Colors.red,
            primaryColor: AppColors.primaryColor,
            fontFamily: 'Arial', // يمكن تغييرها لخط عربي
            // تكوين الأزرار للتأكد من الحد الأدنى للمس
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                minimumSize: Size(48, 48),
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
            // تكوين الحقول
            inputDecorationTheme: InputDecorationTheme(
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          builder: (context, widget) {
            // التأكد من أن النصوص لا تتأثر بشكل مفرط بإعدادات النظام
            return MediaQuery(
              data: MediaQuery.of(context).copyWith(
                textScaler: TextScaler.linear(
                  MediaQuery.of(context).textScaler.scale(1.0).clamp(0.8, 1.3),
                ),
              ),
              child: widget!,
            );
          },
          initialRoute: "/splash",
          routes: {
            "/splash": (context) => SplashScreen(),
            "/": (context) => HomePage(),
            "/home": (context) => HomePage(),
            "/cart": (context) => CartPage(),
            "cartPage": (context) => CartPage(),
            "/items": (context) => ItemsPages(),
            "itemsPage": (context) => ItemsPages(),
            "/orders": (context) => OrdersPage(),
            "/order-details": (context) => OrderDetailsPage(orderId: "001"),
            "/order-tracking": (context) => OrderTrackingPage(orderId: "001"),
            "/stores": (context) => StoresPage(),
            "/profile": (context) => ProfilePage(),
            "/settings": (context) => SettingsPage(),
            "/login": (context) => LoginPage(),
            "/register": (context) => RegisterPage(),
            "/favorites": (context) => FavoritesPage(),
            "/addresses": (context) => AddressesPage(),
            "/offers": (context) => OffersPage(),
          },
        );
      },
    );
  }
}
