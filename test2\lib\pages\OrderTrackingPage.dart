import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:test2/utils/AppColors.dart';
import 'package:test2/services/OrderTrackingService.dart';
import 'package:test2/services/LocationService.dart';
import 'package:test2/models/OrderTrackingData.dart';
import 'package:test2/models/LocationPermissionResult.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:async';

class OrderTrackingPage extends StatefulWidget {
  final String orderId;

  OrderTrackingPage({required this.orderId});

  @override
  _OrderTrackingPageState createState() => _OrderTrackingPageState();
}

class _OrderTrackingPageState extends State<OrderTrackingPage> {
  final OrderTrackingService _trackingService = OrderTrackingService();
  final LocationService _locationService = LocationService();

  GoogleMapController? _mapController;
  StreamSubscription<OrderTrackingData>? _trackingSubscription;

  OrderTrackingData? _currentTrackingData;
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _initializeTracking();
  }

  @override
  void dispose() {
    _trackingSubscription?.cancel();
    _trackingService.stopTracking();
    super.dispose();
  }

  /// تهيئة نظام التتبع
  Future<void> _initializeTracking() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      // التحقق من تفعيل GPS
      if (!await _checkGPSStatus()) {
        return;
      }

      // بدء تتبع الطلب
      Stream<OrderTrackingData>? trackingStream =
          await _trackingService.startTracking(widget.orderId);

      if (trackingStream != null) {
        _trackingSubscription = trackingStream.listen(
          _onTrackingUpdate,
          onError: _onTrackingError,
        );
      } else {
        setState(() {
          _errorMessage = 'فشل في بدء تتبع الطلب';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تهيئة التتبع: $e';
        _isLoading = false;
      });
    }
  }

  /// التحقق من حالة GPS وطلب الأذونات إذا لزم الأمر
  Future<bool> _checkGPSStatus() async {
    try {
      // طلب أذونات الموقع تلقائياً
      LocationPermissionResult result =
          await _locationService.requestLocationPermissionInteractive();

      if (result.success) {
        return true;
      } else {
        _showPermissionDialog(result);
        return false;
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في فحص GPS: $e';
        _isLoading = false;
      });
      return false;
    }
  }

  /// عرض حوار أذونات الموقع حسب نوع المشكلة
  void _showPermissionDialog(LocationPermissionResult result) {
    String title;
    IconData icon;
    Color iconColor;
    List<Widget> actions;

    if (result.needsSystemSettings) {
      // يحتاج لإعدادات النظام
      title = 'تفعيل خدمة الموقع';
      icon = Icons.location_off;
      iconColor = Colors.orange;
      actions = [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            Navigator.of(context).pop(); // العودة للصفحة السابقة
          },
          child: Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.of(context).pop();
            try {
              await Geolocator.openLocationSettings();
              // إعادة المحاولة بعد فتح الإعدادات
              Future.delayed(Duration(seconds: 2), () {
                _initializeTracking();
              });
            } catch (e) {
              print('خطأ في فتح إعدادات الموقع: $e');
            }
          },
          child: Text('فتح إعدادات الجهاز'),
        ),
      ];
    } else if (result.needsAppSettings) {
      // يحتاج لإعدادات التطبيق
      title = 'أذونات التطبيق';
      icon = Icons.settings_applications;
      iconColor = Colors.red;
      actions = [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            Navigator.of(context).pop(); // العودة للصفحة السابقة
          },
          child: Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.of(context).pop();
            try {
              await openAppSettings();
              // إعادة المحاولة بعد فتح الإعدادات
              Future.delayed(Duration(seconds: 2), () {
                _initializeTracking();
              });
            } catch (e) {
              print('خطأ في فتح إعدادات التطبيق: $e');
            }
          },
          child: Text('فتح إعدادات التطبيق'),
        ),
      ];
    } else {
      // خطأ عام
      title = 'خطأ في الأذونات';
      icon = Icons.error_outline;
      iconColor = Colors.red;
      actions = [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            Navigator.of(context).pop(); // العودة للصفحة السابقة
          },
          child: Text('إغلاق'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            _initializeTracking(); // إعادة المحاولة
          },
          child: Text('إعادة المحاولة'),
        ),
      ];
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(icon, color: iconColor),
            SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(
          result.message,
          style: TextStyle(height: 1.5),
        ),
        actions: actions,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
        centerTitle: true,
        title: Text(
          "تتبع الطلب #${widget.orderId}",
          style: AppTextStyles.appBarTitle,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.whiteColor),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          color: AppColors.backgroundColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppDimensions.borderRadiusXLarge),
            topRight: Radius.circular(AppDimensions.borderRadiusXLarge),
          ),
        ),
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingWidget();
    }

    if (_errorMessage.isNotEmpty) {
      return _buildErrorWidget();
    }

    return Column(
      children: [
        // الخريطة
        Expanded(
          flex: 3,
          child: _buildMapWidget(),
        ),
        // معلومات التتبع
        Expanded(
          flex: 2,
          child: _buildTrackingInfo(),
        ),
      ],
    );
  }

  /// معالج تحديث بيانات التتبع
  void _onTrackingUpdate(OrderTrackingData data) {
    setState(() {
      _currentTrackingData = data;
      _isLoading = false;
      _errorMessage = '';
    });

    _updateMapMarkers(data);
    _updateMapCamera(data);
  }

  /// معالج أخطاء التتبع
  void _onTrackingError(error) {
    setState(() {
      _errorMessage = 'خطأ في التتبع: $error';
      _isLoading = false;
    });
  }

  /// تحديث علامات الخريطة
  void _updateMapMarkers(OrderTrackingData data) {
    setState(() {
      _markers = {
        Marker(
          markerId: MarkerId('user'),
          position:
              LatLng(data.userPosition.latitude, data.userPosition.longitude),
          infoWindow: InfoWindow(title: 'موقعك'),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        ),
        Marker(
          markerId: MarkerId('driver'),
          position: LatLng(
              data.driverPosition.latitude, data.driverPosition.longitude),
          infoWindow: InfoWindow(title: 'السائق'),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        ),
      };
    });
  }

  /// تحديث كاميرا الخريطة
  void _updateMapCamera(OrderTrackingData data) {
    if (_mapController != null) {
      // حساب الحدود لإظهار كلا الموقعين
      double minLat = [data.userPosition.latitude, data.driverPosition.latitude]
          .reduce((a, b) => a < b ? a : b);
      double maxLat = [data.userPosition.latitude, data.driverPosition.latitude]
          .reduce((a, b) => a > b ? a : b);
      double minLng = [
        data.userPosition.longitude,
        data.driverPosition.longitude
      ].reduce((a, b) => a < b ? a : b);
      double maxLng = [
        data.userPosition.longitude,
        data.driverPosition.longitude
      ].reduce((a, b) => a > b ? a : b);

      _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(
          LatLngBounds(
            southwest: LatLng(minLat - 0.001, minLng - 0.001),
            northeast: LatLng(maxLat + 0.001, maxLng + 0.001),
          ),
          100.0,
        ),
      );
    }
  }

  /// بناء واجهة التحميل
  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppColors.primaryColor),
          SizedBox(height: 20.h),
          Text(
            'جاري تهيئة نظام التتبع...',
            style: AppTextStyles.bodyLarge,
          ),
        ],
      ),
    );
  }

  /// بناء واجهة الخطأ
  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80.sp,
              color: Colors.red,
            ),
            SizedBox(height: 20.h),
            Text(
              _errorMessage,
              style: AppTextStyles.bodyLarge,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 30.h),
            ElevatedButton(
              onPressed: _initializeTracking,
              child: Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء واجهة الخريطة
  Widget _buildMapWidget() {
    return Container(
      margin: EdgeInsets.all(AppDimensions.marginMedium),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 10,
            spreadRadius: 2,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        child: GoogleMap(
          onMapCreated: (GoogleMapController controller) {
            _mapController = controller;
          },
          initialCameraPosition: CameraPosition(
            target: LatLng(15.3694, 44.1910), // صنعاء كموقع افتراضي
            zoom: 14.0,
          ),
          markers: _markers,
          polylines: _polylines,
          myLocationEnabled: false,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: true,
          mapToolbarEnabled: false,
        ),
      ),
    );
  }

  /// بناء معلومات التتبع
  Widget _buildTrackingInfo() {
    if (_currentTrackingData == null) {
      return Center(
        child: Text(
          'لا توجد بيانات تتبع',
          style: AppTextStyles.bodyLarge,
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "معلومات التوصيل",
            style: AppTextStyles.titleLarge,
          ),
          SizedBox(height: 20.h),
          _buildInfoRow(
            "حالة الطلب",
            _currentTrackingData!.status,
            Icons.info_outline,
          ),
          SizedBox(height: 15.h),
          _buildInfoRow(
            "وقت الوصول المتوقع",
            _currentTrackingData!.formattedEstimatedTime,
            Icons.access_time,
          ),
          SizedBox(height: 15.h),
          _buildInfoRow(
            "المسافة المتبقية",
            _currentTrackingData!.formattedDistance,
            Icons.directions_car,
          ),
          SizedBox(height: 30.h),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // إضافة وظيفة الاتصال بالسائق
                  },
                  icon: Icon(Icons.phone, color: Colors.white),
                  label: Text("اتصال"),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    padding: EdgeInsets.symmetric(vertical: 15.h),
                  ),
                ),
              ),
              SizedBox(width: 15.w),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // إضافة وظيفة إرسال رسالة للسائق
                  },
                  icon: Icon(Icons.message, color: Colors.white),
                  label: Text("رسالة"),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    padding: EdgeInsets.symmetric(vertical: 15.h),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String title, String value, IconData icon) {
    return Row(
      textDirection: TextDirection.rtl,
      children: [
        Container(
          width: 50.w,
          height: 50.h,
          decoration: BoxDecoration(
            color: AppColors.primaryColor.withOpacity(0.1),
            borderRadius:
                BorderRadius.circular(AppDimensions.borderRadiusSmall),
          ),
          child: Icon(
            icon,
            color: AppColors.primaryColor,
            size: AppDimensions.iconSizeMedium,
          ),
        ),
        SizedBox(width: 15.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            textDirection: TextDirection.rtl,
            children: [
              Text(
                title,
                style: AppTextStyles.bodySmall,
              ),
              SizedBox(height: 5.h),
              Text(
                value,
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
