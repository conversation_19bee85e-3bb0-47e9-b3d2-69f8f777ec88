import 'package:flutter/material.dart';

class OrderTrackingPage extends StatelessWidget {
  final String orderId;
  
  OrderTrackingPage({required this.orderId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Color(0xFFC3243B),
        title: Text(
          "تتبع الطلب #$orderId",
          style: TextStyle(
            fontSize: 23,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            flex: 3,
            child: Container(
              color: Colors.grey[300],
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.map,
                      size: 100,
                      color: Color(0xFF4C53A5),
                    ),
                    SizedBox(height: 20),
                    Text(
                      "هنا ستظهر خريطة تتبع الطلب",
                      style: TextStyle(
                        fontSize: 18,
                        color: Color(0xFF4C53A5),
                      ),
                    ),
                    Text(
                      "يمكن استخدام Google Maps API",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            flex: 5,
            child: Container(
              padding: EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "معلومات التوصيل",
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF4C53A5),
                    ),
                  ),
                  SizedBox(height: 20),
                  _buildDeliveryInfo(
                    "وقت الوصول المتوقع",
                    "30-40 دقيقة",
                    Icons.access_time,
                  ),
                  SizedBox(height: 15),
                  _buildDeliveryInfo(
                    "المسافة المتبقية",
                    "3.5 كم",
                    Icons.directions_car,
                  ),
                  SizedBox(height: 15),
                  _buildDeliveryInfo(
                    "عنوان التوصيل",
                    "شارع الملك فهد، حي الورود، الرياض",
                    Icons.location_on,
                  ),
                  SizedBox(height: 20),
                  Container(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {},
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color(0xFF4C53A5),
                        padding: EdgeInsets.symmetric(vertical: 15),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: Text(
                        "الاتصال بالسائق",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryInfo(String title, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Color(0xFF4C53A5), size: 24),
        SizedBox(width: 15),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ],
    );
  }
}