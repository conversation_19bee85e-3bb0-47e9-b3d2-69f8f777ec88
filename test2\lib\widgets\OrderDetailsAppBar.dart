import 'package:flutter/material.dart';

class OrderDetailsAppBar extends StatelessWidget implements PreferredSizeWidget{
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color.fromARGB(255, 219, 55, 80),
      padding: EdgeInsets.only(top: 10,left: 25,right: 25,bottom: 5),
      margin: EdgeInsets.only(top: 15),
      child: Row(
        children: [
          
          Icon(
            Icons.help_outline,
            size: 30,
            color: Color.fromARGB(255, 245, 170, 73),
          ),
          Spacer(),
          Icon(
            Icons.favorite,
            size: 30,
            color: Color.fromARGB(255, 245, 170, 73),
          ),

          Spacer(),
          Padding(
            padding:EdgeInsets.only(right: 20),
            child: Text(
              "تفاصيل  الطلب",
              style: TextStyle(
                fontSize: 23,
                fontWeight: FontWeight.bold,
                color: Colors.white
              ),
            ), 
          ),
          
          InkWell(
            onTap: (){
              
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }else{
                Navigator.pushReplacementNamed(context, "/home");
              }
            },
            child: Icon(
              Icons.arrow_forward,
              size: 30,
              color: Colors.white,
            ),
          ),
          
        ],
      ),
    );
  }
  @override
  Size get preferredSize => Size.fromHeight(70);
}






