import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:test2/utils/ResponsiveHelper.dart';

class ResponsiveTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconPressed;
  final bool obscureText;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function(String?)? onSaved;
  final bool enabled;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final TextCapitalization textCapitalization;
  final TextDirection? textDirection;
  final bool readOnly;
  final VoidCallback? onTap;
  final FocusNode? focusNode;
  final Color? fillColor;
  final Color? borderColor;
  final Color? focusedBorderColor;
  final double? borderRadius;

  const ResponsiveTextField({
    Key? key,
    this.controller,
    this.labelText,
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
    this.obscureText = false,
    this.keyboardType,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onSaved,
    this.enabled = true,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.textCapitalization = TextCapitalization.none,
    this.textDirection,
    this.readOnly = false,
    this.onTap,
    this.focusNode,
    this.fillColor,
    this.borderColor,
    this.focusedBorderColor,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final fontSize = ResponsiveHelper.responsiveFontSize(
      context,
      mobile: 14,
      tablet: 16,
      desktop: 18,
    );

    final iconSize = ResponsiveHelper.responsiveIconSize(
      context,
      mobile: 20,
      tablet: 22,
      desktop: 24,
    );

    final padding = ResponsiveHelper.responsiveEdgeInsets(
      context,
      mobile: 16,
      tablet: 18,
      desktop: 20,
    );

    final radius = borderRadius ?? ResponsiveHelper.responsiveBorderRadius(
      context,
      mobile: 8,
      tablet: 10,
      desktop: 12,
    );

    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      validator: validator,
      onChanged: onChanged,
      onSaved: onSaved,
      enabled: enabled,
      maxLines: maxLines,
      minLines: minLines,
      maxLength: maxLength,
      textCapitalization: textCapitalization,
      textDirection: textDirection,
      readOnly: readOnly,
      onTap: onTap,
      focusNode: focusNode,
      style: TextStyle(
        fontSize: fontSize,
        color: enabled ? Colors.black87 : Colors.grey,
      ),
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        prefixIcon: prefixIcon != null
            ? Icon(
                prefixIcon,
                size: iconSize,
                color: Colors.grey.shade600,
              )
            : null,
        suffixIcon: suffixIcon != null
            ? IconButton(
                icon: Icon(
                  suffixIcon,
                  size: iconSize,
                ),
                onPressed: onSuffixIconPressed,
              )
            : null,
        filled: true,
        fillColor: fillColor ?? Colors.grey.shade50,
        contentPadding: padding,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius),
          borderSide: BorderSide(
            color: borderColor ?? Colors.grey.shade300,
            width: ResponsiveHelper.responsiveBorderWidth(context),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius),
          borderSide: BorderSide(
            color: borderColor ?? Colors.grey.shade300,
            width: ResponsiveHelper.responsiveBorderWidth(context),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius),
          borderSide: BorderSide(
            color: focusedBorderColor ?? Theme.of(context).primaryColor,
            width: ResponsiveHelper.responsiveBorderWidth(
              context,
              mobile: 2,
              tablet: 2.5,
              desktop: 3,
            ),
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius),
          borderSide: BorderSide(
            color: Colors.red,
            width: ResponsiveHelper.responsiveBorderWidth(context),
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius),
          borderSide: BorderSide(
            color: Colors.red,
            width: ResponsiveHelper.responsiveBorderWidth(
              context,
              mobile: 2,
              tablet: 2.5,
              desktop: 3,
            ),
          ),
        ),
        labelStyle: TextStyle(
          fontSize: fontSize * 0.9,
          color: Colors.grey.shade700,
        ),
        hintStyle: TextStyle(
          fontSize: fontSize * 0.9,
          color: Colors.grey.shade500,
        ),
        errorStyle: TextStyle(
          fontSize: fontSize * 0.8,
          color: Colors.red,
        ),
      ),
    );
  }
}

class ResponsiveDropdownField<T> extends StatelessWidget {
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final void Function(T?)? onChanged;
  final String? labelText;
  final String? hintText;
  final IconData? prefixIcon;
  final String? Function(T?)? validator;
  final bool enabled;
  final Color? fillColor;
  final Color? borderColor;
  final Color? focusedBorderColor;
  final double? borderRadius;

  const ResponsiveDropdownField({
    Key? key,
    this.value,
    required this.items,
    this.onChanged,
    this.labelText,
    this.hintText,
    this.prefixIcon,
    this.validator,
    this.enabled = true,
    this.fillColor,
    this.borderColor,
    this.focusedBorderColor,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final fontSize = ResponsiveHelper.responsiveFontSize(
      context,
      mobile: 14,
      tablet: 16,
      desktop: 18,
    );

    final iconSize = ResponsiveHelper.responsiveIconSize(
      context,
      mobile: 20,
      tablet: 22,
      desktop: 24,
    );

    final padding = ResponsiveHelper.responsiveEdgeInsets(
      context,
      mobile: 16,
      tablet: 18,
      desktop: 20,
    );

    final radius = borderRadius ?? ResponsiveHelper.responsiveBorderRadius(
      context,
      mobile: 8,
      tablet: 10,
      desktop: 12,
    );

    return DropdownButtonFormField<T>(
      value: value,
      items: items,
      onChanged: enabled ? onChanged : null,
      validator: validator,
      style: TextStyle(
        fontSize: fontSize,
        color: enabled ? Colors.black87 : Colors.grey,
      ),
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        prefixIcon: prefixIcon != null
            ? Icon(
                prefixIcon,
                size: iconSize,
                color: Colors.grey.shade600,
              )
            : null,
        filled: true,
        fillColor: fillColor ?? Colors.grey.shade50,
        contentPadding: padding,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius),
          borderSide: BorderSide(
            color: borderColor ?? Colors.grey.shade300,
            width: ResponsiveHelper.responsiveBorderWidth(context),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius),
          borderSide: BorderSide(
            color: borderColor ?? Colors.grey.shade300,
            width: ResponsiveHelper.responsiveBorderWidth(context),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius),
          borderSide: BorderSide(
            color: focusedBorderColor ?? Theme.of(context).primaryColor,
            width: ResponsiveHelper.responsiveBorderWidth(
              context,
              mobile: 2,
              tablet: 2.5,
              desktop: 3,
            ),
          ),
        ),
        labelStyle: TextStyle(
          fontSize: fontSize * 0.9,
          color: Colors.grey.shade700,
        ),
        hintStyle: TextStyle(
          fontSize: fontSize * 0.9,
          color: Colors.grey.shade500,
        ),
      ),
    );
  }
}
