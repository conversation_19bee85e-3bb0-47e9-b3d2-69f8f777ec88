# ميزة البحث النصي في تطبيق زاد اليمن
## Search Functionality Implementation

## 🔍 نظرة عامة
تم إضافة نظام بحث شامل ومتقدم لجميع واجهات التطبيق، مع دعم البحث النصي المتقدم والاقتراحات الذكية والفلترة السريعة.

## ✅ الميزات المضافة

### 🏗️ **1. مدير البحث (SearchManager.dart)**

```dart
class SearchManager {
  // Cache للبحث لتحسين الأداء
  static final Map<String, List<Map<String, dynamic>>> _searchCache = {};
  
  // البحث في المنتجات
  static List<Map<String, dynamic>> searchItems(String query, {String? category})
  
  // البحث في المتاجر
  static List<Map<String, dynamic>> searchStores(String query, {String? category})
  
  // البحث العام (للصفحة الرئيسية)
  static Map<String, List<Map<String, dynamic>>> searchGeneral(String query)
  
  // البحث في الطلبات
  static List<Map<String, dynamic>> searchOrders(String query, List<Map<String, dynamic>> orders)
  
  // البحث في المفضلة
  static List<Map<String, dynamic>> searchFavorites(String query, List<Map<String, dynamic>> favorites)
}
```

**الميزات الرئيسية:**
- ✅ **تخزين مؤقت ذكي** لتحسين الأداء
- ✅ **بحث متعدد الحقول** (الاسم، الوصف، التصنيف)
- ✅ **دعم النصوص العربية** مع إزالة التشكيل
- ✅ **اقتراحات البحث** الذكية
- ✅ **بحث متقدم** مع فلاتر

### 🎨 **2. مكون البحث (SearchWidget.dart)**

#### **مكون البحث الأساسي:**
```dart
SearchWidget(
  hintText: "ابحث عن متجر أو منتج...",
  onSearchChanged: (query) {
    setState(() {
      searchQuery = query;
    });
  },
  showSuggestions: true,
  suggestionType: 'items',
  suffixIcon: searchQuery.isNotEmpty ? Icons.clear : null,
)
```

#### **مكون البحث المبسط:**
```dart
SimpleSearchBar(
  hintText: "البحث...",
  onSearchChanged: (query) => _handleSearch(query),
  icon: Icons.search,
)
```

#### **مكون البحث المتقدم:**
```dart
AdvancedSearchWidget(
  onSearchChanged: (query, filters) => _handleAdvancedSearch(query, filters),
  categories: ["الكل", "مطاعم", "بقالة", "صيدليات"],
  hintText: "البحث المتقدم...",
)
```

**الميزات:**
- ✅ **اقتراحات فورية** أثناء الكتابة
- ✅ **تصميم متجاوب** وجميل
- ✅ **أيقونات تفاعلية** (بحث، مسح)
- ✅ **دعم الفلاتر** المتقدمة

## 📱 **التطبيق في الواجهات**

### 🏠 **الصفحة الرئيسية - البحث العام**

```dart
class _HomePageState extends State<HomePage> {
  String selectedCategory = "الكل";
  String searchQuery = "";
  bool isSearching = false;
  
  SearchWidget(
    hintText: "ابحث عن متجر أو منتج...",
    onSearchChanged: (query) {
      setState(() {
        searchQuery = query;
        isSearching = query.isNotEmpty;
      });
    },
    showSuggestions: true,
    suggestionType: 'items',
  ),
}
```

**الميزات:**
- 🔍 **بحث شامل** في المنتجات والمتاجر
- 🎯 **إخفاء التصنيفات** عند البحث
- 📊 **عرض نتائج البحث** بدلاً من المحتوى العادي
- 💡 **اقتراحات ذكية** للمنتجات والمتاجر

### 🛍️ **صفحة المتاجر - البحث المتخصص**

```dart
class _StoresPageState extends State<StoresPage> {
  String selectedCategory = "الكل";
  String searchQuery = "";
  
  SearchWidget(
    hintText: "ابحث عن متجر...",
    onSearchChanged: (query) {
      setState(() {
        searchQuery = query;
      });
    },
    showSuggestions: true,
    suggestionType: 'stores',
  ),
}
```

**الميزات:**
- 🏪 **بحث مخصص للمتاجر** فقط
- 🎯 **فلترة حسب التصنيف** والبحث معاً
- ⭐ **بحث في التقييمات** ووقت التوصيل
- 📍 **اقتراحات المتاجر** القريبة

### 📦 **صفحة الطلبات - البحث في الطلبات**

```dart
class _OrdersPageState extends State<OrdersPage> {
  String searchQuery = "";
  
  SearchWidget(
    hintText: "البحث في الطلبات...",
    onSearchChanged: (query) {
      setState(() {
        searchQuery = query;
      });
    },
    suffixIcon: searchQuery.isNotEmpty ? Icons.clear : null,
  ),
}
```

**الميزات:**
- 📋 **بحث في رقم الطلب** والحالة
- 🏪 **بحث في اسم المتجر**
- 📅 **بحث في التاريخ** والوقت
- 💰 **بحث في المبلغ** والعناصر

### ❤️ **صفحة المفضلة - البحث المزدوج**

```dart
class _FavoritesPageState extends State<FavoritesPage> {
  int selectedTab = 0; // 0 للمتاجر، 1 للأطباق
  String searchQuery = "";
  
  SearchWidget(
    hintText: selectedTab == 0 
        ? "البحث في المتاجر المفضلة..." 
        : "البحث في الأطباق المفضلة...",
    onSearchChanged: (query) {
      setState(() {
        searchQuery = query;
      });
    },
  ),
}
```

**الميزات:**
- 🔄 **بحث متكيف** حسب التبويب المختار
- 🏪 **بحث في المتاجر المفضلة**
- 🍽️ **بحث في الأطباق المفضلة**
- 📱 **واجهة موحدة** للبحث

## 🎯 **أنواع البحث المدعومة**

### **1. البحث البسيط:**
```dart
// البحث في النص فقط
SearchManager.searchItems("برجر");
// النتيجة: جميع المنتجات التي تحتوي على "برجر"
```

### **2. البحث بالتصنيف:**
```dart
// البحث في تصنيف محدد
SearchManager.searchItems("برجر", category: "مطاعم");
// النتيجة: منتجات "برجر" في تصنيف "مطاعم" فقط
```

### **3. البحث المتقدم:**
```dart
// البحث مع فلاتر متعددة
SearchManager.advancedSearch(
  "برجر", 
  data, 
  ["name", "description", "category"]
);
```

### **4. البحث العربي المحسن:**
```dart
// إزالة التشكيل والبحث الذكي
SearchManager.arabicSearch("مطعم", "مَطْعَم");
// النتيجة: true (تطابق رغم التشكيل)
```

## 🚀 **الأداء والتحسينات**

### **1. التخزين المؤقت:**
```dart
// Cache للبحث لتجنب إعادة المعالجة
static final Map<String, List<Map<String, dynamic>>> _searchCache = {};

static List<Map<String, dynamic>> searchItems(String query) {
  final cacheKey = 'items_$query';
  if (_searchCache.containsKey(cacheKey)) {
    return _searchCache[cacheKey]!;
  }
  // معالجة البحث وحفظ النتيجة
}
```

### **2. البحث المتزايد:**
```dart
// البحث أثناء الكتابة مع تأخير
Timer? _debounceTimer;

void _onSearchChanged(String query) {
  _debounceTimer?.cancel();
  _debounceTimer = Timer(Duration(milliseconds: 300), () {
    // تنفيذ البحث
  });
}
```

### **3. تحسين الذاكرة:**
```dart
// تنظيف الذاكرة المؤقتة
static void clearSearchCache() {
  _searchCache.clear();
}

// تنظيف محدد
static void clearCacheForKey(String key) {
  _searchCache.removeWhere((cacheKey, value) => cacheKey.contains(key));
}
```

## 📊 **إحصائيات البحث**

### **البيانات المدعومة:**
- 📦 **14 منتج** في 8 تصنيفات
- 🏪 **8 متاجر** في 8 تصنيفات  
- 📋 **طلبات متعددة** مع حالات مختلفة
- ❤️ **مفضلة** للمتاجر والأطباق

### **حقول البحث:**
- 📝 **الاسم** (name)
- 📄 **الوصف** (description)  
- 🏷️ **التصنيف** (category)
- ⭐ **التقييم** (rating)
- ⏰ **وقت التوصيل** (deliveryTime)
- 💰 **السعر** (price)

## 🎨 **التصميم والواجهة**

### **شريط البحث:**
```dart
Container(
  decoration: BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(30),
    boxShadow: [
      BoxShadow(
        color: Colors.grey.withOpacity(0.1),
        spreadRadius: 1,
        blurRadius: 3,
        offset: Offset(0, 1),
      ),
    ],
  ),
  child: TextField(
    textAlign: TextAlign.right,
    decoration: InputDecoration(
      border: InputBorder.none,
      hintText: "ابحث هنا...",
      prefixIcon: Icon(Icons.search),
      suffixIcon: Icon(Icons.clear),
    ),
  ),
)
```

### **اقتراحات البحث:**
```dart
Container(
  decoration: BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(10),
    boxShadow: [...],
  ),
  child: Column(
    children: suggestions.map((suggestion) {
      return ListTile(
        leading: Icon(Icons.search),
        title: Text(suggestion),
        onTap: () => _selectSuggestion(suggestion),
      );
    }).toList(),
  ),
)
```

## 🔧 **كيفية الاستخدام**

### **للمطور:**
```dart
// إضافة بحث لصفحة جديدة
class MyPage extends StatefulWidget {
  String searchQuery = "";
  
  SearchWidget(
    hintText: "ابحث في صفحتي...",
    onSearchChanged: (query) {
      setState(() {
        searchQuery = query;
      });
    },
  ),
  
  // استخدام البحث
  final results = SearchManager.searchItems(searchQuery);
}
```

### **للمستخدم:**
1. **اكتب في شريط البحث** أي كلمة
2. **اختر من الاقتراحات** المعروضة
3. **شاهد النتائج** تظهر فوراً
4. **امسح البحث** بالضغط على X

## 📈 **النتائج المحققة**

### **قبل إضافة البحث:**
- 🔍 **لا يوجد بحث** في التطبيق
- 📱 **تصفح يدوي** للمحتوى فقط
- ⏰ **وقت أطول** للعثور على المطلوب
- 😕 **تجربة مستخدم محدودة**

### **بعد إضافة البحث:**
- ⚡ **بحث فوري** في جميع الصفحات
- 🎯 **نتائج دقيقة** ومفلترة
- 💡 **اقتراحات ذكية** أثناء الكتابة
- 😊 **تجربة مستخدم ممتازة**

## 🎉 **الميزات المتقدمة**

### **1. البحث الصوتي (مستقبلي):**
```dart
// يمكن إضافة البحث الصوتي
VoiceSearchWidget(
  onVoiceResult: (text) => _handleSearch(text),
)
```

### **2. البحث بالصورة (مستقبلي):**
```dart
// يمكن إضافة البحث بالصورة
ImageSearchWidget(
  onImageSelected: (image) => _searchByImage(image),
)
```

### **3. البحث الجغرافي:**
```dart
// البحث حسب الموقع
LocationSearchWidget(
  onLocationSelected: (location) => _searchNearby(location),
)
```

## 🚀 **كيفية التشغيل**

```bash
cd test2
flutter run
```

## 🎯 **الخلاصة**

تطبيق "زاد اليمن" أصبح الآن يحتوي على:
- 🔍 **نظام بحث شامل** في جميع الصفحات
- ⚡ **أداء محسن** مع التخزين المؤقت
- 🎨 **واجهة جميلة** ومتجاوبة
- 💡 **اقتراحات ذكية** للمستخدمين
- 🌐 **دعم النصوص العربية** الكامل

🎉 **ميزة البحث جاهزة وتعمل بكفاءة عالية في جميع أنحاء التطبيق!**
