import 'package:flutter/material.dart';
import '../services/CustomerDataService.dart';
import '../models/UserProfile.dart';
import '../models/Address.dart';

/// مزود حالة بيانات العميل الموحد
/// يدير جميع بيانات العميل في النظام ويوفر واجهة موحدة للوصول إليها
class CustomerDataProvider extends ChangeNotifier {
  final CustomerDataService _customerService = CustomerDataService();
  
  bool _isLoading = false;
  String? _error;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get error => _error;

  /// بيانات العميل
  UserProfile? get customerProfile => _customerService.customerProfile;

  /// التحقق من تسجيل الدخول
  bool get isLoggedIn => _customerService.isLoggedIn;

  /// الحصول على الاسم الكامل
  String get fullName => _customerService.getFullName();

  /// الحصول على البريد الإلكتروني
  String get email => _customerService.getEmail();

  /// الحصول على رقم الهاتف
  String get phone => _customerService.getPhone();

  /// الحصول على معرف العميل
  String get customerId => _customerService.getCustomerId();

  /// الحصول على جميع العناوين
  List<Address> get addresses => _customerService.getAllAddresses();

  /// الحصول على العنوان الافتراضي
  Address? get defaultAddress => _customerService.getDefaultAddress();

  /// التحقق من اكتمال الملف الشخصي
  bool get isProfileComplete => _customerService.isProfileComplete();

  /// التحقق من أول مرة فتح التطبيق
  Future<bool> isFirstTime() async {
    return await _customerService.isFirstTime();
  }

  /// تحميل بيانات العميل
  Future<bool> loadCustomerData() async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _customerService.loadCustomerData();
      _setLoading(false);
      notifyListeners();
      return success;
    } catch (e) {
      _setError('فشل في تحميل بيانات العميل: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// حفظ بيانات العميل
  Future<bool> saveCustomerData(UserProfile profile) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _customerService.saveCustomerData(profile);
      if (success) {
        notifyListeners();
      } else {
        _setError('فشل في حفظ بيانات العميل');
      }
      _setLoading(false);
      return success;
    } catch (e) {
      _setError('خطأ في حفظ بيانات العميل: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// تحديث بيانات العميل
  Future<bool> updateCustomerData({
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? avatar,
    DateTime? dateOfBirth,
    String? gender,
    List<Address>? addresses,
    String? defaultAddressId,
    Map<String, dynamic>? preferences,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _customerService.updateCustomerData(
        firstName: firstName,
        lastName: lastName,
        email: email,
        phone: phone,
        avatar: avatar,
        dateOfBirth: dateOfBirth,
        gender: gender,
        addresses: addresses,
        defaultAddressId: defaultAddressId,
        preferences: preferences,
      );

      if (success) {
        notifyListeners();
      } else {
        _setError('فشل في تحديث بيانات العميل');
      }
      _setLoading(false);
      return success;
    } catch (e) {
      _setError('خطأ في تحديث بيانات العميل: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// إضافة عنوان جديد
  Future<bool> addAddress(Address address) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _customerService.addAddress(address);
      if (success) {
        notifyListeners();
      } else {
        _setError('فشل في إضافة العنوان');
      }
      _setLoading(false);
      return success;
    } catch (e) {
      _setError('خطأ في إضافة العنوان: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// تحديث عنوان موجود
  Future<bool> updateAddress(Address updatedAddress) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _customerService.updateAddress(updatedAddress);
      if (success) {
        notifyListeners();
      } else {
        _setError('فشل في تحديث العنوان');
      }
      _setLoading(false);
      return success;
    } catch (e) {
      _setError('خطأ في تحديث العنوان: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// حذف عنوان
  Future<bool> deleteAddress(String addressId) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _customerService.deleteAddress(addressId);
      if (success) {
        notifyListeners();
      } else {
        _setError('فشل في حذف العنوان');
      }
      _setLoading(false);
      return success;
    } catch (e) {
      _setError('خطأ في حذف العنوان: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// تعيين العنوان الافتراضي
  Future<bool> setDefaultAddress(String addressId) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _customerService.setDefaultAddress(addressId);
      if (success) {
        notifyListeners();
      } else {
        _setError('فشل في تعيين العنوان الافتراضي');
      }
      _setLoading(false);
      return success;
    } catch (e) {
      _setError('خطأ في تعيين العنوان الافتراضي: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    _setLoading(true);
    _clearError();

    try {
      await _customerService.logout();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تسجيل الخروج: ${e.toString()}');
    }
    _setLoading(false);
  }

  /// حذف جميع بيانات العميل
  Future<void> deleteAllCustomerData() async {
    _setLoading(true);
    _clearError();

    try {
      await _customerService.deleteAllCustomerData();
      notifyListeners();
    } catch (e) {
      _setError('خطأ في حذف بيانات العميل: ${e.toString()}');
    }
    _setLoading(false);
  }

  /// إعادة تحميل البيانات
  Future<bool> refreshCustomerData() async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _customerService.refreshCustomerData();
      notifyListeners();
      _setLoading(false);
      return success;
    } catch (e) {
      _setError('فشل في إعادة تحميل البيانات: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// التحقق من صحة رقم الهاتف
  bool isValidPhone(String phone) {
    return _customerService.isValidPhone(phone);
  }

  /// التحقق من صحة البريد الإلكتروني
  bool isValidEmail(String email) {
    return _customerService.isValidEmail(email);
  }

  /// الحصول على معلومات العميل للعرض
  Map<String, String> getCustomerDisplayInfo() {
    return _customerService.getCustomerDisplayInfo();
  }

  /// ضمان تحميل البيانات
  Future<void> ensureDataLoaded() async {
    if (!_customerService.isDataLoaded) {
      await loadCustomerData();
    }
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void _clearError() {
    _error = null;
  }

  /// تسجيل مستخدم جديد
  Future<bool> registerNewCustomer({
    required String firstName,
    required String lastName,
    required String email,
    required String phone,
    String? avatar,
    DateTime? dateOfBirth,
    String? gender,
    List<Address>? addresses,
    Map<String, dynamic>? preferences,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // التحقق من صحة البيانات
      if (firstName.trim().isEmpty || lastName.trim().isEmpty) {
        _setError('يرجى إدخال الاسم الأول والأخير');
        _setLoading(false);
        return false;
      }

      if (!isValidEmail(email)) {
        _setError('البريد الإلكتروني غير صحيح');
        _setLoading(false);
        return false;
      }

      if (!isValidPhone(phone)) {
        _setError('رقم الهاتف غير صحيح');
        _setLoading(false);
        return false;
      }

      // إنشاء ملف شخصي جديد
      final newProfile = UserProfile(
        id: 'customer_${DateTime.now().millisecondsSinceEpoch}',
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        email: email.trim(),
        phone: phone.trim(),
        avatar: avatar,
        dateOfBirth: dateOfBirth,
        gender: gender,
        addresses: addresses ?? [],
        preferences: preferences ?? {
          'language': 'ar',
          'notifications': true,
          'theme': 'light',
        },
      );

      final success = await saveCustomerData(newProfile);
      if (success) {
        await _customerService.setNotFirstTime();
      }

      return success;
    } catch (e) {
      _setError('خطأ في التسجيل: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// تحديث الملف الشخصي الأساسي
  Future<bool> updateBasicProfile({
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? avatar,
    DateTime? dateOfBirth,
    String? gender,
  }) async {
    return await updateCustomerData(
      firstName: firstName,
      lastName: lastName,
      email: email,
      phone: phone,
      avatar: avatar,
      dateOfBirth: dateOfBirth,
      gender: gender,
    );
  }

  /// تحديث التفضيلات
  Future<bool> updatePreferences(Map<String, dynamic> preferences) async {
    return await updateCustomerData(preferences: preferences);
  }

  /// الحصول على تفضيل معين
  T? getPreference<T>(String key, [T? defaultValue]) {
    final preferences = customerProfile?.preferences;
    if (preferences == null) return defaultValue;
    return preferences[key] as T? ?? defaultValue;
  }

  /// تعيين تفضيل معين
  Future<bool> setPreference(String key, dynamic value) async {
    final currentPreferences = customerProfile?.preferences ?? {};
    final updatedPreferences = Map<String, dynamic>.from(currentPreferences);
    updatedPreferences[key] = value;
    return await updatePreferences(updatedPreferences);
  }
}
