import 'Address.dart';

/// نموذج الملف الشخصي للمستخدم
class UserProfile {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String? avatar;
  final DateTime? dateOfBirth;
  final String? gender;
  final List<Address> addresses;
  final String? defaultAddressId;
  final Map<String, dynamic>? preferences;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserProfile({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    this.avatar,
    this.dateOfBirth,
    this.gender,
    this.addresses = const [],
    this.defaultAddressId,
    this.preferences,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// إنشاء من Map
  factory UserProfile.fromMap(Map<String, dynamic> map) {
    return UserProfile(
      id: map['id']?.toString() ?? '',
      firstName: map['firstName']?.toString() ?? '',
      lastName: map['lastName']?.toString() ?? '',
      email: map['email']?.toString() ?? '',
      phone: map['phone']?.toString() ?? '',
      avatar: map['avatar']?.toString(),
      dateOfBirth: map['dateOfBirth'] != null 
          ? DateTime.tryParse(map['dateOfBirth'].toString())
          : null,
      gender: map['gender']?.toString(),
      addresses: map['addresses'] != null
          ? (map['addresses'] as List).map((addr) => Address.fromMap(addr)).toList()
          : [],
      defaultAddressId: map['defaultAddressId']?.toString(),
      preferences: map['preferences'] as Map<String, dynamic>?,
      createdAt: map['createdAt'] != null 
          ? DateTime.tryParse(map['createdAt'].toString()) ?? DateTime.now()
          : DateTime.now(),
      updatedAt: map['updatedAt'] != null 
          ? DateTime.tryParse(map['updatedAt'].toString()) ?? DateTime.now()
          : DateTime.now(),
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'gender': gender,
      'addresses': addresses.map((addr) => addr.toMap()).toList(),
      'defaultAddressId': defaultAddressId,
      'preferences': preferences,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// الحصول على الاسم الكامل
  String get fullName {
    return '$firstName $lastName'.trim();
  }

  /// الحصول على العنوان الافتراضي
  Address? get defaultAddress {
    if (defaultAddressId != null) {
      try {
        return addresses.firstWhere((addr) => addr.id == defaultAddressId);
      } catch (e) {
        // إذا لم يوجد العنوان الافتراضي، إرجاع أول عنوان
        return addresses.isNotEmpty ? addresses.first : null;
      }
    }
    return addresses.isNotEmpty ? addresses.first : null;
  }

  /// الحصول على عناوين حسب النوع
  List<Address> getAddressesByType(String type) {
    return addresses.where((addr) => addr.title == type).toList();
  }

  /// التحقق من اكتمال الملف الشخصي
  bool get isProfileComplete {
    return firstName.isNotEmpty &&
           lastName.isNotEmpty &&
           email.isNotEmpty &&
           phone.isNotEmpty &&
           addresses.isNotEmpty;
  }

  /// التحقق من وجود عنوان افتراضي
  bool get hasDefaultAddress {
    return defaultAddress != null;
  }

  /// نسخ مع تعديل
  UserProfile copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? avatar,
    DateTime? dateOfBirth,
    String? gender,
    List<Address>? addresses,
    String? defaultAddressId,
    Map<String, dynamic>? preferences,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserProfile(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      addresses: addresses ?? this.addresses,
      defaultAddressId: defaultAddressId ?? this.defaultAddressId,
      preferences: preferences ?? this.preferences,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// إضافة عنوان جديد
  UserProfile addAddress(Address address) {
    final updatedAddresses = List<Address>.from(addresses);
    updatedAddresses.add(address);
    
    // إذا كان هذا أول عنوان، اجعله افتراضي
    String? newDefaultId = defaultAddressId;
    if (addresses.isEmpty || address.isDefault) {
      newDefaultId = address.id;
      // إزالة الافتراضي من العناوين الأخرى
      for (int i = 0; i < updatedAddresses.length - 1; i++) {
        if (updatedAddresses[i].isDefault) {
          updatedAddresses[i] = updatedAddresses[i].copyWith(isDefault: false);
        }
      }
    }
    
    return copyWith(
      addresses: updatedAddresses,
      defaultAddressId: newDefaultId,
      updatedAt: DateTime.now(),
    );
  }

  /// تحديث عنوان
  UserProfile updateAddress(Address updatedAddress) {
    final updatedAddresses = addresses.map((addr) {
      if (addr.id == updatedAddress.id) {
        return updatedAddress;
      }
      return addr;
    }).toList();
    
    String? newDefaultId = defaultAddressId;
    if (updatedAddress.isDefault) {
      newDefaultId = updatedAddress.id;
      // إزالة الافتراضي من العناوين الأخرى
      for (int i = 0; i < updatedAddresses.length; i++) {
        if (updatedAddresses[i].id != updatedAddress.id && updatedAddresses[i].isDefault) {
          updatedAddresses[i] = updatedAddresses[i].copyWith(isDefault: false);
        }
      }
    }
    
    return copyWith(
      addresses: updatedAddresses,
      defaultAddressId: newDefaultId,
      updatedAt: DateTime.now(),
    );
  }

  /// حذف عنوان
  UserProfile removeAddress(String addressId) {
    final updatedAddresses = addresses.where((addr) => addr.id != addressId).toList();
    
    String? newDefaultId = defaultAddressId;
    if (defaultAddressId == addressId) {
      // إذا تم حذف العنوان الافتراضي، اختر أول عنوان متاح
      newDefaultId = updatedAddresses.isNotEmpty ? updatedAddresses.first.id : null;
    }
    
    return copyWith(
      addresses: updatedAddresses,
      defaultAddressId: newDefaultId,
      updatedAt: DateTime.now(),
    );
  }

  /// تعيين عنوان كافتراضي
  UserProfile setDefaultAddress(String addressId) {
    final updatedAddresses = addresses.map((addr) {
      return addr.copyWith(isDefault: addr.id == addressId);
    }).toList();
    
    return copyWith(
      addresses: updatedAddresses,
      defaultAddressId: addressId,
      updatedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProfile && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserProfile(id: $id, name: $fullName, email: $email, addresses: ${addresses.length})';
  }
}

/// بيانات المستخدم التجريبية
class MockUserData {
  static UserProfile getSampleUser() {
    final addresses = [
      Address(
        id: 'addr_1',
        title: 'المنزل',
        fullAddress: 'شارع الملك فهد، مبنى 123، شقة 45، حي العليا، الرياض',
        city: 'الرياض',
        district: 'العليا',
        street: 'شارع الملك فهد',
        buildingNumber: '123',
        apartmentNumber: '45',
        landmark: 'بجانب مول العليا',
        isDefault: true,
      ),
      Address(
        id: 'addr_2',
        title: 'العمل',
        fullAddress: 'طريق الملك عبدالعزيز، مبنى 456، الدور الثالث، حي الملز، الرياض',
        city: 'الرياض',
        district: 'الملز',
        street: 'طريق الملك عبدالعزيز',
        buildingNumber: '456',
        apartmentNumber: 'الدور الثالث',
        landmark: 'مقابل مستشفى الملك فيصل',
        isDefault: false,
      ),
      Address(
        id: 'addr_3',
        title: 'أخرى',
        fullAddress: 'شارع التحلية، مبنى 789، حي الروضة، الرياض',
        city: 'الرياض',
        district: 'الروضة',
        street: 'شارع التحلية',
        buildingNumber: '789',
        apartmentNumber: '',
        landmark: 'قريب من مطعم الأصالة',
        isDefault: false,
      ),
    ];

    return UserProfile(
      id: 'user_123',
      firstName: 'أحمد',
      lastName: 'محمد',
      email: '<EMAIL>',
      phone: '+966501234567',
      addresses: addresses,
      defaultAddressId: 'addr_1',
      preferences: {
        'language': 'ar',
        'notifications': true,
        'theme': 'light',
      },
    );
  }
}
