import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:test2/models/PaymentMethod.dart';
import 'package:test2/services/PaymentService.dart';

class PaymentDetailsPage extends StatefulWidget {
  final PaymentMethod paymentMethod;
  final double orderAmount;
  final String orderId;

  const PaymentDetailsPage({
    Key? key,
    required this.paymentMethod,
    required this.orderAmount,
    required this.orderId,
  }) : super(key: key);

  @override
  _PaymentDetailsPageState createState() => _PaymentDetailsPageState();
}

class _PaymentDetailsPageState extends State<PaymentDetailsPage> {
  final PaymentService _paymentService = PaymentService();
  final TextEditingController _accountController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  
  bool _isProcessing = false;
  double _fees = 0.0;
  double _total = 0.0;

  @override
  void initState() {
    super.initState();
    _calculateAmounts();
  }

  void _calculateAmounts() {
    _fees = widget.paymentMethod.calculateFees(widget.orderAmount);
    _total = _paymentService.calculateTotalAmount(widget.orderAmount, widget.paymentMethod);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: AppBar(
        title: Text(
          'تفاصيل الدفع',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Color(0xFF4C53A5),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            // بطاقة طريقة الدفع
            _buildPaymentMethodCard(),
            
            SizedBox(height: 16),
            
            // بطاقة ملخص المبلغ
            _buildAmountSummaryCard(),
            
            SizedBox(height: 16),
            
            // بطاقة تعليمات الدفع
            _buildPaymentInstructionsCard(),
            
            if (widget.paymentMethod.requiresAccountInfo) ...[
              SizedBox(height: 16),
              _buildAccountInfoCard(),
            ],
            
            SizedBox(height: 16),
            
            // بطاقة ملاحظات إضافية
            _buildNotesCard(),
            
            SizedBox(height: 24),
            
            // زر تأكيد الدفع
            _buildConfirmButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: widget.paymentMethod.getColor().withOpacity(0.1),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(
              widget.paymentMethod.getIcon(),
              color: widget.paymentMethod.getColor(),
              size: 32,
            ),
          ),
          
          SizedBox(width: 16),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.paymentMethod.nameAr,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C53A5),
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  widget.paymentMethod.descriptionAr,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountSummaryCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.receipt, color: Color(0xFF4C53A5)),
              SizedBox(width: 8),
              Text(
                'ملخص المبلغ',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16),
          
          _buildAmountRow('رقم الطلب:', widget.orderId),
          _buildAmountRow('مبلغ الطلب:', '${widget.orderAmount.toStringAsFixed(2)} ريال'),
          
          if (_fees > 0)
            _buildAmountRow(
              'رسوم الدفع:', 
              '${_fees.toStringAsFixed(2)} ريال',
              color: Colors.orange.shade700,
            ),
          
          Divider(thickness: 1),
          
          _buildAmountRow(
            'المبلغ الإجمالي:', 
            '${_total.toStringAsFixed(2)} ريال',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildAmountRow(String label, String value, {Color? color, bool isTotal = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Color(0xFF4C53A5) : Colors.grey.shade700,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
              color: color ?? (isTotal ? Color(0xFF4C53A5) : Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentInstructionsCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Color(0xFF4C53A5)),
              SizedBox(width: 8),
              Text(
                'تعليمات الدفع',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 12),
          
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Text(
              _paymentService.getPaymentInstructions(widget.paymentMethod),
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue.shade800,
                height: 1.5,
              ),
            ),
          ),
          
          if (widget.paymentMethod.accountNumber != null) ...[
            SizedBox(height: 12),
            _buildCopyableInfo('رقم الحساب/المحفظة:', widget.paymentMethod.accountNumber!),
          ],
          
          if (widget.paymentMethod.accountName != null) ...[
            SizedBox(height: 8),
            _buildCopyableInfo('اسم الحساب:', widget.paymentMethod.accountName!),
          ],
        ],
      ),
    );
  }

  Widget _buildCopyableInfo(String label, String value) {
    return Row(
      children: [
        Expanded(
          child: Text(
            '$label $value',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        IconButton(
          icon: Icon(Icons.copy, color: Color(0xFF4C53A5), size: 20),
          onPressed: () {
            Clipboard.setData(ClipboardData(text: value));
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم نسخ $label'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildAccountInfoCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.account_circle, color: Color(0xFF4C53A5)),
              SizedBox(width: 8),
              Text(
                'معلومات حسابك',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16),
          
          TextFormField(
            controller: _accountController,
            decoration: InputDecoration(
              labelText: 'رقم حسابك/محفظتك',
              hintText: 'أدخل رقم حسابك أو محفظتك',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              prefixIcon: Icon(Icons.account_balance_wallet),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال رقم الحساب/المحفظة';
              }
              return null;
            },
            keyboardType: TextInputType.number,
          ),
        ],
      ),
    );
  }

  Widget _buildNotesCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.note_add, color: Color(0xFF4C53A5)),
              SizedBox(width: 8),
              Text(
                'ملاحظات إضافية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16),
          
          TextFormField(
            controller: _notesController,
            decoration: InputDecoration(
              labelText: 'ملاحظات (اختياري)',
              hintText: 'أضف أي ملاحظات إضافية...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              prefixIcon: Icon(Icons.note),
            ),
            maxLines: 3,
            maxLength: 200,
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmButton() {
    return Container(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isProcessing ? null : _processPayment,
        style: ElevatedButton.styleFrom(
          backgroundColor: Color(0xFF4C53A5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: _isProcessing
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  ),
                  SizedBox(width: 12),
                  Text(
                    'جاري المعالجة...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              )
            : Text(
                'تأكيد الدفع',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  void _processPayment() async {
    if (widget.paymentMethod.requiresAccountInfo && !_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final result = await _paymentService.processPayment(
        method: widget.paymentMethod,
        amount: widget.orderAmount,
        userAccountNumber: _accountController.text.trim(),
        notes: _notesController.text.trim(),
      );

      if (result.success) {
        _showSuccessDialog(result);
      } else {
        _showErrorDialog(result.messageAr);
      }
    } catch (e) {
      _showErrorDialog('حدث خطأ أثناء معالجة الدفع');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  void _showSuccessDialog(PaymentResult result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 28),
            SizedBox(width: 8),
            Text('تم بنجاح'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تم تأكيد طلب الدفع بنجاح'),
            SizedBox(height: 8),
            Text('رقم المعاملة: ${result.transactionId}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // إغلاق الحوار
              Navigator.of(context).pop(result); // العودة مع النتيجة
            },
            child: Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red, size: 28),
            SizedBox(width: 8),
            Text('خطأ'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('موافق'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _accountController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
