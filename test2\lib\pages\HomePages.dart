import 'package:flutter/material.dart';
import 'package:test2/widgets/HomeAppBar.dart';
import 'package:test2/widgets/CategoriesWidget.dart';
import 'package:test2/widgets/ItemsWidgets.dart';
import 'package:test2/widgets/SearchWidget.dart';
import 'package:test2/widgets/OffersWidget.dart';
import 'package:test2/widgets/CustomBottomNavBar.dart';
import 'package:test2/services/AutoNotificationManager.dart';
import 'package:test2/utils/ResponsiveHelper.dart';
import 'package:test2/widgets/ResponsiveText.dart';
import 'package:test2/services/UserService.dart';
import 'package:test2/models/User.dart';
import 'package:test2/pages/WelcomePage.dart';

class HomePage extends StatefulWidget {
  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String selectedCategory = "الكل"; // التصنيف المختار
  String searchQuery = ""; // نص البحث
  bool isSearching = false; // حالة البحث

  // متغيرات التبويبات الجديدة
  String selectedTab = "الكل"; // التبويب المختار
  final List<String> tabs = ["الكل", "الأقرب", "الجديد", "المفضلة"];

  // متحكم التمرير
  late ScrollController _scrollController;
  double _offersHeight = 220.0; // الارتفاع الأولي للعروض
  final double _maxOffersHeight = 220.0; // الارتفاع الأقصى
  final double _minOffersHeight = 0.0; // الارتفاع الأدنى (مخفي)

  // متغيرات العناوين
  double _offersTitleOpacity = 0.0; // شفافية عنوان العروض

  // خدمة المستخدم
  final UserService _userService = UserService();

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);

    // تحميل بيانات المستخدم عند فتح الصفحة الرئيسية
    _loadUserData();

    // بدء الإشعارات التلقائية
    AutoNotificationManager().startAutoNotifications();
  }

  // تحميل بيانات المستخدم
  Future<void> _loadUserData() async {
    try {
      await _userService.loadUser();

      // إنشاء مستخدم تجريبي للاختبار إذا لم يكن هناك مستخدم
      if (_userService.currentUser == null) {
        await _createTestUser();
      }

      // تحديث الواجهة بعد تحميل البيانات
      if (mounted) {
        setState(() {
          // إعادة بناء الواجهة مع البيانات المحدثة
        });
      }
      print(
          'تم تحميل بيانات المستخدم في الصفحة الرئيسية: ${_userService.currentUser?.displayName ?? "غير محدد"}');
    } catch (e) {
      print('خطأ في تحميل بيانات المستخدم في الصفحة الرئيسية: $e');
    }
  }

  // إنشاء مستخدم تجريبي للاختبار
  Future<void> _createTestUser() async {
    try {
      final testUser = User(
        id: 'test_user_${DateTime.now().millisecondsSinceEpoch}',
        name: 'أحمد محمد علي',
        phone: '+967 777 123 456',
        email: '<EMAIL>',
        address: 'شارع الستين، صنعاء',
        city: 'صنعاء',
        latitude: 15.3694,
        longitude: 44.1910,
        createdAt: DateTime.now(),
        isGuest: false,
      );

      await _userService.saveUser(testUser);
      print('تم إنشاء مستخدم تجريبي: ${testUser.displayName}');
    } catch (e) {
      print('خطأ في إنشاء المستخدم التجريبي: $e');
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();

    // إيقاف الإشعارات التلقائية
    AutoNotificationManager().stopAutoNotifications();

    super.dispose();
  }

  void _onScroll() {
    if (!isSearching) {
      // حساب الارتفاع الجديد للعروض بناءً على موضع التمرير
      double scrollOffset = _scrollController.offset;

      // تحديد معدل التغيير (يمكن تعديله لتحكم أفضل)
      // 0.8 = تغيير سريع، 1.0 = تغيير متوسط، 1.2 = تغيير بطيء
      double changeRate = 0.8; // جعل التأثير أكثر وضوحاً

      // السلوك المطلوب والمطبق:
      // 🔼 التمرير لأعلى (scrollOffset يزيد من 0 إلى 275+) → العروض تقل (من 220px إلى 0px)
      // 🔽 التمرير لأسفل (scrollOffset يقل من 275+ إلى 0) → العروض تزيد (من 0px إلى 220px)
      // 🏠 في الأعلى (scrollOffset = 0) → العروض كاملة (newHeight = 220px)
      // 📐 المعادلة: newHeight = 220 - (scrollOffset * 0.8)
      double newHeight = _maxOffersHeight - (scrollOffset * changeRate);

      // تحديد الحد الأدنى والأقصى
      newHeight = newHeight.clamp(_minOffersHeight, _maxOffersHeight);

      // حساب شفافية عنوان العروض (يختفي مع اختفاء العروض)
      double newOffersTitleOpacity =
          (newHeight / _maxOffersHeight).clamp(0.0, 1.0);

      // التبويبات ثابتة دائماً - لا تتغير مع التمرير

      // تحديث القيم فقط إذا كان هناك تغيير ملحوظ (تحسين الأداء)
      if ((_offersHeight - newHeight).abs() > 2 ||
          (_offersTitleOpacity - newOffersTitleOpacity).abs() > 0.05) {
        setState(() {
          _offersHeight = newHeight;
          _offersTitleOpacity = newOffersTitleOpacity;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: HomeAppBar(),
      backgroundColor: Color(0xFFC3243B),
      body: Column(
        children: [
          // الجزء الثابت (البحث والتصنيفات)
          Container(
            decoration: const BoxDecoration(
              color: Color(0xFFEDECF2),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(35),
                topRight: Radius.circular(35),
              ),
            ),
            child: Column(
              children: [
                // Banner للضيوف
                if (_userService.needsRegistration()) _buildGuestBanner(),
                // if (_userService.currentUser?.isGuest ?? true) _buildGuestBanner(),

                Padding(
                  padding: const EdgeInsets.only(top: 15),
                  child: Column(
                    children: [
                      // شريط البحث
                      SearchWidget(
                        hintText: "ابحث عن متجر أو منتج...",
                        onSearchChanged: (query) {
                          setState(() {
                            searchQuery = query;
                            isSearching = query.isNotEmpty;
                            // إعادة تعيين ارتفاع العروض عند البحث
                            if (isSearching) {
                              _offersHeight = 0.0;
                              _offersTitleOpacity = 0.0;
                            } else {
                              _offersHeight = _maxOffersHeight;
                              _offersTitleOpacity = 1.0;
                              // إعادة تعيين موضع التمرير عند إلغاء البحث
                              if (_scrollController.hasClients) {
                                _scrollController.animateTo(
                                  0,
                                  duration: Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              }
                            }
                          });
                        },
                        showSuggestions: true,
                        suggestionType: 'items',
                        suffixIcon: searchQuery.isNotEmpty ? Icons.clear : null,
                      ),

                      SizedBox(
                        height: ResponsiveHelper.responsiveSpacing(
                          context,
                          mobile: 2,
                          tablet: 3,
                          desktop: 4,
                        ),
                      ),

                      // عنوان التصنيفات (إخفاء عند البحث)
                      // if (!isSearching) _buildSectionTitle("التصنيفات"),

                      // التصنيفات (إخفاء عند البحث)
                      if (!isSearching)
                        CategoriesWidget(
                          selectedCategory: selectedCategory,
                          onCategorySelected: (category) {
                            setState(() {
                              selectedCategory = category;
                              // إعادة تعيين ارتفاع العروض عند تغيير التصنيف
                              _offersHeight = _maxOffersHeight;
                              _offersTitleOpacity = 1.0;
                              // إعادة تعيين موضع التمرير
                              if (_scrollController.hasClients) {
                                _scrollController.animateTo(
                                  0,
                                  duration: Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              }
                            });
                          },
                        ),

                      // قسم العروض (ارتفاع متغير)
                      if (!isSearching) ...[
                        // عنوان العروض مع شفافية متدرجة
                        // AnimatedOpacity(
                        //   duration: Duration(milliseconds: 150),
                        //   opacity: _offersTitleOpacity,
                        //   child: _buildSectionTitle("العروض والتخفيضات"),
                        // ),

                        AnimatedContainer(
                          duration: Duration(milliseconds: 150),
                          curve: Curves.easeInOut,
                          height: _offersHeight,
                          child: ClipRect(
                            child: _offersHeight > 30
                                ? Expanded(
                                    child: SingleChildScrollView(
                                        child: OffersWidget(
                                            searchQuery: searchQuery)))
                                : Container(),
                          ),
                        ),

                        // التبويبات ثابتة دائماً (في الجزء الثابت)
                        if (!isSearching) _buildTabsWidget(),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),

          // الجزء القابل للتمرير (المنتجات)
          Expanded(
            child: Container(
              color: Color(0xFFEDECF2),
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: ResponsiveHelper.responsiveEdgeInsets(
                        context,
                        mobile: 12,
                        tablet: 15,
                        desktop: 18,
                      ),
                      child: Column(
                        children: [
                          // عنوان نتائج البحث (يظهر فقط عند البحث)
                          if (isSearching)
                            _buildSectionTitle(
                                "نتائج البحث (المتاجر والمنتجات)"),

                          // قائمة المطاعم والمنتجات
                          ItemsWidgets(
                            selectedCategory:
                                isSearching ? "الكل" : selectedCategory,
                            searchQuery: searchQuery,
                            selectedTab: isSearching ? "الكل" : selectedTab,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      // bottomNavigationBar: CurvedNavigationBar(
      //   backgroundColor: Colors.transparent,
      //   onTap: (index) {},
      //   height: 70,
      //   color: Color(0xFFC3243B),
      //   items: [
      //     Icon(CupertinoIcons.car_fill, size: 30, color: Colors.white),
      //     Icon(Icons.home, size: 30, color: Colors.white),
      //     Icon(Icons.list, size: 30, color: Colors.white)
      //   ],
      // ),
      bottomNavigationBar: SafeArea(
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: Offset(0, -2),
              ),
            ],
          ),
          child: FlatBottomNavBar(
            currentIndex: 2, // الصفحة الرئيسية
            onTap: (index) {
              NavigationHelper.navigateToPage(context, index);
            },
          ),
        ),
      ),
    );
  }

  // دالة لبناء عناوين الأقسام
  Widget _buildSectionTitle(String title) {
    return Container(
      alignment: Alignment.centerRight,
      margin: ResponsiveHelper.responsiveEdgeInsetsCustom(
        context,
        vertical: 12,
        horizontal: 8,
      ),
      child: ResponsiveText(
        title,
        mobileFontSize: 16,
        tabletFontSize: 18,
        desktopFontSize: 20,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
    );
  }

  // دالة لبناء التبويبات
  Widget _buildTabsWidget() {
    return Container(
      margin: ResponsiveHelper.responsiveEdgeInsetsCustom(
        context,
        vertical: 12,
        horizontal: 8,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        textDirection: TextDirection.rtl,
        children: tabs.map((tab) {
          final isSelected = selectedTab == tab;
          return GestureDetector(
            onTap: () {
              setState(() {
                selectedTab = tab;
              });
            },
            child: AnimatedContainer(
              duration: Duration(milliseconds: 200),
              padding: ResponsiveHelper.responsiveEdgeInsetsCustom(
                context,
                horizontal: 16,
                vertical: 8,
              ),
              decoration: BoxDecoration(
                color: isSelected ? Color(0xFFC3243B) : Colors.transparent,
                borderRadius: BorderRadius.circular(
                  ResponsiveHelper.responsiveBorderRadius(
                    context,
                    mobile: 20,
                    tablet: 25,
                    desktop: 30,
                  ),
                ),
                border: Border.all(
                  color: isSelected ? Color(0xFFC3243B) : Colors.grey.shade400,
                  width: ResponsiveHelper.responsiveBorderWidth(
                    context,
                    mobile: 1.5,
                    tablet: 2,
                    desktop: 2.5,
                  ),
                ),
              ),
              child: ResponsiveText(
                tab,
                mobileFontSize: 13,
                tabletFontSize: 14,
                desktopFontSize: 15,
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  // بناء banner للضيوف
  Widget _buildGuestBanner() {
    return Container(
      width: double.infinity,
      margin: ResponsiveHelper.responsiveEdgeInsets(
        context,
        mobile: 12,
        tablet: 15,
        desktop: 18,
      ),
      padding: ResponsiveHelper.responsiveEdgeInsets(
        context,
        mobile: 12,
        tablet: 15,
        desktop: 18,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFF4C53A5),
            Color(0xFF6366F1),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.responsiveBorderRadius(
            context,
            mobile: 12,
            tablet: 15,
            desktop: 18,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.person_add,
            color: Colors.white,
            size: ResponsiveHelper.responsiveIconSize(
              context,
              mobile: 24,
              tablet: 28,
              desktop: 32,
            ),
          ),
          SizedBox(
            width: ResponsiveHelper.responsiveSpacing(
              context,
              mobile: 12,
              tablet: 15,
              desktop: 18,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText(
                  'مرحباً بك كضيف!',
                  mobileFontSize: 16,
                  tabletFontSize: 18,
                  desktopFontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                SizedBox(height: 4),
                ResponsiveText(
                  'سجل حسابك الآن لحفظ طلباتك والحصول على عروض خاصة',
                  mobileFontSize: 12,
                  tabletFontSize: 14,
                  desktopFontSize: 16,
                  color: Colors.white.withOpacity(0.9),
                ),
              ],
            ),
          ),
          SizedBox(
            width: ResponsiveHelper.responsiveSpacing(
              context,
              mobile: 8,
              tablet: 10,
              desktop: 12,
            ),
          ),
          ElevatedButton(
            onPressed: () {
              // مسح بيانات الضيف والانتقال لصفحة الترحيب
              _userService.clearGuestData().then((_) {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => WelcomePage(),
                  ),
                );
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Color(0xFF4C53A5),
              padding: ResponsiveHelper.responsiveEdgeInsets(
                context,
                mobile: 8,
                tablet: 10,
                desktop: 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveHelper.responsiveBorderRadius(
                    context,
                    mobile: 8,
                    tablet: 10,
                    desktop: 12,
                  ),
                ),
              ),
            ),
            child: ResponsiveText(
              'تسجيل',
              mobileFontSize: 12,
              tabletFontSize: 14,
              desktopFontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
        ],
      ),
    );
  }
}
