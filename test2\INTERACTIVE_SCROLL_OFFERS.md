# ميزة التمرير التفاعلي للعروض
## Interactive Scroll Offers Feature

## 🎯 نظرة عامة
تم إضافة ميزة التمرير التفاعلي حيث يبقى شريط البحث والتصنيفات ثابتين، بينما تتغير العروض ديناميكياً حسب التمرير.

## ✨ **الميزات الجديدة**

### **1. شريط البحث والتصنيفات ثابتة:**
- شريط البحث يبقى في الأعلى دائماً
- التصنيفات تبقى ثابتة تحت شريط البحث
- لا تتحرك مع التمرير

### **2. العروض ديناميكية:**
- **الارتفاع الأولي:** 220px (كامل)
- **عند التمرير لأسفل:** يقل الارتفاع تدريجياً
- **عند الوصول للحد الأدنى:** تختفي العروض تماماً (0px)
- **عند التمرير لأعلى:** يزيد الارتفاع تدريجياً
- **عند العودة للأعلى:** ترجع للحجم الطبيعي

### **3. انتقالات سلسة:**
- انتقال متدرج مع `AnimatedContainer`
- مدة الانتقال: 150ms
- منحنى الانتقال: `Curves.easeInOut`
- تحديث سلس بدون تقطع

## 🔧 **التطبيق التقني**

### **1. متحكم التمرير:**
```dart
class _HomePageState extends State<HomePage> {
  // متحكم التمرير
  late ScrollController _scrollController;
  double _offersHeight = 220.0; // الارتفاع الأولي للعروض
  final double _maxOffersHeight = 220.0; // الارتفاع الأقصى
  final double _minOffersHeight = 0.0; // الارتفاع الأدنى (مخفي)
  
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }
}
```

### **2. دالة التمرير التفاعلي:**
```dart
void _onScroll() {
  if (!isSearching) {
    // حساب الارتفاع الجديد للعروض بناءً على موضع التمرير
    double scrollOffset = _scrollController.offset;
    
    // تحديد معدل التغيير (كلما زاد الرقم، كلما قل التأثير)
    double changeRate = 1.2;
    double newHeight = _maxOffersHeight - (scrollOffset * changeRate);
    
    // تحديد الحد الأدنى والأقصى
    newHeight = newHeight.clamp(_minOffersHeight, _maxOffersHeight);
    
    // تحديث الارتفاع فقط إذا كان هناك تغيير ملحوظ
    if ((_offersHeight - newHeight).abs() > 2) {
      setState(() {
        _offersHeight = newHeight;
      });
    }
  }
}
```

### **3. هيكل الصفحة الجديد:**
```dart
body: Column(
  children: [
    // الجزء الثابت (البحث والتصنيفات)
    Container(
      decoration: BoxDecoration(...),
      child: Column(
        children: [
          // شريط البحث (ثابت)
          SearchWidget(...),
          
          // التصنيفات (ثابتة)
          if (!isSearching) CategoriesWidget(...),
          
          // العروض (ارتفاع متغير)
          if (!isSearching) AnimatedContainer(
            duration: Duration(milliseconds: 150),
            curve: Curves.easeInOut,
            height: _offersHeight, // ارتفاع ديناميكي
            child: ClipRect(
              child: _offersHeight > 30
                  ? OffersWidget(searchQuery: searchQuery)
                  : Container(),
            ),
          ),
        ],
      ),
    ),
    
    // الجزء القابل للتمرير (المنتجات)
    Expanded(
      child: CustomScrollView(
        controller: _scrollController, // متحكم التمرير
        slivers: [
          SliverToBoxAdapter(
            child: ItemsWidgets(...), // قائمة المنتجات
          ),
        ],
      ),
    ),
  ],
),
```

### **4. العروض المتحركة:**
```dart
AnimatedContainer(
  duration: Duration(milliseconds: 150),
  curve: Curves.easeInOut,
  height: _offersHeight, // ارتفاع يتغير مع التمرير
  child: ClipRect(
    child: _offersHeight > 30
        ? OffersWidget(searchQuery: searchQuery)
        : Container(), // إخفاء عند الارتفاع المنخفض
  ),
),
```

## 🎮 **سلوك التفاعل**

### **📱 عند التمرير لأسفل:**
1. **البداية:** العروض بارتفاع 220px (كاملة)
2. **التمرير:** الارتفاع يقل تدريجياً
3. **المعادلة:** `newHeight = 220 - (scrollOffset * 1.2)`
4. **النهاية:** العروض تختفي تماماً (0px)

### **📱 عند التمرير لأعلى:**
1. **البداية:** العروض مخفية (0px)
2. **التمرير:** الارتفاع يزيد تدريجياً
3. **المعادلة:** نفس المعادلة لكن بالعكس
4. **النهاية:** العروض تظهر كاملة (220px)

### **🔍 عند البحث:**
```dart
onSearchChanged: (query) {
  setState(() {
    searchQuery = query;
    isSearching = query.isNotEmpty;
    // إخفاء العروض عند البحث
    if (isSearching) {
      _offersHeight = 0.0;
    } else {
      _offersHeight = _maxOffersHeight;
      // إعادة تعيين موضع التمرير
      _scrollController.animateTo(0, ...);
    }
  });
},
```

### **🏷️ عند تغيير التصنيف:**
```dart
onCategorySelected: (category) {
  setState(() {
    selectedCategory = category;
    // إعادة تعيين ارتفاع العروض
    _offersHeight = _maxOffersHeight;
    // إعادة تعيين موضع التمرير
    _scrollController.animateTo(0, ...);
  });
},
```

## 📊 **معدلات التغيير**

### **معدل التمرير:**
- **changeRate = 1.2:** متوسط السرعة
- **كلما زاد الرقم:** كلما قل تأثير التمرير
- **كلما قل الرقم:** كلما زاد تأثير التمرير

### **حد التحديث:**
- **abs() > 2:** تحديث فقط عند تغيير ملحوظ
- **يمنع التحديث المفرط:** تحسين الأداء
- **انتقال سلس:** بدون تقطع

### **حد الإخفاء:**
- **_offersHeight > 30:** عرض العروض
- **_offersHeight <= 30:** إخفاء العروض
- **منع الوميض:** عند الارتفاعات المنخفضة

## 🎯 **الحالات المختلفة**

### **1. الوضع العادي (بدون بحث):**
- ✅ شريط البحث ثابت
- ✅ التصنيفات ثابتة
- ✅ العروض ديناميكية (تتغير مع التمرير)
- ✅ المنتجات قابلة للتمرير

### **2. وضع البحث:**
- ✅ شريط البحث ثابت
- ❌ التصنيفات مخفية
- ❌ العروض مخفية
- ✅ نتائج البحث قابلة للتمرير

### **3. تغيير التصنيف:**
- ✅ إعادة تعيين ارتفاع العروض
- ✅ إعادة تعيين موضع التمرير
- ✅ انتقال سلس للأعلى

### **4. إلغاء البحث:**
- ✅ إظهار التصنيفات
- ✅ إظهار العروض كاملة
- ✅ إعادة تعيين موضع التمرير

## 🔄 **إدارة الذاكرة**

### **تنظيف الموارد:**
```dart
@override
void dispose() {
  _scrollController.removeListener(_onScroll);
  _scrollController.dispose();
  super.dispose();
}
```

### **تحسين الأداء:**
- **تحديث شرطي:** فقط عند التغيير الملحوظ
- **ClipRect:** لقطع المحتوى الزائد
- **AnimatedContainer:** انتقالات محسنة

## 🎨 **التصميم البصري**

### **الانتقالات:**
- **المدة:** 150ms (سريع وسلس)
- **المنحنى:** `Curves.easeInOut` (طبيعي)
- **التدرج:** متدرج وسلس

### **الألوان:**
- **الخلفية:** `Color(0xFFEDECF2)` (رمادي فاتح)
- **الحواف:** `BorderRadius.circular(35)` (دائرية)
- **التناسق:** مع باقي التطبيق

## 🚀 **النتائج المحققة**

### **قبل التحديث:**
- ❌ **التمرير العادي:** كل شيء يتحرك معاً
- ❌ **عدم تفاعل:** العروض ثابتة دائماً
- ❌ **استغلال مساحة:** العروض تأخذ مساحة دائماً

### **بعد التحديث:**
- ✅ **تمرير تفاعلي:** البحث والتصنيفات ثابتة
- ✅ **عروض ديناميكية:** تتغير مع التمرير
- ✅ **استغلال أمثل للمساحة:** العروض تختفي عند عدم الحاجة
- ✅ **تجربة مستخدم متقدمة:** سلسة وتفاعلية

## 🎯 **كيفية الاستخدام**

### **للمستخدم:**
1. **تصفح العروض:** في الأعلى بالحجم الكامل
2. **ابدأ التمرير:** لرؤية المنتجات
3. **شاهد العروض تقل:** تدريجياً مع التمرير
4. **مرر للأعلى:** لإعادة إظهار العروض
5. **ابحث أو غير التصنيف:** لإعادة تعيين العروض

### **للمطور:**
```bash
cd test2
flutter run
```

## 🎉 **الخلاصة**

تطبيق "زاد اليمن" أصبح الآن يوفر:
- ✅ **تمرير تفاعلي متقدم** مع عروض ديناميكية
- ✅ **شريط بحث وتصنيفات ثابتة** لسهولة الوصول
- ✅ **استغلال أمثل للمساحة** مع إخفاء العروض عند عدم الحاجة
- ✅ **انتقالات سلسة ومتدرجة** لتجربة مستخدم ممتازة
- ✅ **أداء محسن** مع تحديث شرطي وإدارة ذاكرة فعالة

🎯 **الميزة جاهزة وتعمل بكفاءة عالية!**

الآن يمكن للمستخدمين الاستفادة من مساحة الشاشة بشكل أفضل مع تجربة تمرير تفاعلية ومتقدمة.
