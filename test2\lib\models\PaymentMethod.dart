import 'package:flutter/material.dart';

enum PaymentType {
  cash,           // كاش عند الاستلام
  bankTransfer,   // حوالة بنكية
  jeebWallet,     // محفظة جيب
  omFlous,        // أم فلوس
  cashMobile,     // كاش موبايل
  otherWallet,    // محافظ أخرى
}

class PaymentMethod {
  final String id;
  final PaymentType type;
  final String name;
  final String nameAr;
  final String description;
  final String descriptionAr;
  final IconData icon;
  final Color color;
  final bool isEnabled;
  final bool requiresAccountInfo;
  final String? accountNumber;
  final String? accountName;
  final double? fees;
  final String? logoPath;

  PaymentMethod({
    required this.id,
    required this.type,
    required this.name,
    required this.nameAr,
    required this.description,
    required this.descriptionAr,
    required this.icon,
    required this.color,
    this.isEnabled = true,
    this.requiresAccountInfo = false,
    this.accountNumber,
    this.accountName,
    this.fees,
    this.logoPath,
  });

  // تحويل إلى Map للحفظ
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.toString(),
      'name': name,
      'nameAr': nameAr,
      'description': description,
      'descriptionAr': descriptionAr,
      'isEnabled': isEnabled,
      'requiresAccountInfo': requiresAccountInfo,
      'accountNumber': accountNumber,
      'accountName': accountName,
      'fees': fees,
      'logoPath': logoPath,
    };
  }

  // إنشاء من Map
  factory PaymentMethod.fromMap(Map<String, dynamic> map) {
    return PaymentMethod(
      id: map['id'],
      type: PaymentType.values.firstWhere(
        (e) => e.toString() == map['type'],
      ),
      name: map['name'],
      nameAr: map['nameAr'],
      description: map['description'],
      descriptionAr: map['descriptionAr'],
      icon: Icons.payment, // سيتم تحديدها حسب النوع
      color: Colors.blue, // سيتم تحديدها حسب النوع
      isEnabled: map['isEnabled'] ?? true,
      requiresAccountInfo: map['requiresAccountInfo'] ?? false,
      accountNumber: map['accountNumber'],
      accountName: map['accountName'],
      fees: map['fees']?.toDouble(),
      logoPath: map['logoPath'],
    );
  }

  // نسخ مع تعديل
  PaymentMethod copyWith({
    String? id,
    PaymentType? type,
    String? name,
    String? nameAr,
    String? description,
    String? descriptionAr,
    IconData? icon,
    Color? color,
    bool? isEnabled,
    bool? requiresAccountInfo,
    String? accountNumber,
    String? accountName,
    double? fees,
    String? logoPath,
  }) {
    return PaymentMethod(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      nameAr: nameAr ?? this.nameAr,
      description: description ?? this.description,
      descriptionAr: descriptionAr ?? this.descriptionAr,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isEnabled: isEnabled ?? this.isEnabled,
      requiresAccountInfo: requiresAccountInfo ?? this.requiresAccountInfo,
      accountNumber: accountNumber ?? this.accountNumber,
      accountName: accountName ?? this.accountName,
      fees: fees ?? this.fees,
      logoPath: logoPath ?? this.logoPath,
    );
  }

  // الحصول على الأيقونة حسب النوع
  IconData getIcon() {
    switch (type) {
      case PaymentType.cash:
        return Icons.money;
      case PaymentType.bankTransfer:
        return Icons.account_balance;
      case PaymentType.jeebWallet:
        return Icons.account_balance_wallet;
      case PaymentType.omFlous:
        return Icons.payment;
      case PaymentType.cashMobile:
        return Icons.phone_android;
      case PaymentType.otherWallet:
        return Icons.wallet;
    }
  }

  // الحصول على اللون حسب النوع
  Color getColor() {
    switch (type) {
      case PaymentType.cash:
        return Colors.green;
      case PaymentType.bankTransfer:
        return Colors.blue;
      case PaymentType.jeebWallet:
        return Color(0xFF1E88E5);
      case PaymentType.omFlous:
        return Color(0xFFFF6B35);
      case PaymentType.cashMobile:
        return Color(0xFF4CAF50);
      case PaymentType.otherWallet:
        return Color(0xFF9C27B0);
    }
  }

  // التحقق من صحة البيانات
  bool isValid() {
    if (!isEnabled) return false;
    if (requiresAccountInfo) {
      return accountNumber != null && accountNumber!.isNotEmpty;
    }
    return true;
  }

  // حساب الرسوم
  double calculateFees(double amount) {
    if (fees == null) return 0.0;
    return fees!;
  }

  // الحصول على نص الوصف الكامل
  String getFullDescription() {
    String baseDesc = descriptionAr;
    if (fees != null && fees! > 0) {
      baseDesc += " (رسوم: ${fees!.toStringAsFixed(2)} ريال)";
    }
    return baseDesc;
  }

  @override
  String toString() {
    return 'PaymentMethod{id: $id, type: $type, nameAr: $nameAr}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentMethod && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// فئة لمعلومات الدفع
class PaymentInfo {
  final PaymentMethod method;
  final double amount;
  final double fees;
  final double total;
  final String? transactionId;
  final String? accountNumber;
  final String? notes;
  final DateTime timestamp;

  PaymentInfo({
    required this.method,
    required this.amount,
    required this.fees,
    required this.total,
    this.transactionId,
    this.accountNumber,
    this.notes,
    required this.timestamp,
  });

  Map<String, dynamic> toMap() {
    return {
      'method': method.toMap(),
      'amount': amount,
      'fees': fees,
      'total': total,
      'transactionId': transactionId,
      'accountNumber': accountNumber,
      'notes': notes,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory PaymentInfo.fromMap(Map<String, dynamic> map) {
    return PaymentInfo(
      method: PaymentMethod.fromMap(map['method']),
      amount: map['amount'].toDouble(),
      fees: map['fees'].toDouble(),
      total: map['total'].toDouble(),
      transactionId: map['transactionId'],
      accountNumber: map['accountNumber'],
      notes: map['notes'],
      timestamp: DateTime.parse(map['timestamp']),
    );
  }
}
