import 'package:flutter/material.dart';
import '../utils/ResponsiveHelper.dart';
import '../utils/DataManager.dart';
import '../widgets/OptimizedImage.dart';

class CategoriesWidget extends StatelessWidget {
  final String selectedCategory;
  final Function(String) onCategorySelected;

  const CategoriesWidget({
    Key? key,
    required this.selectedCategory,
    required this.onCategorySelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // استخدام البيانات من DataManager
    const categories = DataManager.categories;
    return SizedBox(
      height: ResponsiveHelper.responsiveSize(
        context,
        mobile: 55,
        tablet: 60,
        desktop: 65,
      ),
      child: ListView.builder(
        reverse: true,
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = selectedCategory == category["name"];

          return CategoryItem(
            key: ValueKey('category_${category["name"]}'),
            category: category,
            isSelected: isSelected,
            onTap: () => onCategorySelected(category["name"]),
          );
        },
      ),
    );
  }
}

// مكون منفصل لعنصر التصنيف لتحسين الأداء
class CategoryItem extends StatelessWidget {
  final Map<String, dynamic> category;
  final bool isSelected;
  final VoidCallback onTap;

  const CategoryItem({
    Key? key,
    required this.category,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: ResponsiveHelper.responsiveEdgeInsetsCustom(
          context,
          horizontal: 4,
        ),
        padding: ResponsiveHelper.responsiveEdgeInsetsCustom(
          context,
          vertical: 4,
          horizontal: 8,
        ),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF4C53A5) : Colors.white,
          borderRadius: BorderRadius.circular(
            ResponsiveHelper.responsiveBorderRadius(
              context,
              mobile: 18,
              tablet: 20,
              desktop: 22,
            ),
          ),
          border: Border.all(
            color: isSelected ? const Color(0xFF4C53A5) : Colors.grey.shade300,
            width: ResponsiveHelper.responsiveBorderWidth(context),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: ResponsiveHelper.responsiveSize(
                context,
                mobile: 1,
                tablet: 1.5,
                desktop: 2,
              ),
              blurRadius: ResponsiveHelper.responsiveSize(
                context,
                mobile: 1.5,
                tablet: 2,
                desktop: 2.5,
              ),
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            OptimizedImage(
              imagePath: category["image"],
              width: ResponsiveHelper.responsiveSize(
                context,
                mobile: 26,
                tablet: 30,
                desktop: 34,
              ),
              height: ResponsiveHelper.responsiveSize(
                context,
                mobile: 26,
                tablet: 30,
                desktop: 34,
              ),
              fit: BoxFit.contain,
            ),
            SizedBox(
              width: ResponsiveHelper.responsiveSpacing(
                context,
                mobile: 4,
                tablet: 5,
                desktop: 6,
              ),
            ),
            Text(
              category["name"],
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: ResponsiveHelper.responsiveFontSize(
                  context,
                  mobile: 10,
                  tablet: 11,
                  desktop: 12,
                ),
                color: isSelected ? Colors.white : const Color(0xFF4C53A5),
              ),
            )
          ],
        ),
      ),
    );
  }
}
