import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:test2/utils/AppColors.dart';
import 'package:test2/services/SettingsService.dart'; // استيراد خدمة الإعدادات
import 'package:test2/services/NotificationService.dart'; // استيراد خدمة الإشعارات
import 'package:test2/services/LocationService.dart'; // استيراد خدمة الموقع
import 'package:test2/pages/SettingsTestPage.dart'; // استيراد صفحة اختبار الإعدادات
import 'package:test2/pages/GPSTestPage.dart'; // استيراد صفحة اختبار GPS
import 'package:test2/widgets/LoadingDialogs.dart'; // استيراد حوارات التحميل
import 'package:test2/widgets/CustomSnackBars.dart'; // استيراد الرسائل المخصصة
import 'package:test2/widgets/CustomDialogs.dart'; // استيراد الحوارات المخصصة
import 'package:permission_handler/permission_handler.dart'; // استيراد مكتبة الأذونات
import 'package:geolocator/geolocator.dart'; // استيراد مكتبة الموقع

class SettingsPage extends StatefulWidget {
  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  // إنشاء مثيل من خدمة الإعدادات للوصول لجميع الإعدادات
  final SettingsService _settingsService = SettingsService();
  // إنشاء مثيل من خدمة الموقع لإدارة GPS
  final LocationService _locationService = LocationService();

  // متغيرات محلية لحفظ حالة الإعدادات في واجهة المستخدم
  bool _notificationsEnabled = true; // حالة الإشعارات
  bool _locationEnabled = true; // حالة تحديد الموقع
  bool _darkModeEnabled = false; // حالة الوضع الليلي
  String _selectedLanguage = 'العربية'; // اللغة المختارة
  bool _soundEnabled = true; // حالة الأصوات
  bool _vibrationEnabled = true; // حالة الاهتزاز
  bool _autoUpdateEnabled = true; // حالة التحديث التلقائي
  bool _dataUsageEnabled = true; // حالة استخدام البيانات
  bool _cacheEnabled = true; // حالة التخزين المؤقت
  bool _analyticsEnabled = true; // حالة التحليلات

  @override
  void initState() {
    super.initState();
    _loadSettings(); // تحميل الإعدادات عند بدء الصفحة
  }

  /// تحميل جميع الإعدادات من خدمة الإعدادات
  Future<void> _loadSettings() async {
    await _settingsService.loadSettings(); // تحميل الإعدادات من التخزين المحلي

    // تحديث متغيرات واجهة المستخدم بالقيم المحملة
    setState(() {
      _notificationsEnabled =
          _settingsService.notificationsEnabled; // تحديث حالة الإشعارات
      _locationEnabled = _settingsService.locationEnabled; // تحديث حالة الموقع
      _darkModeEnabled =
          _settingsService.darkModeEnabled; // تحديث حالة الوضع الليلي
      _selectedLanguage =
          _settingsService.selectedLanguage; // تحديث اللغة المختارة
      _soundEnabled = _settingsService.soundEnabled; // تحديث حالة الأصوات
      _vibrationEnabled =
          _settingsService.vibrationEnabled; // تحديث حالة الاهتزاز
      _autoUpdateEnabled =
          _settingsService.autoUpdateEnabled; // تحديث حالة التحديث التلقائي
      _dataUsageEnabled =
          _settingsService.dataUsageEnabled; // تحديث حالة استخدام البيانات
      _cacheEnabled =
          _settingsService.cacheEnabled; // تحديث حالة التخزين المؤقت
      _analyticsEnabled =
          _settingsService.analyticsEnabled; // تحديث حالة التحليلات
    });

    _settingsService.printCurrentSettings(); // طباعة الإعدادات الحالية للتشخيص
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
        centerTitle: true,
        title: Text(
          "الإعدادات",
          style: AppTextStyles.appBarTitle,
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: AppColors.whiteColor,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: ListView(
        children: [
          Container(
            padding: EdgeInsets.only(top: AppDimensions.paddingMedium),
            decoration: BoxDecoration(
              color: AppColors.backgroundColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.borderRadiusXLarge),
                topRight: Radius.circular(AppDimensions.borderRadiusXLarge),
              ),
            ),
            child: Column(
              children: [
                // إعدادات الحساب
                _buildSectionTitle("إعدادات الحساب"),
                _buildSettingsCard([
                  _buildSettingItem(
                    icon: Icons.person_outline,
                    title: "تعديل الملف الشخصي",
                    subtitle: "تحديث معلوماتك الشخصية",
                    onTap: () {},
                  ),
                  _buildSettingItem(
                    icon: Icons.lock_outline,
                    title: "تغيير كلمة المرور",
                    subtitle: "تحديث كلمة المرور الخاصة بك",
                    onTap: () {},
                  ),
                  _buildSettingItem(
                    icon: Icons.location_on_outlined,
                    title: "إدارة العناوين",
                    subtitle: "إضافة أو تعديل عناوين التوصيل",
                    onTap: () {},
                  ),
                ]),

                SizedBox(height: 20.h),

                // إعدادات التطبيق
                _buildSectionTitle("إعدادات التطبيق"),
                _buildSettingsCard([
                  _buildSwitchItem(
                    icon: Icons.notifications_outlined,
                    title: "الإشعارات",
                    subtitle: "تلقي إشعارات الطلبات والعروض",
                    value: _notificationsEnabled,
                    onChanged: (value) async {
                      // حفظ الإعداد الجديد في خدمة الإعدادات
                      bool success =
                          await _settingsService.setNotificationsEnabled(value);
                      if (success) {
                        // تحديث واجهة المستخدم فقط إذا تم الحفظ بنجاح
                        setState(() {
                          _notificationsEnabled =
                              value; // تحديث حالة الإشعارات في الواجهة
                        });
                        // تطبيق الإعداد على نظام الإشعارات
                        if (value) {
                          print(
                              'تم تفعيل الإشعارات - سيتم إرسال الإشعارات الآن');
                        } else {
                          print(
                              'تم إلغاء تفعيل الإشعارات - لن يتم إرسال إشعارات جديدة');
                        }

                        // عرض رسالة تأكيد للمستخدم
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'تم ${value ? 'تفعيل' : 'إلغاء تفعيل'} الإشعارات'),
                            backgroundColor: Colors.green,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      } else {
                        // عرض رسالة خطأ إذا فشل الحفظ
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('حدث خطأ في حفظ الإعدادات'),
                            backgroundColor: Colors.red,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    },
                  ),
                  _buildSwitchItem(
                    icon: Icons.location_on_outlined,
                    title: "تحديد الموقع",
                    subtitle: "السماح بالوصول إلى موقعك",
                    value: _locationEnabled,
                    onChanged: (value) async {
                      // عرض حوار التحميل الجميل
                      LoadingDialogs.showSettingsLoading(context, value);

                      try {
                        // حفظ إعداد الموقع في خدمة الإعدادات
                        bool success =
                            await _settingsService.setLocationEnabled(value);

                        if (success) {
                          // تطبيق الإعداد على خدمة الموقع
                          await _locationService.updateLocationSettings(value);

                          // تحديث واجهة المستخدم عند نجاح الحفظ
                          setState(() {
                            _locationEnabled =
                                value; // تحديث حالة الموقع في الواجهة
                          });

                          // إغلاق حوار التحميل
                          LoadingDialogs.hideLoadingDialog(context);

                          // عرض رسالة نجاح جميلة
                          if (value) {
                            CustomSnackBars.showSuccess(
                              context,
                              message: 'تم تفعيل خدمة الموقع بنجاح!',
                              subtitle: 'يمكنك الآن تتبع طلباتك على الخريطة',
                              actionLabel: 'اختبار',
                              onAction: () => _testLocationService(),
                            );
                          } else {
                            CustomSnackBars.showInfo(
                              context,
                              message: 'تم إلغاء تفعيل خدمة الموقع',
                              subtitle: 'لن تتمكن من تتبع الطلبات على الخريطة',
                            );
                          }

                          // إذا تم التفعيل، عرض معلومات إضافية
                          if (value) {
                            _showLocationStatusInfo();
                          }
                        } else {
                          // إغلاق حوار التحميل
                          LoadingDialogs.hideLoadingDialog(context);

                          // عرض رسالة خطأ جميلة
                          CustomSnackBars.showError(
                            context,
                            message: 'فشل في حفظ إعدادات الموقع',
                            subtitle: 'يرجى المحاولة مرة أخرى',
                            actionLabel: 'إعادة المحاولة',
                          );
                        }
                      } catch (e) {
                        // إغلاق حوار التحميل في حالة الخطأ
                        LoadingDialogs.hideLoadingDialog(context);

                        // عرض رسالة خطأ جميلة
                        CustomSnackBars.showError(
                          context,
                          message: 'خطأ في تطبيق إعدادات الموقع',
                          subtitle: e.toString(),
                        );
                      }
                    },
                  ),
                  _buildSwitchItem(
                    icon: Icons.dark_mode_outlined,
                    title: "الوضع الليلي",
                    subtitle: "تفعيل المظهر الداكن",
                    value: _darkModeEnabled,
                    onChanged: (value) async {
                      // حفظ إعداد الوضع الليلي في خدمة الإعدادات
                      bool success =
                          await _settingsService.setDarkModeEnabled(value);
                      if (success) {
                        // تحديث واجهة المستخدم عند نجاح الحفظ
                        setState(() {
                          _darkModeEnabled =
                              value; // تحديث حالة الوضع الليلي في الواجهة
                        });
                        // عرض رسالة تأكيد
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'تم ${value ? 'تفعيل' : 'إلغاء تفعيل'} الوضع الليلي'),
                            backgroundColor: Colors.green,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      } else {
                        // عرض رسالة خطأ عند فشل الحفظ
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content:
                                Text('حدث خطأ في حفظ إعدادات الوضع الليلي'),
                            backgroundColor: Colors.red,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    },
                  ),
                  _buildLanguageItem(),

                  // إعدادات إضافية
                  _buildSwitchItem(
                    icon: Icons.volume_up_outlined,
                    title: "الأصوات",
                    subtitle: "تشغيل أصوات التطبيق",
                    value: _soundEnabled,
                    onChanged: (value) async {
                      // حفظ إعداد الأصوات في خدمة الإعدادات
                      bool success =
                          await _settingsService.setSoundEnabled(value);
                      if (success) {
                        // تحديث واجهة المستخدم عند نجاح الحفظ
                        setState(() {
                          _soundEnabled =
                              value; // تحديث حالة الأصوات في الواجهة
                        });
                        // عرض رسالة تأكيد
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'تم ${value ? 'تفعيل' : 'إلغاء تفعيل'} الأصوات'),
                            backgroundColor: Colors.green,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      } else {
                        // عرض رسالة خطأ عند فشل الحفظ
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('حدث خطأ في حفظ إعدادات الأصوات'),
                            backgroundColor: Colors.red,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    },
                  ),

                  _buildSwitchItem(
                    icon: Icons.vibration_outlined,
                    title: "الاهتزاز",
                    subtitle: "تفعيل الاهتزاز للإشعارات",
                    value: _vibrationEnabled,
                    onChanged: (value) async {
                      // حفظ إعداد الاهتزاز في خدمة الإعدادات
                      bool success =
                          await _settingsService.setVibrationEnabled(value);
                      if (success) {
                        // تحديث واجهة المستخدم عند نجاح الحفظ
                        setState(() {
                          _vibrationEnabled =
                              value; // تحديث حالة الاهتزاز في الواجهة
                        });
                        // عرض رسالة تأكيد
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'تم ${value ? 'تفعيل' : 'إلغاء تفعيل'} الاهتزاز'),
                            backgroundColor: Colors.green,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      } else {
                        // عرض رسالة خطأ عند فشل الحفظ
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('حدث خطأ في حفظ إعدادات الاهتزاز'),
                            backgroundColor: Colors.red,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    },
                  ),
                ]),

                SizedBox(height: 20.h),

                // إعدادات متقدمة
                _buildSectionTitle("إعدادات متقدمة"),
                _buildSettingsCard([
                  _buildSwitchItem(
                    icon: Icons.update_outlined,
                    title: "التحديث التلقائي",
                    subtitle: "تحديث التطبيق تلقائياً",
                    value: _autoUpdateEnabled,
                    onChanged: (value) async {
                      // حفظ إعداد التحديث التلقائي في خدمة الإعدادات
                      bool success =
                          await _settingsService.setAutoUpdateEnabled(value);
                      if (success) {
                        // تحديث واجهة المستخدم عند نجاح الحفظ
                        setState(() {
                          _autoUpdateEnabled =
                              value; // تحديث حالة التحديث التلقائي في الواجهة
                        });
                        // عرض رسالة تأكيد
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'تم ${value ? 'تفعيل' : 'إلغاء تفعيل'} التحديث التلقائي'),
                            backgroundColor: Colors.green,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      } else {
                        // عرض رسالة خطأ عند فشل الحفظ
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content:
                                Text('حدث خطأ في حفظ إعدادات التحديث التلقائي'),
                            backgroundColor: Colors.red,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    },
                  ),

                  _buildSwitchItem(
                    icon: Icons.data_usage_outlined,
                    title: "استخدام البيانات",
                    subtitle: "السماح باستخدام بيانات الإنترنت",
                    value: _dataUsageEnabled,
                    onChanged: (value) async {
                      // حفظ إعداد استخدام البيانات في خدمة الإعدادات
                      bool success =
                          await _settingsService.setDataUsageEnabled(value);
                      if (success) {
                        // تحديث واجهة المستخدم عند نجاح الحفظ
                        setState(() {
                          _dataUsageEnabled =
                              value; // تحديث حالة استخدام البيانات في الواجهة
                        });
                        // عرض رسالة تأكيد
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'تم ${value ? 'تفعيل' : 'إلغاء تفعيل'} استخدام البيانات'),
                            backgroundColor: Colors.green,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      } else {
                        // عرض رسالة خطأ عند فشل الحفظ
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content:
                                Text('حدث خطأ في حفظ إعدادات استخدام البيانات'),
                            backgroundColor: Colors.red,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    },
                  ),

                  _buildSwitchItem(
                    icon: Icons.storage_outlined,
                    title: "التخزين المؤقت",
                    subtitle: "حفظ البيانات مؤقتاً لتسريع التطبيق",
                    value: _cacheEnabled,
                    onChanged: (value) async {
                      // حفظ إعداد التخزين المؤقت في خدمة الإعدادات
                      bool success =
                          await _settingsService.setCacheEnabled(value);
                      if (success) {
                        // تحديث واجهة المستخدم عند نجاح الحفظ
                        setState(() {
                          _cacheEnabled =
                              value; // تحديث حالة التخزين المؤقت في الواجهة
                        });
                        // عرض رسالة تأكيد
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'تم ${value ? 'تفعيل' : 'إلغاء تفعيل'} التخزين المؤقت'),
                            backgroundColor: Colors.green,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      } else {
                        // عرض رسالة خطأ عند فشل الحفظ
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content:
                                Text('حدث خطأ في حفظ إعدادات التخزين المؤقت'),
                            backgroundColor: Colors.red,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    },
                  ),

                  _buildSwitchItem(
                    icon: Icons.analytics_outlined,
                    title: "التحليلات",
                    subtitle: "مشاركة بيانات الاستخدام لتحسين التطبيق",
                    value: _analyticsEnabled,
                    onChanged: (value) async {
                      // حفظ إعداد التحليلات في خدمة الإعدادات
                      bool success =
                          await _settingsService.setAnalyticsEnabled(value);
                      if (success) {
                        // تحديث واجهة المستخدم عند نجاح الحفظ
                        setState(() {
                          _analyticsEnabled =
                              value; // تحديث حالة التحليلات في الواجهة
                        });
                        // عرض رسالة تأكيد
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'تم ${value ? 'تفعيل' : 'إلغاء تفعيل'} التحليلات'),
                            backgroundColor: Colors.green,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      } else {
                        // عرض رسالة خطأ عند فشل الحفظ
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('حدث خطأ في حفظ إعدادات التحليلات'),
                            backgroundColor: Colors.red,
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    },
                  ),

                  // زر إعادة تعيين الإعدادات
                  _buildSettingItem(
                    icon: Icons.restore_outlined,
                    title: "إعادة تعيين الإعدادات",
                    subtitle: "استعادة الإعدادات الافتراضية",
                    onTap: () => _showResetDialog(), // عرض حوار التأكيد
                  ),
                ]),

                SizedBox(height: 20.h),

                // إعدادات أخرى
                _buildSectionTitle("أخرى"),
                _buildSettingsCard([
                  _buildSettingItem(
                    icon: Icons.help_outline,
                    title: "المساعدة والدعم",
                    subtitle: "الحصول على المساعدة",
                    onTap: () {},
                  ),
                  _buildSettingItem(
                    icon: Icons.privacy_tip_outlined,
                    title: "سياسة الخصوصية",
                    subtitle: "اطلع على سياسة الخصوصية",
                    onTap: () {},
                  ),
                  _buildSettingItem(
                    icon: Icons.info_outline,
                    title: "حول التطبيق",
                    subtitle: "معلومات عن زاد اليمن",
                    onTap: () {},
                  ),
                  _buildSettingItem(
                    icon: Icons.star_outline,
                    title: "تقييم التطبيق",
                    subtitle: "قيم تجربتك معنا",
                    onTap: () {},
                  ),
                  _buildSettingItem(
                    icon: Icons.bug_report_outlined,
                    title: "اختبار الإعدادات",
                    subtitle: "اختبر كيفية عمل نظام الإعدادات",
                    onTap: () {
                      // الانتقال لصفحة اختبار الإعدادات
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SettingsTestPage(),
                        ),
                      );
                    },
                  ),
                  _buildSettingItem(
                    icon: Icons.notification_add_outlined,
                    title: "اختبار إشعار",
                    subtitle: "اختبر إرسال إشعار حسب الإعدادات الحالية",
                    onTap: () async {
                      // اختبار إرسال إشعار
                      final notificationService = NotificationService();
                      await notificationService.showNewOfferNotification(
                        offerTitle: 'اختبار الإعدادات',
                        storeName: 'متجر الاختبار',
                        discountPercentage: '25',
                      );

                      // عرض رسالة تأكيد
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content:
                              Text('تم اختبار الإشعار - تحقق من الإعدادات'),
                          backgroundColor: Colors.blue,
                          duration: Duration(seconds: 2),
                        ),
                      );
                    },
                  ),
                  _buildSettingItem(
                    icon: Icons.gps_fixed_outlined,
                    title: "اختبار GPS",
                    subtitle: "اختبر خدمة تحديد الموقع والحصول على الإحداثيات",
                    onTap: () => _testLocationService(),
                  ),
                  _buildSettingItem(
                    icon: Icons.location_searching_outlined,
                    title: "حالة خدمة الموقع",
                    subtitle: "عرض تفاصيل حالة GPS والأذونات",
                    onTap: () => _showDetailedLocationStatus(),
                  ),
                  _buildSettingItem(
                    icon: Icons.developer_mode_outlined,
                    title: "صفحة اختبار GPS شاملة",
                    subtitle: "اختبار متقدم لجميع وظائف نظام الموقع",
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => GPSTestPage(),
                        ),
                      );
                    },
                  ),
                ]),

                SizedBox(height: 30.h),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Container(
      alignment: Alignment.centerRight,
      margin: EdgeInsets.symmetric(
        horizontal: AppDimensions.marginLarge,
        vertical: AppDimensions.marginMedium,
      ),
      child: Text(
        title,
        style: AppTextStyles.titleMedium,
      ),
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: AppDimensions.marginLarge),
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 10,
            spreadRadius: 2,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(children: children),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingMedium),
        child: Row(
          textDirection: TextDirection.rtl,
          children: [
            Container(
              width: 50.w,
              height: 50.h,
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withOpacity(0.1),
                borderRadius:
                    BorderRadius.circular(AppDimensions.borderRadiusSmall),
              ),
              child: Icon(
                icon,
                color: AppColors.primaryColor,
                size: AppDimensions.iconSizeMedium,
              ),
            ),
            SizedBox(width: 15.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                textDirection: TextDirection.rtl,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 3.h),
                  Text(
                    subtitle,
                    style: AppTextStyles.bodySmall,
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_back_ios,
              color: AppColors.textSecondaryColor,
              size: 18.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingMedium),
      child: Row(
        textDirection: TextDirection.rtl,
        children: [
          Container(
            width: 50.w,
            height: 50.h,
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withOpacity(0.1),
              borderRadius:
                  BorderRadius.circular(AppDimensions.borderRadiusSmall),
            ),
            child: Icon(
              icon,
              color: AppColors.primaryColor,
              size: AppDimensions.iconSizeMedium,
            ),
          ),
          SizedBox(width: 15.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              textDirection: TextDirection.rtl,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 3.h),
                Text(
                  subtitle,
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageItem() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingMedium),
      child: Row(
        textDirection: TextDirection.rtl,
        children: [
          Container(
            width: 50.w,
            height: 50.h,
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withOpacity(0.1),
              borderRadius:
                  BorderRadius.circular(AppDimensions.borderRadiusSmall),
            ),
            child: Icon(
              Icons.language,
              color: AppColors.primaryColor,
              size: AppDimensions.iconSizeMedium,
            ),
          ),
          SizedBox(width: 15.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              textDirection: TextDirection.rtl,
              children: [
                Text(
                  "اللغة",
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 3.h),
                Text(
                  "اختيار لغة التطبيق",
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ),
          DropdownButton<String>(
            value: _selectedLanguage,
            underline: SizedBox(),
            items: ['العربية', 'English'].map((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(value),
              );
            }).toList(),
            onChanged: (String? newValue) async {
              if (newValue != null) {
                // حفظ اللغة الجديدة في خدمة الإعدادات
                bool success = await _settingsService.setLanguage(newValue);
                if (success) {
                  // تحديث واجهة المستخدم عند نجاح الحفظ
                  setState(() {
                    _selectedLanguage =
                        newValue; // تحديث اللغة المختارة في الواجهة
                  });
                  // عرض رسالة تأكيد
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم تغيير اللغة إلى: $newValue'),
                      backgroundColor: Colors.green,
                      duration: Duration(seconds: 2),
                    ),
                  );
                } else {
                  // عرض رسالة خطأ عند فشل الحفظ
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ في حفظ إعدادات اللغة'),
                      backgroundColor: Colors.red,
                      duration: Duration(seconds: 2),
                    ),
                  );
                }
              }
            },
          ),
        ],
      ),
    );
  }

  /// عرض حوار تأكيد إعادة تعيين الإعدادات
  void _showResetDialog() {
    showDialog(
      context: context, // السياق الحالي للحوار
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            "إعادة تعيين الإعدادات", // عنوان الحوار
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          content: Text(
            "هل أنت متأكد من أنك تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\n\nسيتم فقدان جميع الإعدادات المخصصة.", // محتوى الحوار
            style: TextStyle(height: 1.5),
          ),
          actions: [
            // زر الإلغاء
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // إغلاق الحوار دون فعل شيء
              },
              child: Text(
                "إلغاء", // نص زر الإلغاء
                style: TextStyle(color: Colors.grey),
              ),
            ),
            // زر التأكيد
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop(); // إغلاق الحوار أولاً
                await _resetAllSettings(); // تنفيذ إعادة التعيين
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red, // لون أحمر للتحذير
              ),
              child: Text(
                "إعادة تعيين", // نص زر التأكيد
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// عرض معلومات حالة الموقع للمستخدم
  Future<void> _showLocationStatusInfo() async {
    try {
      // الحصول على حالة الموقع
      Map<String, bool> status = await _locationService.checkLocationStatus();

      String message = '';
      Color backgroundColor = Colors.blue;

      if (status['fullyEnabled'] == true) {
        message = 'تم تفعيل GPS بنجاح! يمكن الآن تحديد موقعك.';
        backgroundColor = Colors.green;
      } else if (status['serviceEnabled'] == false) {
        message = 'يرجى تفعيل خدمة الموقع في إعدادات الجهاز.';
        backgroundColor = Colors.orange;
      } else if (status['hasPermission'] == false) {
        message = 'يرجى منح إذن الوصول للموقع في إعدادات التطبيق.';
        backgroundColor = Colors.orange;
      } else {
        message = 'تم حفظ الإعدادات. قد تحتاج لإعادة تشغيل التطبيق.';
        backgroundColor = Colors.blue;
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: Duration(seconds: 4),
          action: SnackBarAction(
            label: 'اختبار الموقع',
            textColor: Colors.white,
            onPressed: () => _testLocationService(),
          ),
        ),
      );
    } catch (e) {
      print('خطأ في عرض معلومات الموقع: $e');
    }
  }

  /// عرض حالة مفصلة لخدمة الموقع
  Future<void> _showDetailedLocationStatus() async {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: Container(
            padding: EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(color: AppColors.primaryColor),
                SizedBox(height: 16),
                Text('جاري فحص حالة خدمة الموقع...'),
              ],
            ),
          ),
        ),
      );

      // الحصول على حالة الموقع
      Map<String, bool> status = await _locationService.checkLocationStatus();

      // إغلاق مؤشر التحميل
      Navigator.of(context).pop();

      // عرض النتائج في حوار
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.location_on, color: AppColors.primaryColor),
              SizedBox(width: 8),
              Text('حالة خدمة الموقع'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatusRow(
                  'خدمة الموقع في النظام', status['serviceEnabled'] ?? false),
              _buildStatusRow(
                  'إذن الوصول للموقع', status['hasPermission'] ?? false),
              _buildStatusRow(
                  'إعدادات التطبيق', status['settingsEnabled'] ?? false),
              _buildStatusRow(
                  'الحالة الإجمالية', status['fullyEnabled'] ?? false),
              SizedBox(height: 16),
              Text(
                'ملاحظة: يجب أن تكون جميع الحالات مفعلة للحصول على الموقع بنجاح.',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('إغلاق'),
            ),
            if (!(status['fullyEnabled'] ?? false))
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _fixLocationIssues(status);
                },
                child: Text('إصلاح المشاكل'),
              ),
          ],
        ),
      );
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فحص حالة الموقع: $e'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 4),
        ),
      );
    }
  }

  /// بناء صف حالة
  Widget _buildStatusRow(String title, bool isEnabled) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isEnabled ? Icons.check_circle : Icons.cancel,
            color: isEnabled ? Colors.green : Colors.red,
            size: 20,
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              title,
              style: TextStyle(fontSize: 14),
            ),
          ),
          Text(
            isEnabled ? 'مفعل' : 'معطل',
            style: TextStyle(
              fontSize: 12,
              color: isEnabled ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// إصلاح مشاكل الموقع
  Future<void> _fixLocationIssues(Map<String, bool> status) async {
    List<String> issues = [];

    if (!(status['settingsEnabled'] ?? false)) {
      issues.add('تفعيل إعدادات الموقع في التطبيق');
    }
    if (!(status['serviceEnabled'] ?? false)) {
      issues.add('تفعيل خدمة الموقع في إعدادات الجهاز');
    }
    if (!(status['hasPermission'] ?? false)) {
      issues.add('منح إذن الوصول للموقع');
    }

    String message = 'يرجى القيام بالخطوات التالية:\n\n';
    for (int i = 0; i < issues.length; i++) {
      message += '${i + 1}. ${issues[i]}\n';
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('خطوات الإصلاح'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إغلاق'),
          ),
          if (!(status['serviceEnabled'] ?? false) ||
              !(status['hasPermission'] ?? false))
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                // محاولة فتح إعدادات الموقع أو التطبيق
                try {
                  if (!(status['serviceEnabled'] ?? false)) {
                    await Geolocator.openLocationSettings();
                  } else {
                    await Permission.location.request();
                  }
                } catch (e) {
                  print('خطأ في فتح الإعدادات: $e');
                }
              },
              child: Text('فتح الإعدادات'),
            ),
        ],
      ),
    );
  }

  /// اختبار خدمة الموقع
  Future<void> _testLocationService() async {
    try {
      // عرض حوار اختبار GPS الجميل
      LoadingDialogs.showGPSTestLoading(context);

      // محاولة الحصول على الموقع الحالي
      var position = await _locationService.getCurrentLocation();

      // إغلاق حوار التحميل
      LoadingDialogs.hideLoadingDialog(context);

      if (position != null) {
        // عرض نتيجة نجاح الاختبار
        CustomSnackBars.showSuccess(
          context,
          message: 'اختبار GPS ناجح!',
          subtitle: 'تم الحصول على موقعك: ${position.latitude.toStringAsFixed(4)}, ${position.longitude.toStringAsFixed(4)}',
          actionLabel: 'تفاصيل',
          onAction: () => _showLocationDetails(position),
        );
      } else {
        // عرض رسالة فشل الاختبار
        CustomSnackBars.showWarning(
          context,
          message: 'لم يتم العثور على الموقع',
          subtitle: 'تأكد من تفعيل GPS والأذونات',
          actionLabel: 'إعادة المحاولة',
          onAction: () => _testLocationService(),
        );
      }
    } catch (e) {
      // إغلاق حوار التحميل في حالة الخطأ
      LoadingDialogs.hideLoadingDialog(context);

      // عرض رسالة خطأ جميلة
      CustomSnackBars.showError(
        context,
        message: 'خطأ في اختبار الموقع',
        subtitle: e.toString(),
        actionLabel: 'إعادة المحاولة',
        onAction: () => _testLocationService(),
      );
    }
  }

  /// عرض تفاصيل الموقع
  void _showLocationDetails(position) {
    CustomDialogs.showInfoDialog(
      context,
      title: 'تفاصيل موقعك الحالي',
      message: '''
📍 خط العرض: ${position.latitude.toStringAsFixed(6)}
📍 خط الطول: ${position.longitude.toStringAsFixed(6)}
📏 دقة الموقع: ${position.accuracy.toStringAsFixed(1)} متر
⏰ وقت التحديث: ${DateTime.fromMillisecondsSinceEpoch(position.timestamp!.millisecondsSinceEpoch).toString().split('.')[0]}
🧭 الاتجاه: ${position.heading.toStringAsFixed(1)}°
⚡ السرعة: ${position.speed.toStringAsFixed(1)} م/ث
      ''',
      buttonText: 'رائع!',
      icon: Icons.location_on,
      iconColor: Colors.green,
    );
  }

  /// تنفيذ إعادة تعيين جميع الإعدادات
  Future<void> _resetAllSettings() async {
    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false, // منع إغلاق الحوار بالنقر خارجه
      builder: (context) => Center(
        child: Container(
          padding: EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                  color: AppColors.primaryColor), // مؤشر التحميل
              SizedBox(height: 16),
              Text('جاري إعادة تعيين الإعدادات...'), // رسالة التحميل
            ],
          ),
        ),
      ),
    );

    // تنفيذ إعادة التعيين
    bool success = await _settingsService.resetAllSettings();

    // إغلاق مؤشر التحميل
    Navigator.of(context).pop();

    if (success) {
      // إعادة تحميل الإعدادات في الواجهة
      await _loadSettings();

      // إعادة تطبيق إعدادات الموقع
      await _locationService.reloadAndApplySettings();

      // عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إعادة تعيين جميع الإعدادات بنجاح'), // رسالة النجاح
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );
    } else {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ في إعادة تعيين الإعدادات'), // رسالة الخطأ
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }
}
