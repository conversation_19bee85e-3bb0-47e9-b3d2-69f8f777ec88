import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class CartItemSamples extends StatelessWidget{
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        for (int i = 1 ; i < 4 ; i++)
        Container(
          
          height: 75,
          margin: EdgeInsets.symmetric(horizontal: 10 , vertical: 5),
          padding: EdgeInsets.all(5),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10)
          ),
          child: Row(textDirection: TextDirection.rtl,children: [
            Radio(
              value: '', 
              groupValue: '',
              activeColor: Colors.black, 
              onChanged: (index){},
              ),
              Container(
                height: 30,
                width: 30,
                margin: EdgeInsets.only(left: 15),
                child: Image.asset('images/$i.png'),
              ),
              Padding(
                padding: EdgeInsets.symmetric(vertical: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'المنتج',
                      style: TextStyle(fontSize: 13,fontWeight: FontWeight.bold,color: Colors.black,),
                      ),
                      Text(
                        "\$55",
                        style: TextStyle(fontSize: 12,fontWeight: FontWeight.bold,color: Colors.black),
                        )
                  ],
                ),
                ),
                Spacer(),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 5),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                          margin: EdgeInsets.only(right: 50 , bottom: 3),
                          padding: EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            
                          ),
                          child: Icon(Icons.delete,color: Colors.red , size: 18,),
                        ),
                      
                      Row(textDirection: TextDirection.rtl,children: [
                        Container(
                          padding: EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow:[ 
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.5),
                                spreadRadius: 1,
                                blurRadius: 10,
                              )
                              ]
                          ),
                          child: Icon(
                            CupertinoIcons.plus,
                            size: 15,
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 10),
                          child: Text(
                            "01",
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow:[ 
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.5),
                                spreadRadius: 1,
                                blurRadius: 10,
                              )
                              ]
                          ),
                          child: Icon(
                            CupertinoIcons.minus,
                            size: 15,
                          ),
                        )
                      ],)
                    ],),
                    )
          ],),
        )
      ],
    );
  }
}