import 'package:flutter/material.dart';

class DataManager {
  // Singleton pattern للحصول على نفس المثيل
  static final DataManager _instance = DataManager._internal();
  factory DataManager() => _instance;
  DataManager._internal();

  // قائمة التصنيفات - ثابتة
  static const List<Map<String, dynamic>> categories = [
    {"image": "images/1.png", "name": "الكل"},
    {"image": "images/2.png", "name": "مطاعم"},
    {"image": "images/3.png", "name": "بقالة"},
    {"image": "images/4.png", "name": "صيدليات"},
    {"image": "images/5.png", "name": "ملابس"},
    {"image": "images/6.png", "name": "إلكترونيات"},
    {"image": "images/7.png", "name": "مخابز"},
    {"image": "images/8.png", "name": "حلويات"},
  ];

  // قائمة تصنيفات المتاجر - ثابتة
  static const List<Map<String, dynamic>> storeCategories = [
    {"icon": Icons.apps, "name": "الكل"},
    {"icon": Icons.fastfood, "name": "مطاعم"},
    {"icon": Icons.local_grocery_store, "name": "بقالة"},
    {"icon": Icons.local_pharmacy, "name": "صيدليات"},
    {"icon": Icons.shopping_bag, "name": "ملابس"},
    {"icon": Icons.devices, "name": "إلكترونيات"},
    {"icon": Icons.bakery_dining, "name": "مخابز"},
    {"icon": Icons.cake, "name": "حلويات"},
  ];

  // قائمة المنتجات - ثابتة
  static const List<Map<String, dynamic>> allItems = [
    {
      "id": 1,
      "name": "برجر لحم",
      "category": "مطاعم",
      "price": "25",
      "description": "برجر لحم طازج"
    },
    {
      "id": 2,
      "name": "أرز بسمتي",
      "category": "بقالة",
      "price": "15",
      "description": "أرز بسمتي فاخر"
    },
    {
      "id": 3,
      "name": "بنادول",
      "category": "صيدليات",
      "price": "8",
      "description": "مسكن للألم"
    },
    {
      "id": 4,
      "name": "قميص قطني",
      "category": "ملابس",
      "price": "45",
      "description": "قميص قطني مريح"
    },
    {
      "id": 5,
      "name": "هاتف ذكي",
      "category": "إلكترونيات",
      "price": "800",
      "description": "هاتف ذكي حديث"
    },
    {
      "id": 6,
      "name": "خبز طازج",
      "category": "مخابز",
      "price": "3",
      "description": "خبز طازج يومياً"
    },
    {
      "id": 7,
      "name": "كنافة",
      "category": "حلويات",
      "price": "20",
      "description": "كنافة بالجبن"
    },
    {
      "id": 8,
      "name": "بيتزا مارجريتا",
      "category": "مطاعم",
      "price": "35",
      "description": "بيتزا إيطالية أصلية"
    },
    {
      "id": 9,
      "name": "زيت زيتون",
      "category": "بقالة",
      "price": "22",
      "description": "زيت زيتون بكر"
    },
    {
      "id": 10,
      "name": "فيتامين سي",
      "category": "صيدليات",
      "price": "12",
      "description": "مكمل غذائي"
    },
    {
      "id": 11,
      "name": "جينز أزرق",
      "category": "ملابس",
      "price": "65",
      "description": "بنطلون جينز عصري"
    },
    {
      "id": 12,
      "name": "سماعات",
      "category": "إلكترونيات",
      "price": "120",
      "description": "سماعات لاسلكية"
    },
    {
      "id": 13,
      "name": "كرواسان",
      "category": "مخابز",
      "price": "5",
      "description": "كرواسان بالزبدة"
    },
    {
      "id": 14,
      "name": "تشيز كيك",
      "category": "حلويات",
      "price": "18",
      "description": "تشيز كيك بالفراولة"
    },
  ];

  // قائمة المتاجر - ثابتة
  static const List<Map<String, dynamic>> allStores = [
    {
      "name": "مطعم الشرق",
      "category": "مطاعم",
      "rating": 4.8,
      "deliveryTime": "30-45 دقيقة",
      "image": "images/1.png",
      "isOpen": true, // مفتوح
    },
    {
      "name": "سوبرماركت الأمانة",
      "category": "بقالة",
      "rating": 4.5,
      "deliveryTime": "20-30 دقيقة",
      "image": "images/2.png",
      "isOpen": false, // مغلق
    },
    {
      "name": "صيدلية الصحة",
      "category": "صيدليات",
      "rating": 4.7,
      "deliveryTime": "15-25 دقيقة",
      "image": "images/3.png",
      "isOpen": true, // مفتوح
    },
    {
      "name": "متجر الأناقة",
      "category": "ملابس",
      "rating": 4.3,
      "deliveryTime": "40-60 دقيقة",
      "image": "images/4.png",
      "isOpen": false, // مغلق
    },
    {
      "name": "متجر التقنية",
      "category": "إلكترونيات",
      "rating": 4.6,
      "deliveryTime": "35-50 دقيقة",
      "image": "images/5.png",
      "isOpen": true, // مفتوح
    },
    {
      "name": "مخبز الأسرة",
      "category": "مخابز",
      "rating": 4.4,
      "deliveryTime": "15-20 دقيقة",
      "image": "images/6.png",
      "isOpen": false, // مغلق
    },
    {
      "name": "حلويات دمشق",
      "category": "حلويات",
      "rating": 4.9,
      "deliveryTime": "25-35 دقيقة",
      "image": "images/7.png",
      "isOpen": true, // مفتوح
    },
    {
      "name": "مطعم البرجر",
      "category": "مطاعم",
      "rating": 4.2,
      "deliveryTime": "20-30 دقيقة",
      "image": "images/8.png",
      "isOpen": false, // مغلق
    },
  ];

  // Cache للبيانات المفلترة
  static final Map<String, List<Map<String, dynamic>>> _itemsCache = {};
  static final Map<String, List<Map<String, dynamic>>> _storesCache = {};

  // فلترة المنتجات مع التخزين المؤقت
  static List<Map<String, dynamic>> getFilteredItems(String category) {
    if (_itemsCache.containsKey(category)) {
      return _itemsCache[category]!;
    }

    List<Map<String, dynamic>> filtered = category == "الكل"
        ? List.from(allItems)
        : allItems.where((item) => item["category"] == category).toList();

    _itemsCache[category] = filtered;
    return filtered;
  }

  // فلترة المتاجر مع التخزين المؤقت
  static List<Map<String, dynamic>> getFilteredStores(String category) {
    if (_storesCache.containsKey(category)) {
      return _storesCache[category]!;
    }

    List<Map<String, dynamic>> filtered = category == "الكل"
        ? List.from(allStores)
        : allStores.where((store) => store["category"] == category).toList();

    _storesCache[category] = filtered;
    return filtered;
  }

  // تنظيف الذاكرة المؤقتة عند الحاجة
  static void clearCache() {
    _itemsCache.clear();
    _storesCache.clear();
  }
}
