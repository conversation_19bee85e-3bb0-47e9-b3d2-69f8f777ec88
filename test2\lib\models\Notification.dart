/// نموذج الإشعار
class AppNotification {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime timestamp;
  final bool isRead;
  final Map<String, dynamic>? data;
  final String? imageUrl;
  final String? actionUrl;
  final NotificationPriority priority;

  AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.data,
    this.imageUrl,
    this.actionUrl,
    this.priority = NotificationPriority.normal,
  });

  /// تحويل من JSON
  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      type: NotificationType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => NotificationType.general,
      ),
      timestamp:
          DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      isRead: json['isRead'] ?? false,
      data: json['data'] as Map<String, dynamic>?,
      imageUrl: json['imageUrl'],
      actionUrl: json['actionUrl'],
      priority: NotificationPriority.values.firstWhere(
        (e) => e.toString() == json['priority'],
        orElse: () => NotificationPriority.normal,
      ),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type.toString(),
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
      'data': data,
      'imageUrl': imageUrl,
      'actionUrl': actionUrl,
      'priority': priority.toString(),
    };
  }

  /// نسخ مع تعديل
  AppNotification copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    DateTime? timestamp,
    bool? isRead,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? actionUrl,
    NotificationPriority? priority,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
      priority: priority ?? this.priority,
    );
  }

  /// الحصول على الوقت المنسق
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  /// الحصول على أيقونة النوع
  String get typeIcon {
    switch (type) {
      case NotificationType.orderConfirmed:
        return '✅';
      case NotificationType.orderPreparing:
        return '👨‍🍳';
      case NotificationType.orderReady:
        return '🍽️';
      case NotificationType.orderDelivered:
        return '🚚';
      case NotificationType.offer:
        return '🔥';
      case NotificationType.newProduct:
        return '🆕';
      case NotificationType.newStore:
        return '🏪';
      case NotificationType.rating:
        return '⭐';
      case NotificationType.general:
        return '📢';
      default:
        return '🔔';
    }
  }

  /// الحصول على لون النوع
  String get typeColor {
    switch (type) {
      case NotificationType.orderConfirmed:
        return '#4CAF50';
      case NotificationType.orderPreparing:
        return '#FF9800';
      case NotificationType.orderReady:
        return '#2196F3';
      case NotificationType.orderDelivered:
        return '#4CAF50';
      case NotificationType.offer:
        return '#F44336';
      case NotificationType.newProduct:
        return '#9C27B0';
      case NotificationType.newStore:
        return '#3F51B5';
      case NotificationType.rating:
        return '#FFC107';
      case NotificationType.general:
        return '#607D8B';
      default:
        return '#9E9E9E';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppNotification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AppNotification(id: $id, title: $title, type: $type, isRead: $isRead)';
  }
}

/// أنواع الإشعارات
enum NotificationType {
  general, // عام
  orderConfirmed, // تأكيد الطلب
  orderPreparing, // تحضير الطلب
  orderReady, // الطلب جاهز
  orderDelivered, // تم التوصيل
  offer, // عرض
  newProduct, // منتج جديد
  newStore, // متجر جديد
  rating, // تقييم
  system, // نظام
}

/// أولوية الإشعار
enum NotificationPriority {
  low, // منخفضة
  normal, // عادية
  high, // عالية
  urgent, // عاجلة
}

/// إعدادات الإشعارات
class NotificationSettings {
  final bool isEnabled;
  final bool orderUpdates;
  final bool offers;
  final bool newProducts;
  final bool newStores;
  final bool general;
  final bool sound;
  final bool vibration;
  final bool badge;
  final String quietHoursStart;
  final String quietHoursEnd;
  final bool quietHoursEnabled;

  NotificationSettings({
    this.isEnabled = true,
    this.orderUpdates = true,
    this.offers = true,
    this.newProducts = true,
    this.newStores = true,
    this.general = true,
    this.sound = true,
    this.vibration = true,
    this.badge = true,
    this.quietHoursStart = '22:00',
    this.quietHoursEnd = '08:00',
    this.quietHoursEnabled = false,
  });

  /// تحويل من JSON
  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      isEnabled: json['isEnabled'] ?? true,
      orderUpdates: json['orderUpdates'] ?? true,
      offers: json['offers'] ?? true,
      newProducts: json['newProducts'] ?? true,
      newStores: json['newStores'] ?? true,
      general: json['general'] ?? true,
      sound: json['sound'] ?? true,
      vibration: json['vibration'] ?? true,
      badge: json['badge'] ?? true,
      quietHoursStart: json['quietHoursStart'] ?? '22:00',
      quietHoursEnd: json['quietHoursEnd'] ?? '08:00',
      quietHoursEnabled: json['quietHoursEnabled'] ?? false,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'isEnabled': isEnabled,
      'orderUpdates': orderUpdates,
      'offers': offers,
      'newProducts': newProducts,
      'newStores': newStores,
      'general': general,
      'sound': sound,
      'vibration': vibration,
      'badge': badge,
      'quietHoursStart': quietHoursStart,
      'quietHoursEnd': quietHoursEnd,
      'quietHoursEnabled': quietHoursEnabled,
    };
  }

  /// نسخ مع تعديل
  NotificationSettings copyWith({
    bool? isEnabled,
    bool? orderUpdates,
    bool? offers,
    bool? newProducts,
    bool? newStores,
    bool? general,
    bool? sound,
    bool? vibration,
    bool? badge,
    String? quietHoursStart,
    String? quietHoursEnd,
    bool? quietHoursEnabled,
  }) {
    return NotificationSettings(
      isEnabled: isEnabled ?? this.isEnabled,
      orderUpdates: orderUpdates ?? this.orderUpdates,
      offers: offers ?? this.offers,
      newProducts: newProducts ?? this.newProducts,
      newStores: newStores ?? this.newStores,
      general: general ?? this.general,
      sound: sound ?? this.sound,
      vibration: vibration ?? this.vibration,
      badge: badge ?? this.badge,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      quietHoursEnabled: quietHoursEnabled ?? this.quietHoursEnabled,
    );
  }

  /// التحقق من كون الوقت الحالي في ساعات الهدوء
  bool get isQuietTime {
    if (!quietHoursEnabled) return false;

    final now = DateTime.now();
    final currentHour = now.hour;
    final currentMinute = now.minute;

    final startParts = quietHoursStart.split(':');
    final endParts = quietHoursEnd.split(':');

    final startHour = int.parse(startParts[0]);
    final startMinute = int.parse(startParts[1]);

    final endHour = int.parse(endParts[0]);
    final endMinute = int.parse(endParts[1]);

    final currentMinutes = currentHour * 60 + currentMinute;
    final startMinutes = startHour * 60 + startMinute;
    final endMinutes = endHour * 60 + endMinute;

    if (endMinutes > startMinutes) {
      // نفس اليوم
      return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
    } else {
      // يمتد للليل
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
    }
  }
}
