import 'package:flutter/material.dart';

/// ألوان التطبيق الموحدة - مطابقة للشاشة الرئيسية
class AppColors {
  // الألوان الأساسية من الشاشة الرئيسية
  static const Color primaryColor = Color(0xFFC3243B); // اللون الأحمر الرئيسي
  static const Color secondaryColor =
      Color(0xFF4C53A5); // اللون البنفسجي الثانوي
  static const Color accentColor = Color(0xFFF5AA49); // لون التمييز البرتقالي

  // ألوان الخلفية
  static const Color backgroundColor = Color(0xFFEDECF2); // خلفية فاتحة رمادية
  static const Color surfaceColor = Colors.white; // خلفية البطاقات
  static const Color cardBackgroundColor = Colors.white; // خلفية البطاقات

  // ألوان النص
  static const Color textPrimaryColor = Colors.black; // نص أساسي
  static const Color textSecondaryColor = Colors.grey; // نص ثانوي
  static const Color textOnPrimaryColor =
      Colors.white; // نص على الخلفية الأساسية
  static const Color textOnSecondaryColor =
      Colors.white; // نص على الخلفية الثانوية

  // ألوان الحالة
  static const Color successColor = Colors.green; // نجاح
  static const Color errorColor = Colors.red; // خطأ
  static const Color warningColor = Colors.orange; // تحذير
  static const Color infoColor = Colors.blue; // معلومات

  // ألوان التدرج
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF4C53A5), Color(0xFF6366F1)],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [Color(0xFFC3243B), Color(0xFFE53E3E)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // ألوان إضافية
  static const Color dividerColor = Color(0xFFE0E0E0); // خطوط الفصل
  static const Color shadowColor = Color(0x1A000000); // الظلال
  static const Color lightShadowColor = Color(0x0A000000); // ظلال فاتحة
  static const Color borderColor = Color(0xFFE0E0E0); // الحدود
  static const Color disabledColor = Color(0xFFBDBDBD); // العناصر المعطلة

  // ألوان الشارات والإشعارات
  static const Color badgeColor = Color(0xFFF5AA49); // شارة السلة
  static const Color notificationBadgeColor = Colors.red; // شارة الإشعارات

  // ألوان التبويبات
  static const Color tabSelectedColor = Color(0xFFC3243B); // التبويب المحدد
  static const Color tabUnselectedColor =
      Colors.transparent; // التبويب غير المحدد
  static const Color tabBorderColor = Color(0xFFBDBDBD); // حدود التبويبات

  // ألوان الأزرار
  static const Color buttonPrimaryColor = Color(0xFFC3243B); // الزر الأساسي
  static const Color buttonSecondaryColor = Color(0xFF4C53A5); // الزر الثانوي
  static const Color buttonDisabledColor = Color(0xFFBDBDBD); // الزر المعطل

  // ألوان خاصة بالإشعارات
  static const Color notificationUnreadColor =
      Color(0xFFF3F4F6); // إشعار غير مقروء
  static const Color notificationReadColor = Colors.white; // إشعار مقروء

  // الألوان القديمة للتوافق مع الكود الموجود
  static const Color whiteColor = Colors.white;
  static const Color blackColor = Colors.black;
  static const Color greyColor = Colors.grey;
  static const Color redColor = Colors.red;
  static const Color greenColor = Colors.green;
  static const Color transparentColor = Colors.transparent;
  static const Color textHintColor = Color(0xFFBDBDBD);

  // دوال مساعدة للحصول على ألوان بشفافية
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  static Color primaryWithOpacity(double opacity) {
    return primaryColor.withOpacity(opacity);
  }

  static Color secondaryWithOpacity(double opacity) {
    return secondaryColor.withOpacity(opacity);
  }
}

class AppTextStyles {
  // أنماط النصوص المختلفة
  static const TextStyle titleLarge = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimaryColor,
  );

  static const TextStyle titleMedium = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimaryColor,
  );

  static const TextStyle titleSmall = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimaryColor,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.blackColor,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.blackColor,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondaryColor,
  );

  static const TextStyle buttonText = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: AppColors.whiteColor,
  );

  static const TextStyle appBarTitle = TextStyle(
    fontSize: 23,
    fontWeight: FontWeight.bold,
    color: AppColors.whiteColor,
  );
}

class AppDimensions {
  // الأبعاد والمسافات المختلفة
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 15.0;
  static const double paddingLarge = 20.0;
  static const double paddingXLarge = 30.0;

  static const double marginSmall = 5.0;
  static const double marginMedium = 10.0;
  static const double marginLarge = 15.0;
  static const double marginXLarge = 20.0;

  static const double borderRadiusSmall = 10.0;
  static const double borderRadiusMedium = 20.0;
  static const double borderRadiusLarge = 30.0;
  static const double borderRadiusXLarge = 35.0;

  static const double iconSizeSmall = 20.0;
  static const double iconSizeMedium = 28.0;
  static const double iconSizeLarge = 35.0;

  static const double buttonHeight = 50.0;
  static const double appBarHeight = 70.0;
  static const double bottomNavBarHeight = 70.0;
}
