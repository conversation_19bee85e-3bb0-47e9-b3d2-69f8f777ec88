import 'package:flutter/material.dart';

class AppColors {
  // الألوان الأساسية للتطبيق
  static const Color primaryColor = Color(0xFFC3243B);      // اللون الأحمر الأساسي
  static const Color backgroundColor = Color(0xFFEDECF2);   // لون الخلفية الرمادي الفاتح
  static const Color textPrimaryColor = Color(0xFF4C53A5);  // لون النص الأساسي الأزرق الداكن
  static const Color accentColor = Color(0xFFF5AA49);       // لون التمييز البرتقالي
  static const Color whiteColor = Colors.white;             // اللون الأبيض
  static const Color blackColor = Colors.black;             // اللون الأسود
  static const Color greyColor = Colors.grey;               // اللون الرمادي
  static const Color redColor = Colors.red;                 // اللون الأحمر
  static const Color greenColor = Colors.green;             // اللون الأخضر
  static const Color transparentColor = Colors.transparent; // اللون الشفاف

  // ألوان إضافية للحالات المختلفة
  static const Color successColor = Color(0xFF4CAF50);      // لون النجاح
  static const Color warningColor = Color(0xFFFF9800);      // لون التحذير
  static const Color errorColor = Color(0xFFF44336);        // لون الخطأ
  static const Color infoColor = Color(0xFF2196F3);         // لون المعلومات

  // ألوان الظلال
  static const Color shadowColor = Color(0x29000000);       // لون الظل
  static const Color lightShadowColor = Color(0x1A000000);  // لون الظل الفاتح

  // ألوان النصوص
  static const Color textSecondaryColor = Color(0xFF757575); // لون النص الثانوي
  static const Color textHintColor = Color(0xFFBDBDBD);      // لون النص التوضيحي
}

class AppTextStyles {
  // أنماط النصوص المختلفة
  static const TextStyle titleLarge = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimaryColor,
  );

  static const TextStyle titleMedium = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimaryColor,
  );

  static const TextStyle titleSmall = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimaryColor,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.blackColor,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.blackColor,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondaryColor,
  );

  static const TextStyle buttonText = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: AppColors.whiteColor,
  );

  static const TextStyle appBarTitle = TextStyle(
    fontSize: 23,
    fontWeight: FontWeight.bold,
    color: AppColors.whiteColor,
  );
}

class AppDimensions {
  // الأبعاد والمسافات المختلفة
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 15.0;
  static const double paddingLarge = 20.0;
  static const double paddingXLarge = 30.0;

  static const double marginSmall = 5.0;
  static const double marginMedium = 10.0;
  static const double marginLarge = 15.0;
  static const double marginXLarge = 20.0;

  static const double borderRadiusSmall = 10.0;
  static const double borderRadiusMedium = 20.0;
  static const double borderRadiusLarge = 30.0;
  static const double borderRadiusXLarge = 35.0;

  static const double iconSizeSmall = 20.0;
  static const double iconSizeMedium = 28.0;
  static const double iconSizeLarge = 35.0;

  static const double buttonHeight = 50.0;
  static const double appBarHeight = 70.0;
  static const double bottomNavBarHeight = 70.0;
}
