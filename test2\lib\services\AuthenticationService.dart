import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import '../models/UserProfile.dart';
import '../models/Address.dart';
import '../services/CustomerDataService.dart';

/// خدمة المصادقة الذكية - تسجيل دخول مرة واحدة فقط
class AuthenticationService {
  static final AuthenticationService _instance =
      AuthenticationService._internal();
  factory AuthenticationService() => _instance;
  AuthenticationService._internal();

  final CustomerDataService _customerService = CustomerDataService();

  // مفاتيح التخزين
  static const String _isFirstTimeKey = 'is_first_time_app';
  static const String _authTokenKey = 'auth_token';
  static const String _lastLoginKey = 'last_login_time';
  static const String _deviceIdKey = 'device_id';

  bool _isAuthenticated = false;
  String? _authToken;
  DateTime? _lastLogin;

  /// التحقق من حالة المصادقة
  bool get isAuthenticated => _isAuthenticated;
  String? get authToken => _authToken;
  DateTime? get lastLogin => _lastLogin;

  /// تحميل حالة المصادقة عند بدء التطبيق
  Future<void> loadAuthState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحميل التوكن
      _authToken = prefs.getString(_authTokenKey);

      // تحميل آخر تسجيل دخول
      final lastLoginStr = prefs.getString(_lastLoginKey);
      if (lastLoginStr != null) {
        _lastLogin = DateTime.parse(lastLoginStr);
      }

      // التحقق من صحة المصادقة
      await _validateAuthentication();
    } catch (e) {
      print('خطأ في تحميل حالة المصادقة: $e');
      _isAuthenticated = false;
    }
  }

  /// التحقق من صحة المصادقة
  Future<void> _validateAuthentication() async {
    try {
      // التحقق من وجود التوكن
      if (_authToken == null || _authToken!.isEmpty) {
        _isAuthenticated = false;
        return;
      }

      // التحقق من انتهاء صلاحية التوكن (30 يوم)
      if (_lastLogin != null) {
        final daysSinceLogin = DateTime.now().difference(_lastLogin!).inDays;
        if (daysSinceLogin > 30) {
          await logout();
          return;
        }
      }

      // التحقق من وجود بيانات العميل
      await _customerService.ensureDataLoaded();
      if (!_customerService.isLoggedIn ||
          _customerService.customerProfile == null) {
        _isAuthenticated = false;
        return;
      }

      _isAuthenticated = true;
    } catch (e) {
      print('خطأ في التحقق من المصادقة: $e');
      _isAuthenticated = false;
    }
  }

  /// التحقق من الحاجة لتسجيل الدخول
  Future<bool> needsAuthentication() async {
    await loadAuthState();

    // إذا كان مصادق بالفعل، لا حاجة لتسجيل دخول
    if (_isAuthenticated) {
      return false;
    }

    // التحقق من وجود البيانات الأساسية
    await _customerService.ensureDataLoaded();
    if (_customerService.isLoggedIn &&
        _customerService.customerProfile != null) {
      // البيانات موجودة، إنشاء توكن جديد
      await _createAuthToken();
      return false;
    }

    // لا توجد بيانات، يحتاج تسجيل دخول
    return true;
  }

  /// تسجيل دخول جديد (مرة واحدة فقط)
  Future<AuthResult> performInitialLogin({
    required String firstName,
    required String lastName,
    required String phone,
    required String email,
    String? city,
    String? address,
  }) async {
    try {
      // التحقق من صحة البيانات
      final validationResult =
          _validateLoginData(firstName, lastName, phone, email);
      if (!validationResult.success) {
        return validationResult;
      }

      // إنشاء ملف شخصي جديد
      final newProfile = UserProfile(
        id: 'customer_${DateTime.now().millisecondsSinceEpoch}',
        firstName: firstName,
        lastName: lastName,
        email: email,
        phone: phone,
        avatar: '',
        dateOfBirth: null,
        gender: '',
        addresses: city != null && address != null
            ? [
                Address(
                  id: 'initial_address',
                  title: 'المنزل',
                  street: address,
                  district: 'حي رئيسي',
                  city: city,
                  buildingNumber: '1',
                  fullAddress: '$address, $city',
                  isDefault: true,
                )
              ]
            : [],
        defaultAddressId:
            city != null && address != null ? 'initial_address' : null,
        preferences: {},
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final success = await _customerService.saveCustomerData(newProfile);

      if (!success) {
        return AuthResult(
          success: false,
          message: 'فشل في حفظ البيانات',
        );
      }

      // إنشاء توكن المصادقة
      await _createAuthToken();

      // حفظ معلومات تسجيل الدخول
      await _saveLoginInfo();

      return AuthResult(
        success: true,
        message: 'تم تسجيل الدخول بنجاح',
        token: _authToken,
      );
    } catch (e) {
      return AuthResult(
        success: false,
        message: 'حدث خطأ غير متوقع: ${e.toString()}',
      );
    }
  }

  /// التحقق من صحة بيانات تسجيل الدخول
  AuthResult _validateLoginData(
      String firstName, String lastName, String phone, String email) {
    // التحقق من الاسم الأول
    if (firstName.trim().isEmpty || firstName.trim().length < 2) {
      return AuthResult(
        success: false,
        message: 'يرجى إدخال الاسم الأول (حرفين على الأقل)',
      );
    }

    // التحقق من رقم الهاتف
    if (!_customerService.isValidPhone(phone)) {
      return AuthResult(
        success: false,
        message: 'رقم الهاتف يجب أن يكون 9 أرقام ويبدأ بالرقم 7',
      );
    }

    // التحقق من البريد الإلكتروني
    if (email.isNotEmpty && !_customerService.isValidEmail(email)) {
      return AuthResult(
        success: false,
        message: 'يرجى إدخال بريد إلكتروني صحيح',
      );
    }

    return AuthResult(success: true, message: 'البيانات صحيحة');
  }

  /// إنشاء توكن المصادقة
  Future<void> _createAuthToken() async {
    try {
      final deviceId = await _getDeviceId();
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final data = '$deviceId:$timestamp';

      // إنشاء hash للتوكن
      final bytes = utf8.encode(data);
      final digest = sha256.convert(bytes);
      _authToken = digest.toString();

      _isAuthenticated = true;
      _lastLogin = DateTime.now();
    } catch (e) {
      print('خطأ في إنشاء التوكن: $e');
      throw Exception('فشل في إنشاء توكن المصادقة');
    }
  }

  /// الحصول على معرف الجهاز
  Future<String> _getDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? deviceId = prefs.getString(_deviceIdKey);

      if (deviceId == null) {
        // إنشاء معرف جديد للجهاز
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        deviceId = 'device_$timestamp';
        await prefs.setString(_deviceIdKey, deviceId);
      }

      return deviceId;
    } catch (e) {
      return 'unknown_device';
    }
  }

  /// حفظ معلومات تسجيل الدخول
  Future<void> _saveLoginInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (_authToken != null) {
        await prefs.setString(_authTokenKey, _authToken!);
      }

      if (_lastLogin != null) {
        await prefs.setString(_lastLoginKey, _lastLogin!.toIso8601String());
      }

      // تحديد أن التطبيق لم يعد جديد
      await prefs.setBool(_isFirstTimeKey, false);
    } catch (e) {
      print('خطأ في حفظ معلومات تسجيل الدخول: $e');
    }
  }

  /// التحقق من كون التطبيق جديد
  Future<bool> isFirstTimeUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_isFirstTimeKey) ?? true;
    } catch (e) {
      return true;
    }
  }

  /// تحديث آخر نشاط
  Future<void> updateLastActivity() async {
    if (_isAuthenticated) {
      _lastLogin = DateTime.now();
      await _saveLoginInfo();
    }
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // مسح بيانات المصادقة
      await prefs.remove(_authTokenKey);
      await prefs.remove(_lastLoginKey);

      // مسح بيانات العميل
      await _customerService.logout();

      // إعادة تعيين الحالة
      _authToken = null;
      _lastLogin = null;
      _isAuthenticated = false;
    } catch (e) {
      print('خطأ في تسجيل الخروج: $e');
    }
  }

  /// إعادة تعيين التطبيق (للاختبار)
  Future<void> resetApp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      _authToken = null;
      _lastLogin = null;
      _isAuthenticated = false;
    } catch (e) {
      print('خطأ في إعادة تعيين التطبيق: $e');
    }
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool success;
  final String message;
  final String? token;
  final Map<String, dynamic>? data;

  AuthResult({
    required this.success,
    required this.message,
    this.token,
    this.data,
  });

  @override
  String toString() {
    return 'AuthResult(success: $success, message: $message, token: ${token?.substring(0, 10)}...)';
  }
}
