import 'package:flutter/material.dart';

class CartAppBar extends StatelessWidget implements PreferredSizeWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFC3243B),
      padding: EdgeInsets.only(top: 25,left: 25,right: 25,bottom: 10),
      margin: EdgeInsets.only(top: 15),
      child: Row(children: [
        Icon(
          Icons.more_vert,
          size: 20,
          color: Colors.white,
        ),
        
        Spacer(),
        Padding(
          padding: EdgeInsets.only(right: 20),
          child: Text(
            "محتوى السلة",
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.bold,
              color:Colors.white
            ),
          ),
        ),
        InkWell(
          onTap: (){
            if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }else{
                Navigator.pushReplacementNamed(context, "/home");
              }
              
          },
          child: Icon(
            Icons.arrow_forward,
            size:20,
            color:Colors.white
          ),
        ),
        
      ],),
    );
  }
  @override
  Size get preferredSize => Size.fromHeight(50);
}