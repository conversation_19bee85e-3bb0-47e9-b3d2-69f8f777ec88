# دليل المطور - نظام السلة

## 🔧 إعداد نظام السلة

### 1. **إضافة المكتبات المطلوبة**

في `pubspec.yaml`:
```yaml
dependencies:
  provider: ^6.0.5
  shared_preferences: ^2.2.2
  # المكتبات الأخرى موجودة مسبقاً
```

### 2. **تشغيل الأوامر**
```bash
flutter pub get
flutter clean
flutter run
```

## 🏗️ بنية النظام

### الملفات المنشأة:
```
lib/
├── models/
│   ├── CartItem.dart          # نموذج عنصر السلة
│   └── Product.dart           # نموذج المنتج
├── services/
│   └── CartService.dart       # خدمة إدارة السلة
├── providers/
│   └── CartProvider.dart      # مزود حالة السلة
├── widgets/
│   └── CartIcon.dart          # أيقونة السلة مع العداد
└── pages/
    └── CartPage.dart          # صفحة السلة (محدثة)
```

### الملفات المحدثة:
```
lib/
├── main.dart                  # إضافة CartProvider
├── widgets/
│   ├── ItemsWidgets.dart      # إضافة أيقونة السلة للمنتجات
│   └── CustomBottomNavBar.dart # إضافة عداد السلة
```

## 💻 كيفية الاستخدام في الكود

### 1. **إضافة منتج للسلة**
```dart
// في أي صفحة
Consumer<CartProvider>(
  builder: (context, cartProvider, child) {
    return ElevatedButton(
      onPressed: () async {
        final success = await cartProvider.addToCart(product);
        if (success) {
          // عرض رسالة نجاح
        }
      },
      child: Text('أضف للسلة'),
    );
  },
)
```

### 2. **عرض عداد السلة**
```dart
// أيقونة بسيطة
CartIcon()

// أيقونة للشريط العلوي
AppBarCartIcon()

// أيقونة للشريط السفلي
BottomNavCartIcon(isSelected: true)
```

### 3. **التحقق من وجود منتج**
```dart
Consumer<CartProvider>(
  builder: (context, cartProvider, child) {
    final isInCart = cartProvider.isProductInCart(productId);
    final quantity = cartProvider.getProductQuantity(productId);
    
    return Text(isInCart ? 'في السلة ($quantity)' : 'غير مضاف');
  },
)
```

### 4. **زر إضافة متقدم**
```dart
AddToCartButton(
  product: product,
  quantity: 1,
  onAdded: () {
    // إجراء بعد الإضافة
  },
  customText: 'أضف الآن',
  backgroundColor: Colors.blue,
)
```

## 🎯 الدوال المهمة

### CartProvider:
```dart
// إضافة منتج
await cartProvider.addToCart(product, quantity: 2);

// إزالة منتج
await cartProvider.removeFromCart(productId);

// تحديث كمية
await cartProvider.updateQuantity(productId, newQuantity);

// زيادة كمية
await cartProvider.increaseQuantity(productId);

// تقليل كمية
await cartProvider.decreaseQuantity(productId);

// مسح السلة
await cartProvider.clearCart();

// التحقق من وجود منتج
bool isInCart = cartProvider.isProductInCart(productId);

// الحصول على كمية
int quantity = cartProvider.getProductQuantity(productId);

// الحصول على إجمالي السعر
double total = cartProvider.totalPrice;

// الحصول على عدد العناصر
int count = cartProvider.itemCount;

// الحصول على إجمالي الكمية
int totalQty = cartProvider.totalQuantity;
```

## 🔄 تدفق البيانات

### 1. **إضافة منتج**:
```
User clicks → CartProvider.addToCart() → CartService.addToCart() 
→ Save to SharedPreferences → notifyListeners() → UI updates
```

### 2. **عرض السلة**:
```
App starts → CartProvider.loadCart() → CartService.loadCart() 
→ Load from SharedPreferences → notifyListeners() → UI shows data
```

### 3. **تحديث الكمية**:
```
User changes quantity → CartProvider.updateQuantity() 
→ CartService.updateQuantity() → Save changes → notifyListeners() 
→ UI updates immediately
```

## 🎨 تخصيص الواجهة

### 1. **تخصيص أيقونة السلة**:
```dart
CartIcon(
  iconColor: Colors.blue,
  iconSize: 30,
  showBadge: true,
  onTap: () {
    // إجراء مخصص
  },
)
```

### 2. **تخصيص زر الإضافة**:
```dart
AddToCartButton(
  product: product,
  customText: 'اشتري الآن',
  customIcon: Icons.shopping_bag,
  backgroundColor: Colors.green,
  textColor: Colors.white,
  width: 200,
  height: 50,
)
```

### 3. **تخصيص صفحة السلة**:
```dart
// يمكن تعديل CartPage.dart مباشرة
// أو إنشاء صفحة مخصصة تستخدم CartProvider
```

## 🧪 الاختبار

### 1. **اختبار إضافة المنتجات**:
```dart
void testAddToCart() {
  final cartProvider = CartProvider();
  final product = Product(id: '1', name: 'Test', price: 10.0, ...);
  
  cartProvider.addToCart(product);
  
  assert(cartProvider.itemCount == 1);
  assert(cartProvider.totalPrice == 10.0);
  assert(cartProvider.isProductInCart('1') == true);
}
```

### 2. **اختبار الكميات**:
```dart
void testQuantities() {
  final cartProvider = CartProvider();
  final product = Product(id: '1', name: 'Test', price: 10.0, ...);
  
  cartProvider.addToCart(product, quantity: 3);
  assert(cartProvider.getProductQuantity('1') == 3);
  
  cartProvider.increaseQuantity('1');
  assert(cartProvider.getProductQuantity('1') == 4);
  
  cartProvider.decreaseQuantity('1');
  assert(cartProvider.getProductQuantity('1') == 3);
}
```

## 🐛 حل المشاكل الشائعة

### 1. **Provider not found**:
```dart
// تأكد من وجود MultiProvider في main.dart
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => CartProvider()),
  ],
  child: MyApp(),
)
```

### 2. **البيانات لا تحفظ**:
```dart
// تأكد من استدعاء loadCart() في initState
@override
void initState() {
  super.initState();
  WidgetsBinding.instance.addPostFrameCallback((_) {
    Provider.of<CartProvider>(context, listen: false).loadCart();
  });
}
```

### 3. **UI لا يتحدث**:
```dart
// استخدم Consumer بدلاً من Provider.of
Consumer<CartProvider>(
  builder: (context, cartProvider, child) {
    return Text('${cartProvider.itemCount}');
  },
)
```

## 📈 تحسينات مستقبلية

### 1. **إضافة المفضلة**:
```dart
// يمكن إضافة نظام مشابه للمفضلة
class FavoritesProvider extends ChangeNotifier {
  // نفس البنية مع تعديلات بسيطة
}
```

### 2. **كوبونات الخصم**:
```dart
// في CartService
double applyDiscount(String couponCode) {
  // منطق تطبيق الكوبون
}
```

### 3. **تتبع الطلبات**:
```dart
// ربط السلة بنظام الطلبات
Future<Order> createOrderFromCart() {
  // تحويل السلة إلى طلب
}
```

## 🎯 أفضل الممارسات

### 1. **إدارة الحالة**:
- ✅ استخدم Provider للحالة العامة
- ✅ استخدم setState للحالة المحلية
- ✅ تجنب الاستدعاءات المتكررة

### 2. **الأداء**:
- ✅ استخدم Consumer فقط عند الحاجة
- ✅ تجنب إعادة بناء الواجهة كاملة
- ✅ استخدم const widgets عند الإمكان

### 3. **تجربة المستخدم**:
- ✅ أضف مؤشرات تحميل
- ✅ اعرض رسائل تأكيد
- ✅ تعامل مع الأخطاء بلطف

## 🚀 النشر

### قبل النشر:
```bash
# اختبار شامل
flutter test

# بناء للإنتاج
flutter build apk --release
flutter build ios --release

# تحقق من الأداء
flutter analyze
```

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من الملفات المنشأة
2. راجع الأخطاء في console
3. تأكد من إضافة المكتبات
4. اختبر على أجهزة مختلفة

**نظام السلة جاهز للاستخدام!** 🛒✨
