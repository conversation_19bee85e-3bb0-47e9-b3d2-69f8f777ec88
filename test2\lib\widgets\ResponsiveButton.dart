import 'package:flutter/material.dart';
import 'package:test2/utils/ResponsiveHelper.dart';
import 'package:test2/widgets/ResponsiveText.dart';

class ResponsiveButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final double? mobileWidth;
  final double? tabletWidth;
  final double? desktopWidth;
  final double? mobileHeight;
  final double? tabletHeight;
  final double? desktopHeight;
  final double? mobileFontSize;
  final double? tabletFontSize;
  final double? desktopFontSize;
  final IconData? icon;
  final bool isLoading;
  final BorderRadius? borderRadius;
  final Border? border;
  final EdgeInsets? padding;

  const ResponsiveButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.mobileWidth,
    this.tabletWidth,
    this.desktopWidth,
    this.mobileHeight = 48,
    this.tabletHeight = 52,
    this.desktopHeight = 56,
    this.mobileFontSize = 16,
    this.tabletFontSize = 18,
    this.desktopFontSize = 20,
    this.icon,
    this.isLoading = false,
    this.borderRadius,
    this.border,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final width = mobileWidth != null
        ? ResponsiveHelper.responsiveSize(
            context,
            mobile: mobileWidth!,
            tablet: tabletWidth,
            desktop: desktopWidth,
          )
        : null;

    final height = ResponsiveHelper.responsiveSize(
      context,
      mobile: mobileHeight ?? 48,
      tablet: tabletHeight ?? 52,
      desktop: desktopHeight ?? 56,
    );

    final fontSize = ResponsiveHelper.responsiveFontSize(
      context,
      mobile: mobileFontSize ?? 16,
      tablet: tabletFontSize ?? 18,
      desktop: desktopFontSize ?? 20,
    );

    final buttonPadding = padding ??
        ResponsiveHelper.responsiveEdgeInsets(
          context,
          mobile: 16,
          tablet: 20,
          desktop: 24,
        );

    final radius = borderRadius ??
        BorderRadius.circular(
          ResponsiveHelper.responsiveBorderRadius(
            context,
            mobile: 8,
            tablet: 10,
            desktop: 12,
          ),
        );

    Widget buttonChild;
    if (isLoading) {
      buttonChild = SizedBox(
        width: ResponsiveHelper.responsiveSize(
          context,
          mobile: 20,
          tablet: 22,
          desktop: 24,
        ),
        height: ResponsiveHelper.responsiveSize(
          context,
          mobile: 20,
          tablet: 22,
          desktop: 24,
        ),
        child: CircularProgressIndicator(
          color: textColor ?? Colors.white,
          strokeWidth: ResponsiveHelper.responsiveSize(
            context,
            mobile: 2,
            tablet: 2.5,
            desktop: 3,
          ),
        ),
      );
    } else if (icon != null) {
      buttonChild = Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: textColor ?? Colors.white,
            size: ResponsiveHelper.responsiveIconSize(
              context,
              mobile: 20,
              tablet: 22,
              desktop: 24,
            ),
          ),
          SizedBox(width: ResponsiveHelper.responsiveSpacing(context)),
          ResponsiveText(
            text,
            mobileFontSize: mobileFontSize ?? 16,
            tabletFontSize: tabletFontSize ?? 18,
            desktopFontSize: desktopFontSize ?? 20,
            color: textColor ?? Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ],
      );
    } else {
      buttonChild = ResponsiveText(
        text,
        mobileFontSize: mobileFontSize ?? 16,
        tabletFontSize: tabletFontSize ?? 18,
        desktopFontSize: desktopFontSize ?? 20,
        color: textColor ?? Colors.white,
        fontWeight: FontWeight.bold,
        textAlign: TextAlign.center,
      );
    }

    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? Theme.of(context).primaryColor,
          padding: buttonPadding,
          shape: RoundedRectangleBorder(
            borderRadius: radius,
            side: border?.top ?? BorderSide.none,
          ),
          elevation: ResponsiveHelper.responsiveElevation(
            context,
            mobile: 2,
            tablet: 3,
            desktop: 4,
          ),
        ),
        child: buttonChild,
      ),
    );
  }
}

class ResponsiveOutlinedButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? borderColor;
  final Color? textColor;
  final double? mobileWidth;
  final double? tabletWidth;
  final double? desktopWidth;
  final double? mobileHeight;
  final double? tabletHeight;
  final double? desktopHeight;
  final double? mobileFontSize;
  final double? tabletFontSize;
  final double? desktopFontSize;
  final IconData? icon;
  final bool isLoading;

  const ResponsiveOutlinedButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.borderColor,
    this.textColor,
    this.mobileWidth,
    this.tabletWidth,
    this.desktopWidth,
    this.mobileHeight = 48,
    this.tabletHeight = 52,
    this.desktopHeight = 56,
    this.mobileFontSize = 16,
    this.tabletFontSize = 18,
    this.desktopFontSize = 20,
    this.icon,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final width = mobileWidth != null
        ? ResponsiveHelper.responsiveSize(
            context,
            mobile: mobileWidth!,
            tablet: tabletWidth,
            desktop: desktopWidth,
          )
        : null;

    final height = ResponsiveHelper.responsiveSize(
      context,
      mobile: mobileHeight ?? 48,
      tablet: tabletHeight ?? 52,
      desktop: desktopHeight ?? 56,
    );

    final buttonPadding = ResponsiveHelper.responsiveEdgeInsets(
      context,
      mobile: 16,
      tablet: 20,
      desktop: 24,
    );

    final radius = BorderRadius.circular(
      ResponsiveHelper.responsiveBorderRadius(
        context,
        mobile: 8,
        tablet: 10,
        desktop: 12,
      ),
    );

    Widget buttonChild;
    if (isLoading) {
      buttonChild = SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          color: textColor ?? Theme.of(context).primaryColor,
          strokeWidth: 2,
        ),
      );
    } else if (icon != null) {
      buttonChild = Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: textColor ?? Theme.of(context).primaryColor,
            size: ResponsiveHelper.responsiveIconSize(
              context,
              mobile: 20,
              tablet: 22,
              desktop: 24,
            ),
          ),
          SizedBox(width: ResponsiveHelper.responsiveSpacing(context)),
          ResponsiveText(
            text,
            mobileFontSize: mobileFontSize ?? 16,
            tabletFontSize: tabletFontSize ?? 18,
            desktopFontSize: desktopFontSize ?? 20,
            color: textColor ?? Theme.of(context).primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ],
      );
    } else {
      buttonChild = ResponsiveText(
        text,
        mobileFontSize: mobileFontSize ?? 16,
        tabletFontSize: tabletFontSize ?? 18,
        desktopFontSize: desktopFontSize ?? 20,
        color: textColor ?? Theme.of(context).primaryColor,
        fontWeight: FontWeight.bold,
        textAlign: TextAlign.center,
      );
    }

    return SizedBox(
      width: width,
      height: height,
      child: OutlinedButton(
        onPressed: isLoading ? null : onPressed,
        style: OutlinedButton.styleFrom(
          padding: buttonPadding,
          side: BorderSide(
            color: borderColor ?? Theme.of(context).primaryColor,
            width: ResponsiveHelper.responsiveBorderWidth(context),
          ),
          shape: RoundedRectangleBorder(borderRadius: radius),
        ),
        child: buttonChild,
      ),
    );
  }
}

class ResponsiveIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double? mobileSize;
  final double? tabletSize;
  final double? desktopSize;
  final double? mobileIconSize;
  final double? tabletIconSize;
  final double? desktopIconSize;
  final String? tooltip;

  const ResponsiveIconButton({
    Key? key,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.mobileSize = 48,
    this.tabletSize = 52,
    this.desktopSize = 56,
    this.mobileIconSize = 24,
    this.tabletIconSize = 26,
    this.desktopIconSize = 28,
    this.tooltip,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = ResponsiveHelper.responsiveSize(
      context,
      mobile: mobileSize ?? 48,
      tablet: tabletSize ?? 52,
      desktop: desktopSize ?? 56,
    );

    final iconSize = ResponsiveHelper.responsiveIconSize(
      context,
      mobile: mobileIconSize ?? 24,
      tablet: tabletIconSize ?? 26,
      desktop: desktopIconSize ?? 28,
    );

    Widget button = SizedBox(
      width: size,
      height: size,
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          size: iconSize,
          color: iconColor,
        ),
        style: IconButton.styleFrom(
          backgroundColor: backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              ResponsiveHelper.responsiveBorderRadius(context),
            ),
          ),
        ),
        tooltip: tooltip,
      ),
    );

    return button;
  }
}
