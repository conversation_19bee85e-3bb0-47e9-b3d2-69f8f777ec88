import 'package:flutter/material.dart';
import '../models/Notification.dart';
import '../utils/AppColors.dart';

/// بطاقة الإشعار
class NotificationCard extends StatelessWidget {
  final AppNotification notification;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const NotificationCard({
    Key? key,
    required this.notification,
    this.onTap,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: notification.isRead
            ? AppColors.notificationReadColor
            : AppColors.notificationUnreadColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: notification.isRead
              ? AppColors.borderColor
              : AppColors.primaryColor.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // أيقونة النوع
              _buildTypeIcon(),

              SizedBox(width: 12),

              // محتوى الإشعار
              Expanded(
                child: _buildContent(),
              ),

              SizedBox(width: 8),

              // الوقت والخيارات
              _buildActions(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeIcon() {
    final color = _getTypeColor();

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: Text(
          notification.typeIcon,
          style: TextStyle(fontSize: 20),
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // العنوان
        Row(
          children: [
            Expanded(
              child: Text(
                notification.title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight:
                      notification.isRead ? FontWeight.w600 : FontWeight.bold,
                  color: AppColors.textPrimaryColor,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // مؤشر عدم القراءة
            if (!notification.isRead)
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  shape: BoxShape.circle,
                ),
              ),
          ],
        ),

        SizedBox(height: 4),

        // الرسالة
        Text(
          notification.message,
          style: TextStyle(
            fontSize: 14,
            color: AppColors.textSecondaryColor,
            height: 1.4,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        SizedBox(height: 8),

        // معلومات إضافية
        _buildAdditionalInfo(),
      ],
    );
  }

  Widget _buildAdditionalInfo() {
    return Row(
      children: [
        // نوع الإشعار
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: _getTypeColor().withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            _getTypeLabel(),
            style: TextStyle(
              fontSize: 10,
              color: _getTypeColor(),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),

        SizedBox(width: 8),

        // الأولوية
        if (notification.priority == NotificationPriority.high ||
            notification.priority == NotificationPriority.urgent)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: notification.priority == NotificationPriority.urgent
                  ? AppColors.errorColor.withOpacity(0.1)
                  : AppColors.warningColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              notification.priority == NotificationPriority.urgent
                  ? 'عاجل'
                  : 'مهم',
              style: TextStyle(
                fontSize: 9,
                color: notification.priority == NotificationPriority.urgent
                    ? AppColors.errorColor
                    : AppColors.warningColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // الوقت
        Text(
          notification.formattedTime,
          style: TextStyle(
            fontSize: 11,
            color: AppColors.disabledColor,
          ),
        ),

        SizedBox(height: 8),

        // زر الخيارات
        PopupMenuButton<String>(
          icon: Icon(
            Icons.more_vert,
            color: AppColors.disabledColor,
            size: 18,
          ),
          onSelected: (value) {
            switch (value) {
              case 'delete':
                onDelete?.call();
                break;
              case 'share':
                _shareNotification(context);
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: AppColors.errorColor, size: 16),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(fontSize: 12)),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share,
                      color: AppColors.textSecondaryColor, size: 16),
                  SizedBox(width: 8),
                  Text('مشاركة', style: TextStyle(fontSize: 12)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Color _getTypeColor() {
    switch (notification.type) {
      case NotificationType.orderConfirmed:
        return Colors.green;
      case NotificationType.orderPreparing:
        return Colors.orange;
      case NotificationType.orderReady:
        return Colors.blue;
      case NotificationType.orderDelivered:
        return Colors.green;
      case NotificationType.offer:
        return Colors.red;
      case NotificationType.newProduct:
        return Colors.purple;
      case NotificationType.newStore:
        return Colors.indigo;
      case NotificationType.rating:
        return Colors.amber;
      case NotificationType.general:
        return Colors.grey;
      default:
        return Color(0xFF4C53A5);
    }
  }

  String _getTypeLabel() {
    switch (notification.type) {
      case NotificationType.orderConfirmed:
        return 'تأكيد طلب';
      case NotificationType.orderPreparing:
        return 'تحضير طلب';
      case NotificationType.orderReady:
        return 'طلب جاهز';
      case NotificationType.orderDelivered:
        return 'تم التوصيل';
      case NotificationType.offer:
        return 'عرض';
      case NotificationType.newProduct:
        return 'منتج جديد';
      case NotificationType.newStore:
        return 'متجر جديد';
      case NotificationType.rating:
        return 'تقييم';
      case NotificationType.general:
        return 'عام';
      default:
        return 'إشعار';
    }
  }

  void _shareNotification(BuildContext context) {
    // يمكن إضافة منطق المشاركة هنا
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم نسخ الإشعار'),
        backgroundColor: Color(0xFF4C53A5),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
