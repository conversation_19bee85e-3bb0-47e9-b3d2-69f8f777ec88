import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart'; // استيراد SharedPreferences للتحقق من الإعدادات

/// خدمة الإشعارات - مسؤولة عن إدارة وإرسال جميع أنواع الإشعارات في التطبيق
class NotificationService {
  // إنشاء مثيل من مكتبة الإشعارات المحلية
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  /// تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    // إعدادات التهيئة لنظام Android
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher'); // أيقونة التطبيق

    // إعدادات التهيئة لنظام iOS
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true, // طلب إذن عرض التنبيهات
      requestBadgePermission: true, // طلب إذن عرض الشارات
      requestSoundPermission: true, // طلب إذن تشغيل الأصوات
    );

    // دمج إعدادات التهيئة لجميع المنصات
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    // تهيئة المكتبة مع معالج النقر على الإشعارات
    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse:
          _onNotificationTapped, // دالة معالجة النقر
    );

    // طلب الأذونات المطلوبة
    await _requestPermissions();
  }

  /// طلب الأذونات المطلوبة للإشعارات
  Future<void> _requestPermissions() async {
    // طلب إذن الإشعارات
    await Permission.notification.request();
    print('تم طلب أذونات الإشعارات'); // طباعة رسالة تأكيد
  }

  /// معالج النقر على الإشعارات
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    final String? payload =
        notificationResponse.payload; // الحصول على البيانات المرفقة
    if (payload != null) {
      print('تم النقر على الإشعار: $payload'); // طباعة البيانات المرفقة
      // يمكن إضافة منطق التنقل هنا حسب نوع الإشعار
    }
  }

  /// التحقق من إعدادات الإشعارات قبل الإرسال
  Future<bool> _shouldSendNotification() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      bool notificationsEnabled =
          prefs.getBool('notifications_enabled') ?? true;

      if (!notificationsEnabled) {
        print('الإشعارات معطلة في الإعدادات - لن يتم إرسال الإشعار');
        return false;
      }
      return true;
    } catch (e) {
      print('خطأ في التحقق من إعدادات الإشعارات: $e');
      return true; // في حالة الخطأ، نسمح بالإشعارات
    }
  }

  /// إنشاء إعدادات الإشعار مع مراعاة إعدادات المستخدم
  Future<NotificationDetails> _createNotificationDetails(
      String channelId, String channelName, String channelDescription) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      bool playSound = prefs.getBool('sound_enabled') ?? true;
      bool enableVibration = prefs.getBool('vibration_enabled') ?? true;

      final AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        channelId, // معرف القناة
        channelName, // اسم القناة
        channelDescription: channelDescription, // وصف القناة
        importance: Importance.high, // أهمية عالية
        priority: Priority.high, // أولوية عالية
        icon: '@mipmap/ic_launcher', // أيقونة الإشعار
        color: Color(0xFF4C53A5), // لون الإشعار
        playSound: playSound, // تشغيل الصوت حسب الإعدادات
        enableVibration: enableVibration, // الاهتزاز حسب الإعدادات
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true, // عرض التنبيه
        presentBadge: true, // عرض الشارة
        presentSound: true, // تشغيل الصوت
      );

      return NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );
    } catch (e) {
      print('خطأ في إنشاء إعدادات الإشعار: $e');
      // إرجاع إعدادات افتراضية في حالة الخطأ
      return const NotificationDetails(
        android: AndroidNotificationDetails(
          'default_channel',
          'إشعارات عامة',
          channelDescription: 'إشعارات عامة للتطبيق',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
          color: Color(0xFF4C53A5),
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      );
    }
  }

  /// إرسال إشعار عرض جديد
  Future<void> showNewOfferNotification({
    required String offerTitle,
    required String storeName,
    required String discountPercentage,
  }) async {
    // التحقق من إعدادات الإشعارات قبل الإرسال
    if (!await _shouldSendNotification()) {
      return; // عدم إرسال الإشعار إذا كان معطلاً
    }

    // إنشاء إعدادات الإشعار مع مراعاة إعدادات المستخدم
    final NotificationDetails platformChannelSpecifics =
        await _createNotificationDetails(
      'new_offers_channel', // معرف قناة العروض الجديدة
      'عروض جديدة', // اسم القناة
      'إشعارات العروض الجديدة في التطبيق', // وصف القناة
    );

    // إرسال الإشعار
    await _flutterLocalNotificationsPlugin.show(
      DateTime.now()
          .millisecondsSinceEpoch
          .remainder(100000), // معرف فريد للإشعار
      '🔥 عرض جديد في زاد!', // عنوان الإشعار
      '$offerTitle من $storeName - خصم $discountPercentage%', // محتوى الإشعار
      platformChannelSpecifics, // إعدادات الإشعار
      payload: 'new_offer:$storeName', // بيانات إضافية للإشعار
    );

    print(
        'تم إرسال إشعار عرض جديد: $offerTitle من $storeName بخصم $discountPercentage%'); // طباعة رسالة تأكيد
  }

  /// إرسال إشعار متجر جديد
  Future<void> showNewStoreNotification({
    required String storeName,
    required String storeCategory,
  }) async {
    // التحقق من إعدادات الإشعارات قبل الإرسال
    if (!await _shouldSendNotification()) {
      return; // عدم إرسال الإشعار إذا كان معطلاً
    }

    // إنشاء إعدادات الإشعار مع مراعاة إعدادات المستخدم
    final NotificationDetails platformChannelSpecifics =
        await _createNotificationDetails(
      'new_stores_channel', // معرف قناة المتاجر الجديدة
      'متاجر جديدة', // اسم القناة
      'إشعارات المتاجر الجديدة في التطبيق', // وصف القناة
    );

    // إرسال الإشعار
    await _flutterLocalNotificationsPlugin.show(
      DateTime.now()
          .millisecondsSinceEpoch
          .remainder(100000), // معرف فريد للإشعار
      '🏪 متجر جديد في زاد!', // عنوان الإشعار
      'انضم إلينا $storeName في قسم $storeCategory', // محتوى الإشعار
      platformChannelSpecifics, // إعدادات الإشعار
      payload: 'new_store:$storeName', // بيانات إضافية للإشعار
    );

    print(
        'تم إرسال إشعار متجر جديد: $storeName في قسم $storeCategory'); // طباعة رسالة تأكيد
  }

  /// إرسال إشعار منتج جديد
  Future<void> showNewProductNotification({
    required String productName,
    required String storeName,
    required String price,
  }) async {
    // التحقق من إعدادات الإشعارات قبل الإرسال
    if (!await _shouldSendNotification()) {
      return; // عدم إرسال الإشعار إذا كان معطلاً
    }

    // إنشاء إعدادات الإشعار مع مراعاة إعدادات المستخدم
    final NotificationDetails platformChannelSpecifics =
        await _createNotificationDetails(
      'new_products_channel', // معرف قناة المنتجات الجديدة
      'منتجات جديدة', // اسم القناة
      'إشعارات المنتجات الجديدة في التطبيق', // وصف القناة
    );

    // إرسال الإشعار
    await _flutterLocalNotificationsPlugin.show(
      DateTime.now()
          .millisecondsSinceEpoch
          .remainder(100000), // معرف فريد للإشعار
      '🆕 منتج جديد في زاد!', // عنوان الإشعار
      '$productName من $storeName بسعر $price ريال', // محتوى الإشعار
      platformChannelSpecifics, // إعدادات الإشعار
      payload: 'new_product:$storeName', // بيانات إضافية للإشعار
    );

    print(
        'تم إرسال إشعار منتج جديد: $productName من $storeName بسعر $price ريال'); // طباعة رسالة تأكيد
  }

  /// إرسال إشعار ترحيبي
  Future<void> showWelcomeNotification() async {
    // التحقق من إعدادات الإشعارات قبل الإرسال
    if (!await _shouldSendNotification()) {
      return; // عدم إرسال الإشعار إذا كان معطلاً
    }

    // إنشاء إعدادات الإشعار مع مراعاة إعدادات المستخدم
    final NotificationDetails platformChannelSpecifics =
        await _createNotificationDetails(
      'welcome_channel', // معرف قناة الترحيب
      'ترحيب', // اسم القناة
      'إشعارات الترحيب بالمستخدمين الجدد', // وصف القناة
    );

    // إرسال الإشعار
    await _flutterLocalNotificationsPlugin.show(
      0, // معرف ثابت للإشعار الترحيبي
      '🎉 أهلاً بك في زاد!', // عنوان الإشعار
      'اكتشف أفضل المتاجر والعروض في مكان واحد', // محتوى الإشعار
      platformChannelSpecifics, // إعدادات الإشعار
      payload: 'welcome', // بيانات إضافية للإشعار
    );

    print('تم إرسال إشعار ترحيبي للمستخدم'); // طباعة رسالة تأكيد
  }

  /// جدولة إشعار يومي للعروض
  Future<void> scheduleDailyOffersNotification() async {
    // التحقق من إعدادات الإشعارات قبل الجدولة
    if (!await _shouldSendNotification()) {
      print(
          'الإشعارات معطلة - لن يتم جدولة الإشعارات اليومية'); // طباعة رسالة توضيحية
      return; // عدم جدولة الإشعار إذا كان معطلاً
    }

    // إنشاء إعدادات الإشعار مع مراعاة إعدادات المستخدم
    final NotificationDetails platformChannelSpecifics =
        await _createNotificationDetails(
      'daily_offers_channel', // معرف قناة العروض اليومية
      'العروض اليومية', // اسم القناة
      'إشعارات العروض اليومية المجدولة', // وصف القناة
    );

    // جدولة إشعار يومي في الساعة 10 صباحاً
    await _flutterLocalNotificationsPlugin.periodicallyShow(
      1, // معرف الإشعار المجدول
      '🔥 عروض اليوم في زاد', // عنوان الإشعار
      'لا تفوت العروض الحصرية اليوم!', // محتوى الإشعار
      RepeatInterval.daily, // تكرار يومي
      platformChannelSpecifics, // إعدادات الإشعار
      payload: 'daily_offers', // بيانات إضافية للإشعار
    );

    print('تم جدولة الإشعارات اليومية للعروض'); // طباعة رسالة تأكيد
  }

  /// إلغاء الإشعارات المجدولة عند تعطيل الإشعارات
  Future<void> cancelScheduledNotifications() async {
    await _flutterLocalNotificationsPlugin.cancel(1); // إلغاء الإشعار اليومي
    print('تم إلغاء جميع الإشعارات المجدولة'); // طباعة رسالة تأكيد
  }

  /// إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    await _flutterLocalNotificationsPlugin.cancelAll(); // إلغاء جميع الإشعارات
    print('تم إلغاء جميع الإشعارات'); // طباعة رسالة تأكيد
  }
}
