import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/Notification.dart';

/// خدمة الإشعارات - مسؤولة عن إدارة وإرسال جميع أنواع الإشعارات في التطبيق
class NotificationService {
  // إنشاء مثيل من مكتبة الإشعارات المحلية
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // قوائم الإشعارات والإعدادات
  List<AppNotification> _notifications = [];
  NotificationSettings _settings = NotificationSettings();

  // Getters للوصول للبيانات
  List<AppNotification> get notifications => _notifications;
  NotificationSettings get settings => _settings;
  int get badgeCount => _notifications.where((n) => !n.isRead).length;

  /// تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    // إعدادات التهيئة لنظام Android
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher'); // أيقونة التطبيق

    // إعدادات التهيئة لنظام iOS
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true, // طلب إذن عرض التنبيهات
      requestBadgePermission: true, // طلب إذن عرض الشارات
      requestSoundPermission: true, // طلب إذن تشغيل الأصوات
    );

    // دمج إعدادات التهيئة لجميع المنصات
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    // تهيئة المكتبة مع معالج النقر على الإشعارات
    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse:
          _onNotificationTapped, // دالة معالجة النقر
    );

    // طلب الأذونات المطلوبة
    await _requestPermissions();
  }

  /// طلب الأذونات المطلوبة للإشعارات
  Future<void> _requestPermissions() async {
    // طلب إذن الإشعارات
    await Permission.notification.request();
    print('تم طلب أذونات الإشعارات'); // طباعة رسالة تأكيد
  }

  /// معالج النقر على الإشعارات
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    final String? payload =
        notificationResponse.payload; // الحصول على البيانات المرفقة
    if (payload != null) {
      print('تم النقر على الإشعار: $payload'); // طباعة البيانات المرفقة
      // يمكن إضافة منطق التنقل هنا حسب نوع الإشعار
    }
  }

  /// التحقق من إعدادات الإشعارات قبل الإرسال
  Future<bool> _shouldSendNotification() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      bool notificationsEnabled =
          prefs.getBool('notifications_enabled') ?? true;

      if (!notificationsEnabled) {
        print('الإشعارات معطلة في الإعدادات - لن يتم إرسال الإشعار');
        return false;
      }
      return true;
    } catch (e) {
      print('خطأ في التحقق من إعدادات الإشعارات: $e');
      return true; // في حالة الخطأ، نسمح بالإشعارات
    }
  }

  /// إنشاء إعدادات الإشعار مع مراعاة إعدادات المستخدم
  Future<NotificationDetails> _createNotificationDetails(
      String channelId, String channelName, String channelDescription) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      bool playSound = prefs.getBool('sound_enabled') ?? true;
      bool enableVibration = prefs.getBool('vibration_enabled') ?? true;

      final AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        channelId, // معرف القناة
        channelName, // اسم القناة
        channelDescription: channelDescription, // وصف القناة
        importance: Importance.high, // أهمية عالية
        priority: Priority.high, // أولوية عالية
        icon: '@mipmap/ic_launcher', // أيقونة الإشعار
        color: Color(0xFF4C53A5), // لون الإشعار
        playSound: playSound, // تشغيل الصوت حسب الإعدادات
        enableVibration: enableVibration, // الاهتزاز حسب الإعدادات
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true, // عرض التنبيه
        presentBadge: true, // عرض الشارة
        presentSound: true, // تشغيل الصوت
      );

      return NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );
    } catch (e) {
      print('خطأ في إنشاء إعدادات الإشعار: $e');
      // إرجاع إعدادات افتراضية في حالة الخطأ
      return const NotificationDetails(
        android: AndroidNotificationDetails(
          'default_channel',
          'إشعارات عامة',
          channelDescription: 'إشعارات عامة للتطبيق',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
          color: Color(0xFF4C53A5),
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      );
    }
  }

  /// إرسال إشعار عرض جديد
  Future<void> showNewOfferNotification({
    required String offerTitle,
    required String storeName,
    required String discountPercentage,
  }) async {
    // التحقق من إعدادات الإشعارات قبل الإرسال
    if (!await _shouldSendNotification()) {
      return; // عدم إرسال الإشعار إذا كان معطلاً
    }

    // إنشاء إعدادات الإشعار مع مراعاة إعدادات المستخدم
    final NotificationDetails platformChannelSpecifics =
        await _createNotificationDetails(
      'new_offers_channel', // معرف قناة العروض الجديدة
      'عروض جديدة', // اسم القناة
      'إشعارات العروض الجديدة في التطبيق', // وصف القناة
    );

    // إرسال الإشعار
    await _flutterLocalNotificationsPlugin.show(
      DateTime.now()
          .millisecondsSinceEpoch
          .remainder(100000), // معرف فريد للإشعار
      '🔥 عرض جديد في زاد!', // عنوان الإشعار
      '$offerTitle من $storeName - خصم $discountPercentage%', // محتوى الإشعار
      platformChannelSpecifics, // إعدادات الإشعار
      payload: 'new_offer:$storeName', // بيانات إضافية للإشعار
    );

    print(
        'تم إرسال إشعار عرض جديد: $offerTitle من $storeName بخصم $discountPercentage%'); // طباعة رسالة تأكيد
  }

  /// إرسال إشعار متجر جديد
  Future<void> showNewStoreNotification({
    required String storeName,
    required String storeCategory,
  }) async {
    // التحقق من إعدادات الإشعارات قبل الإرسال
    if (!await _shouldSendNotification()) {
      return; // عدم إرسال الإشعار إذا كان معطلاً
    }

    // إنشاء إعدادات الإشعار مع مراعاة إعدادات المستخدم
    final NotificationDetails platformChannelSpecifics =
        await _createNotificationDetails(
      'new_stores_channel', // معرف قناة المتاجر الجديدة
      'متاجر جديدة', // اسم القناة
      'إشعارات المتاجر الجديدة في التطبيق', // وصف القناة
    );

    // إرسال الإشعار
    await _flutterLocalNotificationsPlugin.show(
      DateTime.now()
          .millisecondsSinceEpoch
          .remainder(100000), // معرف فريد للإشعار
      '🏪 متجر جديد في زاد!', // عنوان الإشعار
      'انضم إلينا $storeName في قسم $storeCategory', // محتوى الإشعار
      platformChannelSpecifics, // إعدادات الإشعار
      payload: 'new_store:$storeName', // بيانات إضافية للإشعار
    );

    print(
        'تم إرسال إشعار متجر جديد: $storeName في قسم $storeCategory'); // طباعة رسالة تأكيد
  }

  /// إرسال إشعار منتج جديد
  Future<void> showNewProductNotification({
    required String productName,
    required String storeName,
    required String price,
  }) async {
    // التحقق من إعدادات الإشعارات قبل الإرسال
    if (!await _shouldSendNotification()) {
      return; // عدم إرسال الإشعار إذا كان معطلاً
    }

    // إنشاء إعدادات الإشعار مع مراعاة إعدادات المستخدم
    final NotificationDetails platformChannelSpecifics =
        await _createNotificationDetails(
      'new_products_channel', // معرف قناة المنتجات الجديدة
      'منتجات جديدة', // اسم القناة
      'إشعارات المنتجات الجديدة في التطبيق', // وصف القناة
    );

    // إرسال الإشعار
    await _flutterLocalNotificationsPlugin.show(
      DateTime.now()
          .millisecondsSinceEpoch
          .remainder(100000), // معرف فريد للإشعار
      '🆕 منتج جديد في زاد!', // عنوان الإشعار
      '$productName من $storeName بسعر $price ريال', // محتوى الإشعار
      platformChannelSpecifics, // إعدادات الإشعار
      payload: 'new_product:$storeName', // بيانات إضافية للإشعار
    );

    print(
        'تم إرسال إشعار منتج جديد: $productName من $storeName بسعر $price ريال'); // طباعة رسالة تأكيد
  }

  /// إرسال إشعار ترحيبي
  Future<void> showWelcomeNotification() async {
    // التحقق من إعدادات الإشعارات قبل الإرسال
    if (!await _shouldSendNotification()) {
      return; // عدم إرسال الإشعار إذا كان معطلاً
    }

    // إنشاء إعدادات الإشعار مع مراعاة إعدادات المستخدم
    final NotificationDetails platformChannelSpecifics =
        await _createNotificationDetails(
      'welcome_channel', // معرف قناة الترحيب
      'ترحيب', // اسم القناة
      'إشعارات الترحيب بالمستخدمين الجدد', // وصف القناة
    );

    // إرسال الإشعار
    await _flutterLocalNotificationsPlugin.show(
      0, // معرف ثابت للإشعار الترحيبي
      '🎉 أهلاً بك في زاد!', // عنوان الإشعار
      'اكتشف أفضل المتاجر والعروض في مكان واحد', // محتوى الإشعار
      platformChannelSpecifics, // إعدادات الإشعار
      payload: 'welcome', // بيانات إضافية للإشعار
    );

    print('تم إرسال إشعار ترحيبي للمستخدم'); // طباعة رسالة تأكيد
  }

  /// جدولة إشعار يومي للعروض
  Future<void> scheduleDailyOffersNotification() async {
    // التحقق من إعدادات الإشعارات قبل الجدولة
    if (!await _shouldSendNotification()) {
      print(
          'الإشعارات معطلة - لن يتم جدولة الإشعارات اليومية'); // طباعة رسالة توضيحية
      return; // عدم جدولة الإشعار إذا كان معطلاً
    }

    // إنشاء إعدادات الإشعار مع مراعاة إعدادات المستخدم
    final NotificationDetails platformChannelSpecifics =
        await _createNotificationDetails(
      'daily_offers_channel', // معرف قناة العروض اليومية
      'العروض اليومية', // اسم القناة
      'إشعارات العروض اليومية المجدولة', // وصف القناة
    );

    // جدولة إشعار يومي في الساعة 10 صباحاً
    await _flutterLocalNotificationsPlugin.periodicallyShow(
      1, // معرف الإشعار المجدول
      '🔥 عروض اليوم في زاد', // عنوان الإشعار
      'لا تفوت العروض الحصرية اليوم!', // محتوى الإشعار
      RepeatInterval.daily, // تكرار يومي
      platformChannelSpecifics, // إعدادات الإشعار
      payload: 'daily_offers', // بيانات إضافية للإشعار
    );

    print('تم جدولة الإشعارات اليومية للعروض'); // طباعة رسالة تأكيد
  }

  /// إلغاء الإشعارات المجدولة عند تعطيل الإشعارات
  Future<void> cancelScheduledNotifications() async {
    await _flutterLocalNotificationsPlugin.cancel(1); // إلغاء الإشعار اليومي
    print('تم إلغاء جميع الإشعارات المجدولة'); // طباعة رسالة تأكيد
  }

  /// إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    await _flutterLocalNotificationsPlugin.cancelAll(); // إلغاء جميع الإشعارات
    print('تم إلغاء جميع الإشعارات'); // طباعة رسالة تأكيد
  }

  // ===== الدوال الجديدة المطلوبة للنظام المتقدم =====

  /// تحميل الإشعارات من التخزين المحلي
  Future<void> loadNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getStringList('notifications') ?? [];

      _notifications = notificationsJson
          .map((json) => AppNotification.fromJson(jsonDecode(json)))
          .toList();

      // ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
      _notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    } catch (e) {
      print('خطأ في تحميل الإشعارات: $e');
      _notifications = [];
    }
  }

  /// حفظ الإشعارات في التخزين المحلي
  Future<void> _saveNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = _notifications
          .map((notification) => jsonEncode(notification.toJson()))
          .toList();

      await prefs.setStringList('notifications', notificationsJson);
    } catch (e) {
      print('خطأ في حفظ الإشعارات: $e');
    }
  }

  /// تحميل الإعدادات من التخزين المحلي
  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString('notification_settings');

      if (settingsJson != null) {
        _settings = NotificationSettings.fromJson(jsonDecode(settingsJson));
      }
    } catch (e) {
      print('خطأ في تحميل إعدادات الإشعارات: $e');
      _settings = NotificationSettings();
    }
  }

  /// حفظ الإعدادات في التخزين المحلي
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          'notification_settings', jsonEncode(_settings.toJson()));
    } catch (e) {
      print('خطأ في حفظ إعدادات الإشعارات: $e');
    }
  }

  /// إضافة إشعار جديد
  Future<void> addNotification({
    required String title,
    required String message,
    required NotificationType type,
    Map<String, dynamic>? data,
  }) async {
    // التحقق من إعدادات الإشعارات
    if (!_settings.isEnabled || !_shouldShowNotificationType(type)) {
      return;
    }

    // التحقق من ساعات الهدوء
    if (_settings.isQuietTime) {
      return;
    }

    final notification = AppNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      message: message,
      type: type,
      timestamp: DateTime.now(),
      data: data,
    );

    _notifications.insert(0, notification);
    await _saveNotifications();

    // إرسال الإشعار المحلي
    await _showLocalNotification(notification);
  }

  /// التحقق من إمكانية عرض نوع الإشعار
  bool _shouldShowNotificationType(NotificationType type) {
    switch (type) {
      case NotificationType.orderConfirmed:
      case NotificationType.orderPreparing:
      case NotificationType.orderReady:
      case NotificationType.orderDelivered:
        return _settings.orderUpdates;
      case NotificationType.offer:
        return _settings.offers;
      case NotificationType.newProduct:
        return _settings.newProducts;
      case NotificationType.newStore:
        return _settings.newStores;
      case NotificationType.general:
        return _settings.general;
      default:
        return true;
    }
  }

  /// عرض الإشعار المحلي
  Future<void> _showLocalNotification(AppNotification notification) async {
    if (!_settings.isEnabled) return;

    final details = NotificationDetails(
      android: AndroidNotificationDetails(
        'app_notifications',
        'إشعارات التطبيق',
        channelDescription: 'إشعارات عامة للتطبيق',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
        color: const Color(0xFF4C53A5),
        playSound: _settings.sound,
        enableVibration: _settings.vibration,
      ),
      iOS: const DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      ),
    );

    await _flutterLocalNotificationsPlugin.show(
      notification.id.hashCode,
      notification.title,
      notification.message,
      details,
      payload: notification.id,
    );
  }

  /// تحديد إشعار كمقروء
  Future<void> markAsRead(String notificationId) async {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      await _saveNotifications();
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<void> markAllAsRead() async {
    for (int i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
    await _saveNotifications();
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    _notifications.removeWhere((n) => n.id == notificationId);
    await _saveNotifications();
  }

  /// مسح جميع الإشعارات
  Future<void> clearAllNotifications() async {
    _notifications.clear();
    await _saveNotifications();
  }

  /// تحديث الإعدادات
  Future<void> updateSettings(NotificationSettings newSettings) async {
    _settings = newSettings;
    await _saveSettings();
  }

  /// إضافة إشعار طلب
  Future<void> addOrderNotification({
    required String orderId,
    required String status,
    String? message,
  }) async {
    // تحديد نوع الإشعار حسب الحالة
    NotificationType type;
    switch (status.toLowerCase()) {
      case 'confirmed':
      case 'تم التأكيد':
        type = NotificationType.orderConfirmed;
        break;
      case 'preparing':
      case 'قيد التحضير':
        type = NotificationType.orderPreparing;
        break;
      case 'ready':
      case 'جاهز':
        type = NotificationType.orderReady;
        break;
      case 'delivered':
      case 'تم التوصيل':
        type = NotificationType.orderDelivered;
        break;
      default:
        type = NotificationType.general;
    }

    await addNotification(
      title: 'تحديث الطلب #$orderId',
      message: message ?? 'تم تحديث حالة طلبك إلى: $status',
      type: type,
      data: {'orderId': orderId, 'status': status},
    );
  }

  /// إضافة إشعار عرض
  Future<void> addOfferNotification({
    required String title,
    required String storeName,
    String? discountPercentage,
  }) async {
    await addNotification(
      title: title,
      message:
          'عرض جديد من $storeName${discountPercentage != null ? ' - خصم $discountPercentage%' : ''}',
      type: NotificationType.offer,
      data: {'storeName': storeName, 'discount': discountPercentage},
    );
  }
}
