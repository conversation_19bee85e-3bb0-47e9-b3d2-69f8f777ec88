import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/UserProfile.dart';
import '../models/Address.dart';

/// خدمة إدارة الملف الشخصي للمستخدم
class UserProfileService {
  static const String _userProfileKey = 'user_profile';
  static const String _addressesKey = 'user_addresses';

  /// الحصول على الملف الشخصي للمستخدم
  static Future<UserProfile?> getUserProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = prefs.getString(_userProfileKey);
      
      if (profileJson != null) {
        final profileMap = jsonDecode(profileJson) as Map<String, dynamic>;
        return UserProfile.fromMap(profileMap);
      }
      
      // إذا لم يوجد ملف شخصي، إرجاع بيانات تجريبية
      return MockUserData.getSampleUser();
    } catch (e) {
      print('خطأ في تحميل الملف الشخصي: $e');
      return MockUserData.getSampleUser();
    }
  }

  /// حفظ الملف الشخصي
  static Future<bool> saveUserProfile(UserProfile profile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = jsonEncode(profile.toMap());
      await prefs.setString(_userProfileKey, profileJson);
      return true;
    } catch (e) {
      print('خطأ في حفظ الملف الشخصي: $e');
      return false;
    }
  }

  /// تحديث المعلومات الأساسية
  static Future<bool> updateBasicInfo({
    required String firstName,
    required String lastName,
    required String email,
    required String phone,
  }) async {
    try {
      final profile = await getUserProfile();
      if (profile != null) {
        final updatedProfile = profile.copyWith(
          firstName: firstName,
          lastName: lastName,
          email: email,
          phone: phone,
          updatedAt: DateTime.now(),
        );
        return await saveUserProfile(updatedProfile);
      }
      return false;
    } catch (e) {
      print('خطأ في تحديث المعلومات الأساسية: $e');
      return false;
    }
  }

  /// إضافة عنوان جديد
  static Future<bool> addAddress(Address address) async {
    try {
      final profile = await getUserProfile();
      if (profile != null) {
        final updatedProfile = profile.addAddress(address);
        return await saveUserProfile(updatedProfile);
      }
      return false;
    } catch (e) {
      print('خطأ في إضافة العنوان: $e');
      return false;
    }
  }

  /// تحديث عنوان
  static Future<bool> updateAddress(Address address) async {
    try {
      final profile = await getUserProfile();
      if (profile != null) {
        final updatedProfile = profile.updateAddress(address);
        return await saveUserProfile(updatedProfile);
      }
      return false;
    } catch (e) {
      print('خطأ في تحديث العنوان: $e');
      return false;
    }
  }

  /// حذف عنوان
  static Future<bool> removeAddress(String addressId) async {
    try {
      final profile = await getUserProfile();
      if (profile != null) {
        final updatedProfile = profile.removeAddress(addressId);
        return await saveUserProfile(updatedProfile);
      }
      return false;
    } catch (e) {
      print('خطأ في حذف العنوان: $e');
      return false;
    }
  }

  /// تعيين عنوان كافتراضي
  static Future<bool> setDefaultAddress(String addressId) async {
    try {
      final profile = await getUserProfile();
      if (profile != null) {
        final updatedProfile = profile.setDefaultAddress(addressId);
        return await saveUserProfile(updatedProfile);
      }
      return false;
    } catch (e) {
      print('خطأ في تعيين العنوان الافتراضي: $e');
      return false;
    }
  }

  /// الحصول على جميع العناوين
  static Future<List<Address>> getUserAddresses() async {
    try {
      final profile = await getUserProfile();
      return profile?.addresses ?? [];
    } catch (e) {
      print('خطأ في تحميل العناوين: $e');
      return [];
    }
  }

  /// الحصول على العنوان الافتراضي
  static Future<Address?> getDefaultAddress() async {
    try {
      final profile = await getUserProfile();
      return profile?.defaultAddress;
    } catch (e) {
      print('خطأ في تحميل العنوان الافتراضي: $e');
      return null;
    }
  }

  /// التحقق من اكتمال الملف الشخصي
  static Future<bool> isProfileComplete() async {
    try {
      final profile = await getUserProfile();
      return profile?.isProfileComplete ?? false;
    } catch (e) {
      print('خطأ في التحقق من اكتمال الملف الشخصي: $e');
      return false;
    }
  }

  /// مسح جميع بيانات المستخدم
  static Future<bool> clearUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userProfileKey);
      await prefs.remove(_addressesKey);
      return true;
    } catch (e) {
      print('خطأ في مسح بيانات المستخدم: $e');
      return false;
    }
  }

  /// إنشاء عنوان جديد بمعرف فريد
  static Address createNewAddress({
    required String title,
    required String fullAddress,
    required String city,
    required String district,
    required String street,
    required String buildingNumber,
    String apartmentNumber = '',
    String? landmark,
    String? additionalInfo,
    double? latitude,
    double? longitude,
    bool isDefault = false,
  }) {
    final id = 'addr_${DateTime.now().millisecondsSinceEpoch}';
    
    return Address(
      id: id,
      title: title,
      fullAddress: fullAddress,
      city: city,
      district: district,
      street: street,
      buildingNumber: buildingNumber,
      apartmentNumber: apartmentNumber,
      landmark: landmark,
      additionalInfo: additionalInfo,
      latitude: latitude,
      longitude: longitude,
      isDefault: isDefault,
    );
  }

  /// التحقق من صحة رقم الهاتف السعودي
  static bool isValidSaudiPhone(String phone) {
    // إزالة المسافات والرموز
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    // التحقق من الأنماط المختلفة للأرقام السعودية
    final patterns = [
      RegExp(r'^(\+966|966|0)?5[0-9]{8}$'), // أرقام الجوال
      RegExp(r'^(\+966|966|0)?1[1-9][0-9]{7}$'), // أرقام الرياض
      RegExp(r'^(\+966|966|0)?2[0-9]{8}$'), // أرقام جدة ومكة
    ];
    
    return patterns.any((pattern) => pattern.hasMatch(cleanPhone));
  }

  /// تنسيق رقم الهاتف السعودي
  static String formatSaudiPhone(String phone) {
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    if (cleanPhone.startsWith('+966')) {
      return cleanPhone;
    } else if (cleanPhone.startsWith('966')) {
      return '+$cleanPhone';
    } else if (cleanPhone.startsWith('0')) {
      return '+966${cleanPhone.substring(1)}';
    } else if (cleanPhone.length == 9) {
      return '+966$cleanPhone';
    }
    
    return phone; // إرجاع الرقم كما هو إذا لم يتطابق مع أي نمط
  }

  /// التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }
}
