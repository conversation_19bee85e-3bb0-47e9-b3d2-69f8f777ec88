import 'package:flutter/material.dart';
import 'package:test2/models/PaymentMethod.dart';
import 'package:test2/services/PaymentService.dart';

class PaymentMethodsPage extends StatefulWidget {
  @override
  _PaymentMethodsPageState createState() => _PaymentMethodsPageState();
}

class _PaymentMethodsPageState extends State<PaymentMethodsPage> {
  final PaymentService _paymentService = PaymentService();
  List<PaymentMethod> _paymentMethods = [];

  @override
  void initState() {
    super.initState();
    _loadPaymentMethods();
  }

  void _loadPaymentMethods() {
    setState(() {
      _paymentMethods = _paymentService.getAllPaymentMethods();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: AppBar(
        title: Text(
          'طرق الدفع',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Color(0xFF4C53A5),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: ListView(
        padding: EdgeInsets.all(16),
        children: [
          // مقدمة
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue),
                    SizedBox(width: 8),
                    Text(
                      'طرق الدفع المتاحة',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade800,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                Text(
                  'يمكنك الدفع بإحدى الطرق التالية عند إتمام طلبك:',
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 16),

          // قائمة طرق الدفع
          ...(_paymentMethods.map((method) => _buildPaymentMethodCard(method)).toList()),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodCard(PaymentMethod method) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس البطاقة
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: method.getColor().withOpacity(0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: method.getColor().withOpacity(0.2),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Icon(
                    method.getIcon(),
                    color: method.getColor(),
                    size: 28,
                  ),
                ),
                
                SizedBox(width: 16),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        method.nameAr,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: method.getColor(),
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        method.descriptionAr,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // مؤشر الحالة
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: method.isEnabled ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    method.isEnabled ? 'متاح' : 'غير متاح',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // تفاصيل البطاقة
          Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات الرسوم
                if (method.fees != null && method.fees! > 0) ...[
                  _buildInfoRow(
                    'الرسوم:',
                    '${method.fees!.toStringAsFixed(2)} ريال',
                    Icons.monetization_on,
                    Colors.orange,
                  ),
                  SizedBox(height: 8),
                ],

                // معلومات الحساب
                if (method.accountNumber != null) ...[
                  _buildInfoRow(
                    'رقم الحساب/المحفظة:',
                    method.accountNumber!,
                    Icons.account_balance_wallet,
                    Colors.blue,
                  ),
                  SizedBox(height: 8),
                ],

                if (method.accountName != null) ...[
                  _buildInfoRow(
                    'اسم الحساب:',
                    method.accountName!,
                    Icons.person,
                    Colors.green,
                  ),
                  SizedBox(height: 8),
                ],

                // تعليمات خاصة
                _buildInstructionsSection(method),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: color, size: 16),
        SizedBox(width: 8),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: TextStyle(fontSize: 14, color: Colors.black87),
              children: [
                TextSpan(
                  text: label,
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                TextSpan(text: ' '),
                TextSpan(
                  text: value,
                  style: TextStyle(color: color, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInstructionsSection(PaymentMethod method) {
    String instructions = '';
    
    switch (method.type) {
      case PaymentType.cash:
        instructions = 'سيتم تحصيل المبلغ عند تسليم الطلب. تأكد من توفر المبلغ المطلوب نقداً.';
        break;
      case PaymentType.bankTransfer:
        instructions = 'قم بتحويل المبلغ إلى الحساب المذكور أعلاه وأرسل صورة الإيصال عبر الواتساب.';
        break;
      case PaymentType.jeebWallet:
        instructions = 'قم بتحويل المبلغ إلى محفظة جيب المذكورة أعلاه وأرسل رقم العملية.';
        break;
      case PaymentType.omFlous:
        instructions = 'قم بتحويل المبلغ إلى محفظة أم فلوس المذكورة أعلاه وأرسل رقم العملية.';
        break;
      case PaymentType.cashMobile:
        instructions = 'قم بتحويل المبلغ عبر كاش موبايل إلى الرقم المذكور أعلاه وأرسل رقم العملية.';
        break;
      case PaymentType.otherWallet:
        instructions = 'قم بتحويل المبلغ إلى المحفظة المذكورة أعلاه وأرسل تأكيد العملية.';
        break;
    }

    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: method.getColor().withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: method.getColor().withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: method.getColor(),
                size: 16,
              ),
              SizedBox(width: 6),
              Text(
                'تعليمات الدفع:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: method.getColor(),
                  fontSize: 14,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            instructions,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey.shade700,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}
