import 'package:shared_preferences/shared_preferences.dart';

/// خدمة إدارة الإعدادات - تحفظ وتسترجع جميع إعدادات التطبيق
class SettingsService {
  // إنشاء مثيل واحد فقط من الخدمة (Singleton Pattern)
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  // مفاتيح حفظ الإعدادات في التخزين المحلي
  static const String _notificationsKey = 'notifications_enabled';
  static const String _locationKey = 'location_enabled';
  static const String _darkModeKey = 'dark_mode_enabled';
  static const String _languageKey = 'selected_language';
  static const String _soundKey = 'sound_enabled';
  static const String _vibrationKey = 'vibration_enabled';
  static const String _autoUpdateKey = 'auto_update_enabled';
  static const String _dataUsageKey = 'data_usage_enabled';
  static const String _cacheKey = 'cache_enabled';
  static const String _analyticsKey = 'analytics_enabled';

  // متغيرات الإعدادات الحالية في الذاكرة
  bool _notificationsEnabled = true; // حالة الإشعارات
  bool _locationEnabled = true; // حالة تحديد الموقع
  bool _darkModeEnabled = false; // حالة الوضع الليلي
  String _selectedLanguage = 'العربية'; // اللغة المختارة
  bool _soundEnabled = true; // حالة الأصوات
  bool _vibrationEnabled = true; // حالة الاهتزاز
  bool _autoUpdateEnabled = true; // حالة التحديث التلقائي
  bool _dataUsageEnabled = true; // حالة استخدام البيانات
  bool _cacheEnabled = true; // حالة التخزين المؤقت
  bool _analyticsEnabled = true; // حالة التحليلات

  // Getters للحصول على قيم الإعدادات
  bool get notificationsEnabled =>
      _notificationsEnabled; // إرجاع حالة الإشعارات
  bool get locationEnabled => _locationEnabled; // إرجاع حالة الموقع
  bool get darkModeEnabled => _darkModeEnabled; // إرجاع حالة الوضع الليلي
  String get selectedLanguage => _selectedLanguage; // إرجاع اللغة المختارة
  bool get soundEnabled => _soundEnabled; // إرجاع حالة الأصوات
  bool get vibrationEnabled => _vibrationEnabled; // إرجاع حالة الاهتزاز
  bool get autoUpdateEnabled =>
      _autoUpdateEnabled; // إرجاع حالة التحديث التلقائي
  bool get dataUsageEnabled => _dataUsageEnabled; // إرجاع حالة استخدام البيانات
  bool get cacheEnabled => _cacheEnabled; // إرجاع حالة التخزين المؤقت
  bool get analyticsEnabled => _analyticsEnabled; // إرجاع حالة التحليلات

  /// تحميل جميع الإعدادات من التخزين المحلي
  Future<void> loadSettings() async {
    try {
      // الحصول على مثيل SharedPreferences للوصول للتخزين المحلي
      final prefs = await SharedPreferences.getInstance();

      // تحميل كل إعداد مع القيمة الافتراضية إذا لم يكن موجوداً
      _notificationsEnabled =
          prefs.getBool(_notificationsKey) ?? true; // تحميل حالة الإشعارات
      _locationEnabled =
          prefs.getBool(_locationKey) ?? true; // تحميل حالة الموقع
      _darkModeEnabled =
          prefs.getBool(_darkModeKey) ?? false; // تحميل حالة الوضع الليلي
      _selectedLanguage =
          prefs.getString(_languageKey) ?? 'العربية'; // تحميل اللغة المختارة
      _soundEnabled = prefs.getBool(_soundKey) ?? true; // تحميل حالة الأصوات
      _vibrationEnabled =
          prefs.getBool(_vibrationKey) ?? true; // تحميل حالة الاهتزاز
      _autoUpdateEnabled =
          prefs.getBool(_autoUpdateKey) ?? true; // تحميل حالة التحديث التلقائي
      _dataUsageEnabled =
          prefs.getBool(_dataUsageKey) ?? true; // تحميل حالة استخدام البيانات
      _cacheEnabled =
          prefs.getBool(_cacheKey) ?? true; // تحميل حالة التخزين المؤقت
      _analyticsEnabled =
          prefs.getBool(_analyticsKey) ?? true; // تحميل حالة التحليلات

      print('تم تحميل الإعدادات بنجاح'); // طباعة رسالة نجاح التحميل
    } catch (e) {
      print('خطأ في تحميل الإعدادات: $e'); // طباعة رسالة الخطأ
    }
  }

  /// تفعيل أو إلغاء تفعيل الإشعارات
  Future<bool> setNotificationsEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences
          .getInstance(); // الحصول على مثيل التخزين المحلي
      await prefs.setBool(_notificationsKey, enabled); // حفظ القيمة الجديدة
      _notificationsEnabled = enabled; // تحديث القيمة في الذاكرة

      // ملاحظة: سيتم تطبيق الإعداد على خدمة الإشعارات من خلال التحقق من الإعدادات في الخدمات الأخرى

      print(
          'تم ${enabled ? 'تفعيل' : 'إلغاء تفعيل'} الإشعارات'); // طباعة رسالة التأكيد
      return true; // إرجاع نجاح العملية
    } catch (e) {
      print('خطأ في حفظ إعدادات الإشعارات: $e'); // طباعة رسالة الخطأ
      return false; // إرجاع فشل العملية
    }
  }

  /// تفعيل أو إلغاء تفعيل تحديد الموقع
  Future<bool> setLocationEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences
          .getInstance(); // الحصول على مثيل التخزين المحلي
      await prefs.setBool(_locationKey, enabled); // حفظ القيمة الجديدة
      _locationEnabled = enabled; // تحديث القيمة في الذاكرة
      print(
          'تم ${enabled ? 'تفعيل' : 'إلغاء تفعيل'} تحديد الموقع'); // طباعة رسالة التأكيد
      return true; // إرجاع نجاح العملية
    } catch (e) {
      print('خطأ في حفظ إعدادات الموقع: $e'); // طباعة رسالة الخطأ
      return false; // إرجاع فشل العملية
    }
  }

  /// تفعيل أو إلغاء تفعيل الوضع الليلي
  Future<bool> setDarkModeEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences
          .getInstance(); // الحصول على مثيل التخزين المحلي
      await prefs.setBool(_darkModeKey, enabled); // حفظ القيمة الجديدة
      _darkModeEnabled = enabled; // تحديث القيمة في الذاكرة
      print(
          'تم ${enabled ? 'تفعيل' : 'إلغاء تفعيل'} الوضع الليلي'); // طباعة رسالة التأكيد
      return true; // إرجاع نجاح العملية
    } catch (e) {
      print('خطأ في حفظ إعدادات الوضع الليلي: $e'); // طباعة رسالة الخطأ
      return false; // إرجاع فشل العملية
    }
  }

  /// تغيير لغة التطبيق
  Future<bool> setLanguage(String language) async {
    try {
      final prefs = await SharedPreferences
          .getInstance(); // الحصول على مثيل التخزين المحلي
      await prefs.setString(_languageKey, language); // حفظ اللغة الجديدة
      _selectedLanguage = language; // تحديث اللغة في الذاكرة
      print('تم تغيير اللغة إلى: $language'); // طباعة رسالة التأكيد
      return true; // إرجاع نجاح العملية
    } catch (e) {
      print('خطأ في حفظ إعدادات اللغة: $e'); // طباعة رسالة الخطأ
      return false; // إرجاع فشل العملية
    }
  }

  /// تفعيل أو إلغاء تفعيل الأصوات
  Future<bool> setSoundEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences
          .getInstance(); // الحصول على مثيل التخزين المحلي
      await prefs.setBool(_soundKey, enabled); // حفظ القيمة الجديدة
      _soundEnabled = enabled; // تحديث القيمة في الذاكرة
      print(
          'تم ${enabled ? 'تفعيل' : 'إلغاء تفعيل'} الأصوات'); // طباعة رسالة التأكيد
      return true; // إرجاع نجاح العملية
    } catch (e) {
      print('خطأ في حفظ إعدادات الأصوات: $e'); // طباعة رسالة الخطأ
      return false; // إرجاع فشل العملية
    }
  }

  /// تفعيل أو إلغاء تفعيل الاهتزاز
  Future<bool> setVibrationEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences
          .getInstance(); // الحصول على مثيل التخزين المحلي
      await prefs.setBool(_vibrationKey, enabled); // حفظ القيمة الجديدة
      _vibrationEnabled = enabled; // تحديث القيمة في الذاكرة
      print(
          'تم ${enabled ? 'تفعيل' : 'إلغاء تفعيل'} الاهتزاز'); // طباعة رسالة التأكيد
      return true; // إرجاع نجاح العملية
    } catch (e) {
      print('خطأ في حفظ إعدادات الاهتزاز: $e'); // طباعة رسالة الخطأ
      return false; // إرجاع فشل العملية
    }
  }

  /// تفعيل أو إلغاء تفعيل التحديث التلقائي
  Future<bool> setAutoUpdateEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences
          .getInstance(); // الحصول على مثيل التخزين المحلي
      await prefs.setBool(_autoUpdateKey, enabled); // حفظ القيمة الجديدة
      _autoUpdateEnabled = enabled; // تحديث القيمة في الذاكرة
      print(
          'تم ${enabled ? 'تفعيل' : 'إلغاء تفعيل'} التحديث التلقائي'); // طباعة رسالة التأكيد
      return true; // إرجاع نجاح العملية
    } catch (e) {
      print('خطأ في حفظ إعدادات التحديث التلقائي: $e'); // طباعة رسالة الخطأ
      return false; // إرجاع فشل العملية
    }
  }

  /// تفعيل أو إلغاء تفعيل استخدام البيانات
  Future<bool> setDataUsageEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences
          .getInstance(); // الحصول على مثيل التخزين المحلي
      await prefs.setBool(_dataUsageKey, enabled); // حفظ القيمة الجديدة
      _dataUsageEnabled = enabled; // تحديث القيمة في الذاكرة
      print(
          'تم ${enabled ? 'تفعيل' : 'إلغاء تفعيل'} استخدام البيانات'); // طباعة رسالة التأكيد
      return true; // إرجاع نجاح العملية
    } catch (e) {
      print('خطأ في حفظ إعدادات استخدام البيانات: $e'); // طباعة رسالة الخطأ
      return false; // إرجاع فشل العملية
    }
  }

  /// تفعيل أو إلغاء تفعيل التخزين المؤقت
  Future<bool> setCacheEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences
          .getInstance(); // الحصول على مثيل التخزين المحلي
      await prefs.setBool(_cacheKey, enabled); // حفظ القيمة الجديدة
      _cacheEnabled = enabled; // تحديث القيمة في الذاكرة
      print(
          'تم ${enabled ? 'تفعيل' : 'إلغاء تفعيل'} التخزين المؤقت'); // طباعة رسالة التأكيد
      return true; // إرجاع نجاح العملية
    } catch (e) {
      print('خطأ في حفظ إعدادات التخزين المؤقت: $e'); // طباعة رسالة الخطأ
      return false; // إرجاع فشل العملية
    }
  }

  /// تفعيل أو إلغاء تفعيل التحليلات
  Future<bool> setAnalyticsEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences
          .getInstance(); // الحصول على مثيل التخزين المحلي
      await prefs.setBool(_analyticsKey, enabled); // حفظ القيمة الجديدة
      _analyticsEnabled = enabled; // تحديث القيمة في الذاكرة
      print(
          'تم ${enabled ? 'تفعيل' : 'إلغاء تفعيل'} التحليلات'); // طباعة رسالة التأكيد
      return true; // إرجاع نجاح العملية
    } catch (e) {
      print('خطأ في حفظ إعدادات التحليلات: $e'); // طباعة رسالة الخطأ
      return false; // إرجاع فشل العملية
    }
  }

  /// إعادة تعيين جميع الإعدادات للقيم الافتراضية
  Future<bool> resetAllSettings() async {
    try {
      final prefs = await SharedPreferences
          .getInstance(); // الحصول على مثيل التخزين المحلي

      // حذف جميع مفاتيح الإعدادات من التخزين المحلي
      await prefs.remove(_notificationsKey); // حذف إعدادات الإشعارات
      await prefs.remove(_locationKey); // حذف إعدادات الموقع
      await prefs.remove(_darkModeKey); // حذف إعدادات الوضع الليلي
      await prefs.remove(_languageKey); // حذف إعدادات اللغة
      await prefs.remove(_soundKey); // حذف إعدادات الأصوات
      await prefs.remove(_vibrationKey); // حذف إعدادات الاهتزاز
      await prefs.remove(_autoUpdateKey); // حذف إعدادات التحديث التلقائي
      await prefs.remove(_dataUsageKey); // حذف إعدادات استخدام البيانات
      await prefs.remove(_cacheKey); // حذف إعدادات التخزين المؤقت
      await prefs.remove(_analyticsKey); // حذف إعدادات التحليلات

      // إعادة تعيين القيم في الذاكرة للقيم الافتراضية
      _notificationsEnabled = true; // إعادة تعيين الإشعارات للقيمة الافتراضية
      _locationEnabled = true; // إعادة تعيين الموقع للقيمة الافتراضية
      _darkModeEnabled = false; // إعادة تعيين الوضع الليلي للقيمة الافتراضية
      _selectedLanguage = 'العربية'; // إعادة تعيين اللغة للقيمة الافتراضية
      _soundEnabled = true; // إعادة تعيين الأصوات للقيمة الافتراضية
      _vibrationEnabled = true; // إعادة تعيين الاهتزاز للقيمة الافتراضية
      _autoUpdateEnabled =
          true; // إعادة تعيين التحديث التلقائي للقيمة الافتراضية
      _dataUsageEnabled =
          true; // إعادة تعيين استخدام البيانات للقيمة الافتراضية
      _cacheEnabled = true; // إعادة تعيين التخزين المؤقت للقيمة الافتراضية
      _analyticsEnabled = true; // إعادة تعيين التحليلات للقيمة الافتراضية

      print(
          'تم إعادة تعيين جميع الإعدادات للقيم الافتراضية'); // طباعة رسالة التأكيد
      return true; // إرجاع نجاح العملية
    } catch (e) {
      print('خطأ في إعادة تعيين الإعدادات: $e'); // طباعة رسالة الخطأ
      return false; // إرجاع فشل العملية
    }
  }

  /// الحصول على جميع الإعدادات كخريطة
  Map<String, dynamic> getAllSettings() {
    return {
      'notifications': _notificationsEnabled, // إرجاع حالة الإشعارات
      'location': _locationEnabled, // إرجاع حالة الموقع
      'darkMode': _darkModeEnabled, // إرجاع حالة الوضع الليلي
      'language': _selectedLanguage, // إرجاع اللغة المختارة
      'sound': _soundEnabled, // إرجاع حالة الأصوات
      'vibration': _vibrationEnabled, // إرجاع حالة الاهتزاز
      'autoUpdate': _autoUpdateEnabled, // إرجاع حالة التحديث التلقائي
      'dataUsage': _dataUsageEnabled, // إرجاع حالة استخدام البيانات
      'cache': _cacheEnabled, // إرجاع حالة التخزين المؤقت
      'analytics': _analyticsEnabled, // إرجاع حالة التحليلات
    };
  }

  /// طباعة جميع الإعدادات الحالية للتشخيص
  void printCurrentSettings() {
    print('=== الإعدادات الحالية ==='); // طباعة عنوان
    print(
        'الإشعارات: ${_notificationsEnabled ? 'مفعلة' : 'معطلة'}'); // طباعة حالة الإشعارات
    print('الموقع: ${_locationEnabled ? 'مفعل' : 'معطل'}'); // طباعة حالة الموقع
    print(
        'الوضع الليلي: ${_darkModeEnabled ? 'مفعل' : 'معطل'}'); // طباعة حالة الوضع الليلي
    print('اللغة: $_selectedLanguage'); // طباعة اللغة المختارة
    print(
        'الأصوات: ${_soundEnabled ? 'مفعلة' : 'معطلة'}'); // طباعة حالة الأصوات
    print(
        'الاهتزاز: ${_vibrationEnabled ? 'مفعل' : 'معطل'}'); // طباعة حالة الاهتزاز
    print(
        'التحديث التلقائي: ${_autoUpdateEnabled ? 'مفعل' : 'معطل'}'); // طباعة حالة التحديث التلقائي
    print(
        'استخدام البيانات: ${_dataUsageEnabled ? 'مفعل' : 'معطل'}'); // طباعة حالة استخدام البيانات
    print(
        'التخزين المؤقت: ${_cacheEnabled ? 'مفعل' : 'معطل'}'); // طباعة حالة التخزين المؤقت
    print(
        'التحليلات: ${_analyticsEnabled ? 'مفعلة' : 'معطلة'}'); // طباعة حالة التحليلات
    print('========================'); // طباعة خط الفصل
  }
}
