# ميزة التمرير التفاعلي المتقدم مع العناوين الديناميكية
## Advanced Interactive Scroll with Dynamic Titles

## 🎯 نظرة عامة
تم تطوير ميزة التمرير التفاعلي لتشمل إخفاء عنوان "العروض والتخفيضات" تدريجياً وإظهار "المطاعم الأكثر مبيعاً" في نفس المكان، مع جعل المحتوى ثابت حتى يصل لمكان العروض.

## ✨ **الميزات الجديدة المتقدمة**

### **1. العناوين الديناميكية:**
- 📝 **عنوان العروض:** يختفي تدريجياً مع اختفاء العروض
- 📝 **عنوان المطاعم:** يظهر تدريجياً في نفس مكان عنوان العروض
- 🔄 **انتقال سلس:** بين العناوين بدون تقطع
- 📍 **موضع ثابت:** عنوان المطاعم يثبت في الأعلى

### **2. سلوك التمرير المتقدم:**
- 🔒 **المحتوى ثابت:** حتى يصل لمكان العروض
- 📜 **تمرير تدريجي:** للمحتوى بعد وصول العنوان للأعلى
- 🎯 **نقطة التحول:** عند 50% من ارتفاع العروض
- ⚡ **استجابة فورية:** لحركة التمرير

### **3. إدارة الحالات:**
- 🔍 **وضع البحث:** إخفاء العروض وإظهار عنوان المطاعم
- 🏷️ **تغيير التصنيف:** إعادة تعيين جميع القيم
- 🔄 **إلغاء البحث:** استعادة الوضع الطبيعي
- 📱 **تجربة متسقة:** في جميع الحالات

## 🔧 **التطبيق التقني المتقدم**

### **1. متغيرات التحكم الجديدة:**
```dart
class _HomePageState extends State<HomePage> {
  // متحكم التمرير
  late ScrollController _scrollController;
  double _offersHeight = 220.0; // الارتفاع الأولي للعروض
  final double _maxOffersHeight = 220.0; // الارتفاع الأقصى
  final double _minOffersHeight = 0.0; // الارتفاع الأدنى
  
  // متغيرات العناوين الديناميكية
  double _offersTitleOpacity = 1.0; // شفافية عنوان العروض
  double _restaurantsTitleOpacity = 0.0; // شفافية عنوان المطاعم
  bool _showRestaurantsTitle = false; // إظهار عنوان المطاعم في المكان الثابت
}
```

### **2. دالة التمرير المتقدمة:**
```dart
void _onScroll() {
  if (!isSearching) {
    double scrollOffset = _scrollController.offset;
    double changeRate = 1.2;
    double newHeight = _maxOffersHeight - (scrollOffset * changeRate);
    
    // تحديد الحد الأدنى والأقصى
    newHeight = newHeight.clamp(_minOffersHeight, _maxOffersHeight);
    
    // حساب شفافية عنوان العروض (يختفي مع اختفاء العروض)
    double newOffersTitleOpacity = (newHeight / _maxOffersHeight).clamp(0.0, 1.0);
    
    // حساب شفافية عنوان المطاعم (يظهر عندما تختفي العروض)
    double newRestaurantsTitleOpacity = (1.0 - (newHeight / _maxOffersHeight)).clamp(0.0, 1.0);
    
    // تحديد متى نظهر عنوان المطاعم في المكان الثابت
    bool newShowRestaurantsTitle = newHeight < (_maxOffersHeight * 0.5);
    
    // تحديث القيم مع التحقق من التغيير الملحوظ
    if ((_offersHeight - newHeight).abs() > 2 || 
        (_offersTitleOpacity - newOffersTitleOpacity).abs() > 0.05 ||
        _showRestaurantsTitle != newShowRestaurantsTitle) {
      setState(() {
        _offersHeight = newHeight;
        _offersTitleOpacity = newOffersTitleOpacity;
        _restaurantsTitleOpacity = newRestaurantsTitleOpacity;
        _showRestaurantsTitle = newShowRestaurantsTitle;
      });
    }
  }
}
```

### **3. العناوين المتداخلة:**
```dart
// Stack للعناوين المتداخلة
Stack(
  children: [
    // عنوان العروض مع شفافية متدرجة
    AnimatedOpacity(
      duration: Duration(milliseconds: 150),
      opacity: _offersTitleOpacity,
      child: _buildSectionTitle("العروض والتخفيضات"),
    ),

    // عنوان المطاعم في نفس المكان (يظهر عندما تختفي العروض)
    AnimatedOpacity(
      duration: Duration(milliseconds: 150),
      opacity: _restaurantsTitleOpacity,
      child: Container(
        color: Color(0xFFEDECF2), // نفس لون الخلفية
        child: _buildSectionTitle("المطاعم الأكثر مبيعاً"),
      ),
    ),
  ],
),
```

### **4. عنوان المطاعم في الأسفل:**
```dart
// عنوان المطاعم (يختفي عندما يظهر في الأعلى)
if (!isSearching)
  AnimatedOpacity(
    duration: Duration(milliseconds: 150),
    opacity: _showRestaurantsTitle ? 0.0 : 1.0,
    child: _buildSectionTitle("المطاعم الأكثر مبيعاً"),
  ),
```

## 🎮 **سلوك التفاعل المتقدم**

### **📱 المرحلة الأولى - بداية التمرير:**
1. **العروض:** ارتفاع كامل (220px) + شفافية 100%
2. **عنوان العروض:** شفافية 100%
3. **عنوان المطاعم (أعلى):** شفافية 0% (مخفي)
4. **عنوان المطاعم (أسفل):** شفافية 100%

### **📱 المرحلة الثانية - التمرير المتوسط:**
1. **العروض:** ارتفاع متناقص + شفافية متناقصة
2. **عنوان العروض:** شفافية متناقصة
3. **عنوان المطاعم (أعلى):** شفافية متزايدة
4. **عنوان المطاعم (أسفل):** شفافية متناقصة

### **📱 المرحلة الثالثة - نقطة التحول (50%):**
1. **العروض:** ارتفاع 110px + شفافية 50%
2. **عنوان العروض:** شفافية 50%
3. **عنوان المطاعم (أعلى):** شفافية 50% + يصبح ثابت
4. **عنوان المطاعم (أسفل):** شفافية 50%

### **📱 المرحلة الرابعة - اكتمال التحول:**
1. **العروض:** ارتفاع 0px (مخفي)
2. **عنوان العروض:** شفافية 0% (مخفي)
3. **عنوان المطاعم (أعلى):** شفافية 100% + ثابت
4. **عنوان المطاعم (أسفل):** شفافية 0% (مخفي)

## 📊 **معادلات التحكم**

### **ارتفاع العروض:**
```dart
newHeight = _maxOffersHeight - (scrollOffset * 1.2)
newHeight = newHeight.clamp(0.0, 220.0)
```

### **شفافية عنوان العروض:**
```dart
_offersTitleOpacity = (newHeight / _maxOffersHeight).clamp(0.0, 1.0)
// عندما العروض 220px → شفافية 100%
// عندما العروض 0px → شفافية 0%
```

### **شفافية عنوان المطاعم:**
```dart
_restaurantsTitleOpacity = (1.0 - (newHeight / _maxOffersHeight)).clamp(0.0, 1.0)
// عندما العروض 220px → شفافية 0%
// عندما العروض 0px → شفافية 100%
```

### **نقطة التحول:**
```dart
_showRestaurantsTitle = newHeight < (_maxOffersHeight * 0.5)
// عندما العروض أقل من 110px → إظهار عنوان المطاعم في الأعلى
```

## 🔄 **إدارة الحالات المتقدمة**

### **🔍 عند البحث:**
```dart
if (isSearching) {
  _offersHeight = 0.0;
  _offersTitleOpacity = 0.0;
  _restaurantsTitleOpacity = 1.0;
  _showRestaurantsTitle = true;
} else {
  _offersHeight = _maxOffersHeight;
  _offersTitleOpacity = 1.0;
  _restaurantsTitleOpacity = 0.0;
  _showRestaurantsTitle = false;
  // إعادة تعيين موضع التمرير
  _scrollController.animateTo(0, ...);
}
```

### **🏷️ عند تغيير التصنيف:**
```dart
selectedCategory = category;
_offersHeight = _maxOffersHeight;
_offersTitleOpacity = 1.0;
_restaurantsTitleOpacity = 0.0;
_showRestaurantsTitle = false;
// إعادة تعيين موضع التمرير
_scrollController.animateTo(0, ...);
```

## 🎨 **التصميم البصري المتقدم**

### **الانتقالات:**
- **المدة:** 150ms (سريع وسلس)
- **المنحنى:** `Curves.easeInOut` (طبيعي)
- **التزامن:** جميع العناصر تتحرك معاً

### **الشفافية:**
- **تدرج سلس:** من 0% إلى 100%
- **عدم وميض:** بفضل التحديث الشرطي
- **انتقال طبيعي:** بين العناوين

### **الموضع:**
- **Stack:** لوضع العناوين فوق بعض
- **Container:** بنفس لون الخلفية لإخفاء المحتوى
- **AnimatedOpacity:** للانتقالات السلسة

## 🚀 **تحسينات الأداء**

### **التحديث الشرطي:**
```dart
// تحديث فقط عند التغيير الملحوظ
if ((_offersHeight - newHeight).abs() > 2 || 
    (_offersTitleOpacity - newOffersTitleOpacity).abs() > 0.05 ||
    _showRestaurantsTitle != newShowRestaurantsTitle) {
  setState(() { ... });
}
```

### **تحسين الذاكرة:**
- **ClipRect:** لقطع المحتوى الزائد
- **AnimatedContainer:** بدلاً من إعادة البناء الكامل
- **تنظيف الموارد:** في dispose()

## 🎯 **النتائج المحققة**

### **قبل التحديث:**
- ❌ **عناوين ثابتة:** لا تتفاعل مع التمرير
- ❌ **استغلال مساحة:** عنوان المطاعم يأخذ مساحة دائماً
- ❌ **تجربة عادية:** بدون تفاعل متقدم

### **بعد التحديث:**
- ✅ **عناوين ديناميكية:** تتفاعل مع التمرير
- ✅ **استغلال أمثل للمساحة:** عنوان واحد في كل وقت
- ✅ **تجربة متقدمة:** تفاعل سلس ومتطور
- ✅ **انتقالات احترافية:** بين العناوين والمحتوى

## 🎮 **كيفية الاستخدام**

### **للمستخدم:**
1. **ابدأ من الأعلى:** شاهد العروض وعنوانها
2. **ابدأ التمرير:** شاهد عنوان العروض يختفي تدريجياً
3. **استمر في التمرير:** شاهد عنوان المطاعم يظهر في نفس المكان
4. **وصول نقطة التحول:** عنوان المطاعم يثبت في الأعلى
5. **التمرير للمحتوى:** تصفح المطاعم والمنتجات
6. **العودة للأعلى:** شاهد العملية بالعكس

### **للمطور:**
```bash
cd test2
flutter run
```

## 🎉 **الخلاصة**

تطبيق "زاد اليمن" أصبح الآن يوفر:
- ✅ **تمرير تفاعلي متقدم** مع عناوين ديناميكية
- ✅ **انتقالات سلسة** بين العناوين والمحتوى
- ✅ **استغلال أمثل للمساحة** مع عناوين متداخلة
- ✅ **تجربة مستخدم احترافية** مع تفاعل متطور
- ✅ **أداء محسن** مع تحديث شرطي وإدارة ذاكرة فعالة
- ✅ **سلوك متسق** في جميع الحالات والأوضاع

🎯 **الميزة جاهزة وتعمل بكفاءة عالية!**

الآن يمكن للمستخدمين الاستمتاع بتجربة تمرير تفاعلية متقدمة مع عناوين ديناميكية تتفاعل بسلاسة مع حركة التمرير، مما يوفر استغلالاً أمثل لمساحة الشاشة وتجربة مستخدم احترافية.
