import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:test2/utils/AppColors.dart';
import 'package:test2/services/LocationService.dart';
import 'package:test2/services/SettingsService.dart';
import 'package:geolocator/geolocator.dart';

/// صفحة اختبار شاملة لنظام GPS
class GPSTestPage extends StatefulWidget {
  @override
  _GPSTestPageState createState() => _GPSTestPageState();
}

class _GPSTestPageState extends State<GPSTestPage> {
  final LocationService _locationService = LocationService();
  final SettingsService _settingsService = SettingsService();
  
  Position? _currentPosition;
  Map<String, bool>? _locationStatus;
  bool _isLoading = false;
  String _statusMessage = 'اضغط على أحد الأزرار لبدء الاختبار';

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    await _settingsService.loadSettings();
    await _checkLocationStatus();
  }

  Future<void> _checkLocationStatus() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري فحص حالة GPS...';
    });

    try {
      _locationStatus = await _locationService.checkLocationStatus();
      setState(() {
        _statusMessage = 'تم فحص الحالة بنجاح';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في فحص الحالة: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري الحصول على الموقع...';
    });

    try {
      Position? position = await _locationService.getCurrentLocation();
      setState(() {
        _currentPosition = position;
        if (position != null) {
          _statusMessage = 'تم الحصول على الموقع بنجاح';
        } else {
          _statusMessage = 'لم يتم العثور على الموقع';
        }
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في الحصول على الموقع: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleLocationSetting() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري تغيير إعدادات الموقع...';
    });

    try {
      bool currentSetting = _settingsService.locationEnabled;
      bool newSetting = !currentSetting;
      
      bool success = await _settingsService.setLocationEnabled(newSetting);
      if (success) {
        await _locationService.updateLocationSettings(newSetting);
        setState(() {
          _statusMessage = 'تم ${newSetting ? 'تفعيل' : 'إلغاء تفعيل'} إعدادات الموقع';
        });
        await _checkLocationStatus();
      } else {
        setState(() {
          _statusMessage = 'فشل في تغيير الإعدادات';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في تغيير الإعدادات: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
        centerTitle: true,
        title: Text(
          "اختبار نظام GPS",
          style: AppTextStyles.appBarTitle,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.whiteColor),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        padding: EdgeInsets.only(top: AppDimensions.paddingMedium),
        decoration: BoxDecoration(
          color: AppColors.backgroundColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppDimensions.borderRadiusXLarge),
            topRight: Radius.circular(AppDimensions.borderRadiusXLarge),
          ),
        ),
        child: ListView(
          padding: EdgeInsets.all(AppDimensions.paddingLarge),
          children: [
            // حالة النظام
            _buildStatusCard(),
            SizedBox(height: 20.h),
            
            // معلومات الموقع الحالي
            _buildLocationCard(),
            SizedBox(height: 20.h),
            
            // أزرار الاختبار
            _buildTestButtons(),
            SizedBox(height: 20.h),
            
            // تفاصيل الحالة
            if (_locationStatus != null) _buildDetailedStatus(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 10,
            spreadRadius: 2,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                _isLoading ? Icons.refresh : Icons.info_outline,
                color: AppColors.primaryColor,
                size: 24.sp,
              ),
              SizedBox(width: 12.w),
              Text(
                'حالة النظام',
                style: AppTextStyles.titleMedium,
              ),
            ],
          ),
          SizedBox(height: 12.h),
          if (_isLoading)
            CircularProgressIndicator(color: AppColors.primaryColor)
          else
            Text(
              _statusMessage,
              style: AppTextStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );
  }

  Widget _buildLocationCard() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 10,
            spreadRadius: 2,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: AppColors.primaryColor,
                size: 24.sp,
              ),
              SizedBox(width: 12.w),
              Text(
                'الموقع الحالي',
                style: AppTextStyles.titleMedium,
              ),
            ],
          ),
          SizedBox(height: 12.h),
          if (_currentPosition != null) ...[
            Text('خط العرض: ${_currentPosition!.latitude.toStringAsFixed(6)}'),
            SizedBox(height: 4.h),
            Text('خط الطول: ${_currentPosition!.longitude.toStringAsFixed(6)}'),
            SizedBox(height: 4.h),
            Text('الدقة: ${_currentPosition!.accuracy.toStringAsFixed(2)} متر'),
            SizedBox(height: 4.h),
            Text('الوقت: ${DateTime.fromMillisecondsSinceEpoch(_currentPosition!.timestamp.millisecondsSinceEpoch)}'),
          ] else
            Text(
              'لا يوجد موقع محفوظ',
              style: AppTextStyles.bodyMedium.copyWith(color: Colors.grey),
            ),
        ],
      ),
    );
  }

  Widget _buildTestButtons() {
    return Column(
      children: [
        _buildTestButton(
          icon: Icons.gps_fixed,
          title: 'اختبار الحصول على الموقع',
          onTap: _getCurrentLocation,
        ),
        SizedBox(height: 12.h),
        _buildTestButton(
          icon: Icons.settings_applications,
          title: 'تبديل إعدادات الموقع',
          subtitle: 'الحالة الحالية: ${_settingsService.locationEnabled ? 'مفعل' : 'معطل'}',
          onTap: _toggleLocationSetting,
        ),
        SizedBox(height: 12.h),
        _buildTestButton(
          icon: Icons.refresh,
          title: 'فحص حالة النظام',
          onTap: _checkLocationStatus,
        ),
      ],
    );
  }

  Widget _buildTestButton({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: _isLoading ? null : onTap,
      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingMedium),
        decoration: BoxDecoration(
          color: AppColors.whiteColor,
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowColor,
              blurRadius: 5,
              spreadRadius: 1,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 50.w,
              height: 50.h,
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.borderRadiusSmall),
              ),
              child: Icon(icon, color: AppColors.primaryColor),
            ),
            SizedBox(width: 15.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: AppTextStyles.bodyLarge),
                  if (subtitle != null) ...[
                    SizedBox(height: 4.h),
                    Text(subtitle, style: AppTextStyles.bodySmall),
                  ],
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 16.sp, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedStatus() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 10,
            spreadRadius: 2,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('تفاصيل الحالة', style: AppTextStyles.titleMedium),
          SizedBox(height: 12.h),
          _buildStatusRow('خدمة الموقع', _locationStatus!['serviceEnabled'] ?? false),
          _buildStatusRow('أذونات التطبيق', _locationStatus!['hasPermission'] ?? false),
          _buildStatusRow('إعدادات التطبيق', _locationStatus!['settingsEnabled'] ?? false),
          _buildStatusRow('الحالة الإجمالية', _locationStatus!['fullyEnabled'] ?? false),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String title, bool isEnabled) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        children: [
          Icon(
            isEnabled ? Icons.check_circle : Icons.cancel,
            color: isEnabled ? Colors.green : Colors.red,
            size: 20.sp,
          ),
          SizedBox(width: 8.w),
          Expanded(child: Text(title)),
          Text(
            isEnabled ? 'مفعل' : 'معطل',
            style: TextStyle(
              color: isEnabled ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
