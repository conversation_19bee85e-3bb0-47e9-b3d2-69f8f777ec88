/// نموذج العنوان
class Address {
  final String id;
  final String title;
  final String fullAddress;
  final String city;
  final String district;
  final String street;
  final String buildingNumber;
  final String apartmentNumber;
  final String? landmark;
  final String? additionalInfo;
  final double? latitude;
  final double? longitude;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;

  Address({
    required this.id,
    required this.title,
    required this.fullAddress,
    required this.city,
    required this.district,
    required this.street,
    required this.buildingNumber,
    this.apartmentNumber = '',
    this.landmark,
    this.additionalInfo,
    this.latitude,
    this.longitude,
    this.isDefault = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// إنشاء من Map
  factory Address.fromMap(Map<String, dynamic> map) {
    return Address(
      id: map['id']?.toString() ?? '',
      title: map['title']?.toString() ?? '',
      fullAddress: map['fullAddress']?.toString() ?? '',
      city: map['city']?.toString() ?? '',
      district: map['district']?.toString() ?? '',
      street: map['street']?.toString() ?? '',
      buildingNumber: map['buildingNumber']?.toString() ?? '',
      apartmentNumber: map['apartmentNumber']?.toString() ?? '',
      landmark: map['landmark']?.toString(),
      additionalInfo: map['additionalInfo']?.toString(),
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
      isDefault: map['isDefault'] ?? false,
      createdAt: map['createdAt'] != null 
          ? DateTime.tryParse(map['createdAt'].toString()) ?? DateTime.now()
          : DateTime.now(),
      updatedAt: map['updatedAt'] != null 
          ? DateTime.tryParse(map['updatedAt'].toString()) ?? DateTime.now()
          : DateTime.now(),
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'fullAddress': fullAddress,
      'city': city,
      'district': district,
      'street': street,
      'buildingNumber': buildingNumber,
      'apartmentNumber': apartmentNumber,
      'landmark': landmark,
      'additionalInfo': additionalInfo,
      'latitude': latitude,
      'longitude': longitude,
      'isDefault': isDefault,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// نسخ مع تعديل
  Address copyWith({
    String? id,
    String? title,
    String? fullAddress,
    String? city,
    String? district,
    String? street,
    String? buildingNumber,
    String? apartmentNumber,
    String? landmark,
    String? additionalInfo,
    double? latitude,
    double? longitude,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Address(
      id: id ?? this.id,
      title: title ?? this.title,
      fullAddress: fullAddress ?? this.fullAddress,
      city: city ?? this.city,
      district: district ?? this.district,
      street: street ?? this.street,
      buildingNumber: buildingNumber ?? this.buildingNumber,
      apartmentNumber: apartmentNumber ?? this.apartmentNumber,
      landmark: landmark ?? this.landmark,
      additionalInfo: additionalInfo ?? this.additionalInfo,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// الحصول على العنوان المختصر
  String get shortAddress {
    return '$district، $city';
  }

  /// الحصول على العنوان الكامل المنسق
  String get formattedAddress {
    List<String> parts = [];
    
    if (street.isNotEmpty) parts.add(street);
    if (buildingNumber.isNotEmpty) parts.add('مبنى $buildingNumber');
    if (apartmentNumber.isNotEmpty) parts.add('شقة $apartmentNumber');
    if (district.isNotEmpty) parts.add(district);
    if (city.isNotEmpty) parts.add(city);
    
    return parts.join('، ');
  }

  /// التحقق من اكتمال البيانات
  bool get isComplete {
    return title.isNotEmpty &&
           city.isNotEmpty &&
           district.isNotEmpty &&
           street.isNotEmpty &&
           buildingNumber.isNotEmpty;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Address && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Address(id: $id, title: $title, city: $city, district: $district)';
  }
}

/// أنواع العناوين المحددة مسبقاً
class AddressType {
  static const String home = 'المنزل';
  static const String work = 'العمل';
  static const String other = 'أخرى';

  static const List<String> allTypes = [home, work, other];

  /// الحصول على أيقونة نوع العنوان
  static String getIcon(String type) {
    switch (type) {
      case home:
        return '🏠';
      case work:
        return '🏢';
      default:
        return '📍';
    }
  }
}

/// المدن المتاحة في اليمن
class SaudiCities {
  static const List<String> cities = [
    'صنعاء',
  ];

  /// الحصول على أحياء المدينة (مثال صنعاء)
  static List<String> getDistricts(String city) {
    switch (city) {
      case 'صنعاء':
        return [
          'سعوان',
          'الجراف',
          'حدة',
          'شملان',
          'الستين',
          'السبعين',
          'الدائري',
          'التحرير',
          'شارع القيادة',
        ];
      default:
        return [
          'سعوان',
          'الجراف',
          'حدة',
          'شملان',
          'الستين',
          'السبعين',
          'الدائري',
          'التحرير',
          'شارع القيادة',
        ];
    }
  }
}
