import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:test2/models/PaymentMethod.dart';
import 'package:test2/services/PaymentService.dart';
import 'package:test2/widgets/PaymentMethodSelector.dart';
import 'package:test2/pages/PaymentDetailsPage.dart';
import 'package:test2/providers/CartProvider.dart';
import 'package:test2/providers/CustomerDataProvider.dart';
import 'package:test2/providers/OrderProvider.dart';
import 'package:test2/providers/AddressProvider.dart';
import 'package:test2/widgets/CustomSnackBars.dart';
import 'package:test2/models/Address.dart';
import 'package:test2/pages/MyOrdersPage.dart';
import 'package:test2/pages/AddressSelectionPage.dart';

class CheckoutPage extends StatefulWidget {
  final List<Map<String, dynamic>> cartItems;
  final double totalAmount;
  final String? storeName;

  const CheckoutPage({
    Key? key,
    required this.cartItems,
    required this.totalAmount,
    this.storeName,
  }) : super(key: key);

  @override
  _CheckoutPageState createState() => _CheckoutPageState();
}

class _CheckoutPageState extends State<CheckoutPage> {
  final PaymentService _paymentService = PaymentService();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  PaymentMethod? _selectedPaymentMethod;
  bool _isProcessing = false;
  Address? _selectedAddress;

  @override
  void initState() {
    super.initState();
    _loadCustomerData();
  }

  /// تحميل بيانات العميل وملء الحقول تلقائياً
  Future<void> _loadCustomerData() async {
    final customerProvider =
        Provider.of<CustomerDataProvider>(context, listen: false);
    await customerProvider.ensureDataLoaded();

    if (customerProvider.isLoggedIn) {
      // ملء رقم الهاتف إذا كان متوفراً
      if (customerProvider.phone.isNotEmpty) {
        _phoneController.text = customerProvider.phone;
      }

      // ملء العنوان الافتراضي إذا كان متوفراً
      final defaultAddress = customerProvider.defaultAddress;
      if (defaultAddress != null && defaultAddress.fullAddress.isNotEmpty) {
        _addressController.text = defaultAddress.fullAddress;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: AppBar(
        title: Text(
          'إتمام الطلب',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Color(0xFF4C53A5),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: ListView(
                padding: EdgeInsets.all(16),
                children: [
                  // ملخص الطلب
                  _buildOrderSummaryCard(),

                  SizedBox(height: 16),

                  // معلومات التوصيل
                  _buildDeliveryInfoCard(),

                  SizedBox(height: 16),

                  // اختيار طريقة الدفع
                  PaymentMethodSelector(
                    orderAmount: widget.totalAmount,
                    selectedMethod: _selectedPaymentMethod,
                    onMethodSelected: (method) {
                      setState(() {
                        _selectedPaymentMethod = method;
                      });
                    },
                    showFees: true,
                  ),

                  SizedBox(height: 16),

                  // ملاحظات إضافية
                  _buildNotesCard(),

                  SizedBox(height: 100), // مساحة للزر السفلي
                ],
              ),
            ),

            // زر إتمام الطلب
            _buildBottomButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderSummaryCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.shopping_cart, color: Color(0xFF4C53A5)),
              SizedBox(width: 8),
              Text(
                'ملخص الطلب',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),

          if (widget.storeName != null) ...[
            SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.store, color: Colors.grey.shade600, size: 16),
                SizedBox(width: 4),
                Text(
                  'من: ${widget.storeName}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],

          SizedBox(height: 16),

          // قائمة المنتجات
          ...widget.cartItems.map((item) => _buildCartItem(item)).toList(),

          Divider(thickness: 1),

          // المجموع
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المجموع:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
              Text(
                '${widget.totalAmount.toStringAsFixed(2)} ريال',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCartItem(Map<String, dynamic> item) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // صورة المنتج
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: AssetImage(item['image'] ?? 'images/1.png'),
                fit: BoxFit.cover,
              ),
            ),
          ),

          SizedBox(width: 12),

          // معلومات المنتج
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name'] ?? 'منتج',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'الكمية: ${item['quantity'] ?? 1}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          // السعر
          Text(
            '${((item['price'] ?? 0) * (item['quantity'] ?? 1)).toStringAsFixed(2)} ريال',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryInfoCard() {
    return Consumer<CustomerDataProvider>(
      builder: (context, customerProvider, child) {
        return Container(
          padding: EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.location_on, color: Color(0xFF4C53A5)),
                  SizedBox(width: 8),
                  Text(
                    'معلومات التوصيل',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF4C53A5),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 16),

              // عرض معلومات العميل من البروفايل
              if (customerProvider.isLoggedIn) ...[
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.account_circle,
                              color: Color(0xFF4C53A5), size: 24),
                          SizedBox(width: 8),
                          Text(
                            'معلومات العميل',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF4C53A5),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 12),

                      // الاسم
                      _buildProfileInfoRow(
                          Icons.person,
                          'الاسم',
                          customerProvider.fullName.isNotEmpty
                              ? customerProvider.fullName
                              : 'غير محدد'),

                      SizedBox(height: 8),

                      // رقم الهاتف
                      _buildProfileInfoRow(
                          Icons.phone,
                          'الهاتف',
                          customerProvider.phone.isNotEmpty
                              ? customerProvider.phone
                              : 'غير محدد'),

                      SizedBox(height: 8),

                      // البريد الإلكتروني
                      if (customerProvider.email.isNotEmpty &&
                          customerProvider.email != '<EMAIL>') ...[
                        _buildProfileInfoRow(Icons.email, 'البريد الإلكتروني',
                            customerProvider.email),
                        SizedBox(height: 8),
                      ],

                      // العنوان الافتراضي
                      if (customerProvider.defaultAddress != null) ...[
                        _buildProfileInfoRow(
                            Icons.location_on,
                            'العنوان المحفوظ',
                            customerProvider.defaultAddress!.title),
                        SizedBox(height: 8),
                      ],

                      // زر تحديث المعلومات
                      SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () =>
                                  _updateFromProfile(customerProvider),
                              icon: Icon(Icons.refresh, size: 16),
                              label: Text(
                                'تحديث من البروفايل',
                                style: TextStyle(fontSize: 12),
                              ),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Color(0xFF4C53A5),
                                side: BorderSide(color: Color(0xFF4C53A5)),
                                padding: EdgeInsets.symmetric(vertical: 8),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16),
              ],

              // رقم الهاتف
              TextFormField(
                controller: _phoneController,
                decoration: InputDecoration(
                  labelText: 'رقم الهاتف للتوصيل',
                  hintText: customerProvider.isLoggedIn &&
                          customerProvider.phone.isNotEmpty
                      ? 'محدث من البروفايل: ${customerProvider.phone}'
                      : 'أدخل رقم هاتفك',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: Icon(Icons.phone),
                  suffixIcon: customerProvider.isLoggedIn &&
                          customerProvider.phone.isNotEmpty
                      ? Icon(Icons.verified_user, color: Colors.green, size: 20)
                      : null,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال رقم الهاتف';
                  }
                  return null;
                },
                keyboardType: TextInputType.phone,
              ),

              SizedBox(height: 16),

              // اختيار العنوان
              _buildAddressSelection(customerProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAddressSelection(CustomerDataProvider customerProvider) {
    return Consumer<AddressProvider>(
      builder: (context, addressProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عرض العنوان المختار
            if (_selectedAddress != null) ...[
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.green, size: 20),
                        SizedBox(width: 8),
                        Text(
                          'العنوان المختار',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text(
                      _selectedAddress!.title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF4C53A5),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      _selectedAddress!.formattedAddress,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 12),
            ],

            // أزرار اختيار العنوان
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _selectAddress(),
                    icon: Icon(Icons.location_on, size: 18),
                    label: Text(_selectedAddress != null
                        ? 'تغيير العنوان'
                        : 'اختيار عنوان محفوظ'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Color(0xFF4C53A5),
                      side: BorderSide(color: Color(0xFF4C53A5)),
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showManualAddressInput(),
                    icon: Icon(Icons.edit, size: 18),
                    label: Text('إدخال يدوي'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.grey[600],
                      side: BorderSide(color: Colors.grey[400]!),
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),

            // حقل الإدخال اليدوي (مخفي افتراضياً)
            if (_selectedAddress == null &&
                _addressController.text.isEmpty) ...[
              SizedBox(height: 16),
              TextFormField(
                controller: _addressController,
                decoration: InputDecoration(
                  labelText: 'عنوان التوصيل',
                  hintText: 'أدخل عنوان التوصيل بالتفصيل',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: Icon(Icons.location_on),
                ),
                validator: (value) {
                  if (_selectedAddress == null &&
                      (value == null || value.isEmpty)) {
                    return 'يرجى اختيار عنوان أو إدخال عنوان التوصيل';
                  }
                  return null;
                },
                maxLines: 2,
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buildNotesCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.note_add, color: Color(0xFF4C53A5)),
              SizedBox(width: 8),
              Text(
                'ملاحظات إضافية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          TextFormField(
            controller: _notesController,
            decoration: InputDecoration(
              labelText: 'ملاحظات (اختياري)',
              hintText: 'أضف أي ملاحظات للطلب...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              prefixIcon: Icon(Icons.note),
            ),
            maxLines: 3,
            maxLength: 200,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    final totalWithFees = _selectedPaymentMethod != null
        ? _paymentService.calculateTotalAmount(
            widget.totalAmount, _selectedPaymentMethod!)
        : widget.totalAmount;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_selectedPaymentMethod != null) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المبلغ الإجمالي:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C53A5),
                  ),
                ),
                Text(
                  '${totalWithFees.toStringAsFixed(2)} ريال',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C53A5),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
          ],
          Container(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _selectedPaymentMethod == null || _isProcessing
                  ? null
                  : _proceedToPayment,
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF4C53A5),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              child: _isProcessing
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        ),
                        SizedBox(width: 12),
                        Text(
                          'جاري المعالجة...',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    )
                  : Text(
                      _selectedPaymentMethod == null
                          ? 'اختر طريقة الدفع'
                          : 'متابعة الدفع',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// اختيار عنوان من العناوين المحفوظة
  void _selectAddress() async {
    final addressProvider =
        Provider.of<AddressProvider>(context, listen: false);
    await addressProvider.loadAddresses();

    if (addressProvider.addresses.isEmpty) {
      CustomSnackBars.showInfo(
        context,
        message: 'لا توجد عناوين محفوظة',
        subtitle: 'أضف عنوان جديد أولاً',
      );
      return;
    }

    final selectedAddress = await Navigator.push<Address>(
      context,
      MaterialPageRoute(
        builder: (context) => AddressSelectionPage(
          selectedAddress: _selectedAddress,
        ),
      ),
    );

    if (selectedAddress != null) {
      setState(() {
        _selectedAddress = selectedAddress;
        _addressController.clear(); // مسح الإدخال اليدوي
      });
    }
  }

  /// إظهار حقل الإدخال اليدوي
  void _showManualAddressInput() {
    setState(() {
      _selectedAddress = null;
    });

    // التركيز على حقل الإدخال
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(FocusNode());
    });
  }

  /// تحديث معلومات التوصيل من البروفايل
  void _updateFromProfile(CustomerDataProvider customerProvider) {
    if (customerProvider.isLoggedIn) {
      // تحديث رقم الهاتف
      if (customerProvider.phone.isNotEmpty) {
        _phoneController.text = customerProvider.phone;
      }

      // تحديث العنوان من العنوان الافتراضي
      final defaultAddress = customerProvider.defaultAddress;
      if (defaultAddress != null) {
        _addressController.text = defaultAddress.fullAddress;
      }

      // عرض رسالة تأكيد
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 8),
              Text('تم تحديث المعلومات من البروفايل'),
            ],
          ),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  /// بناء صف معلومات البروفايل
  Widget _buildProfileInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: Colors.grey.shade600, size: 18),
        SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade700,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  void _proceedToPayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedPaymentMethod == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى اختيار طريقة الدفع'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // إنشاء معرف الطلب
    final orderId = 'ORD${DateTime.now().millisecondsSinceEpoch}';

    // الانتقال لصفحة تفاصيل الدفع
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaymentDetailsPage(
          paymentMethod: _selectedPaymentMethod!,
          orderAmount: widget.totalAmount,
          orderId: orderId,
        ),
      ),
    );

    if (result != null && result is PaymentResult && result.success) {
      await _createOrderAfterPayment(result);
    }
  }

  /// إنشاء الطلب بعد نجاح الدفع
  Future<void> _createOrderAfterPayment(PaymentResult result) async {
    try {
      setState(() {
        _isProcessing = true;
      });

      final customerProvider =
          Provider.of<CustomerDataProvider>(context, listen: false);
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final cartProvider = Provider.of<CartProvider>(context, listen: false);

      // التأكد من تحميل بيانات العميل
      await customerProvider.ensureDataLoaded();

      if (!customerProvider.isLoggedIn) {
        throw Exception('لم يتم العثور على بيانات العميل');
      }

      // إنشاء عنوان التوصيل
      final deliveryAddress = _selectedAddress ??
          Address(
            id: 'temp_address_${DateTime.now().millisecondsSinceEpoch}',
            title: 'عنوان التوصيل',
            street: _addressController.text.trim(),
            district: 'حي العليا',
            city: 'الرياض',
            buildingNumber: '123',
            fullAddress: _addressController.text.trim(),
            isDefault: false,
          );

      // إنشاء الطلب
      final order = await orderProvider.createOrderFromCustomerData(
        deliveryAddress: deliveryAddress,
        items: cartProvider.cartItems,
        paymentMethod: _selectedPaymentMethod!.name,
        deliveryTime: 'في أقرب وقت',
        notes: _notesController.text.trim().isNotEmpty
            ? _notesController.text.trim()
            : null,
      );

      if (order != null) {
        // مسح السلة
        await cartProvider.clearCart();

        // عرض رسالة النجاح والانتقال للطلبات
        _showOrderSuccessDialog(result, order.id);
      } else {
        throw Exception('فشل في إنشاء الطلب');
      }
    } catch (e) {
      CustomSnackBars.showError(
        context,
        message: 'فشل في إنشاء الطلب',
        subtitle: e.toString(),
      );
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  void _showOrderSuccessDialog(PaymentResult result, [String? orderId]) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Column(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 64),
            SizedBox(height: 8),
            Text(
              'تم إرسال الطلب بنجاح!',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'شكراً لك! تم استلام طلبك وسيتم التواصل معك قريباً.',
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'رقم المعاملة: ${result.transactionId}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Color(0xFF4C53A5),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // إغلاق الحوار
              Navigator.of(context).pop(); // العودة للصفحة السابقة

              // الانتقال لصفحة الطلبات
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => MyOrdersPage()),
              );
            },
            child: Text('عرض الطلبات'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // إغلاق الحوار
              Navigator.of(context).pop(); // العودة للصفحة السابقة
            },
            child: Text('موافق'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _addressController.dispose();
    _phoneController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
