import 'package:flutter/material.dart';
import 'CartItem.dart';
import 'Address.dart';

/// حالات الطلب
enum OrderStatus {
  pending, // في الانتظار
  confirmed, // تم التأكيد من المحل
  preparing, // قيد التجهيز
  ready, // جاهز للاستلام
  pickedUp, // تم استلامه من عامل التوصيل
  onTheWay, // في الطريق
  delivered, // تم التوصيل
  cancelled, // ملغي
}

/// أنواع الدفع
enum PaymentMethod {
  cash, // نقدي
  card, // بطاقة ائتمان
  applePay, // آبل باي
  stcPay, // STC Pay
  mada, // مدى
}

/// أوقات التوصيل
enum DeliveryTime {
  asap, // في أسرع وقت ممكن
  scheduled, // وقت محدد
}

/// نموذج الطلب
class Order {
  final String id;
  final String userId;
  final String userName;
  final String userPhone;
  final String userEmail;
  final Address deliveryAddress;
  final List<CartItem> items;
  final double subtotal;
  final double deliveryFee;
  final double tax;
  final double discount;
  final double total;
  final OrderStatus status;
  final PaymentMethod paymentMethod;
  final DeliveryTime deliveryTime;
  final DateTime? scheduledTime;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<OrderStatusUpdate> statusHistory;
  final String? storeId;
  final String? storeName;
  final String? deliveryPersonId;
  final String? deliveryPersonName;
  final String? deliveryPersonPhone;
  final double? estimatedDeliveryMinutes;

  Order({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userPhone,
    required this.userEmail,
    required this.deliveryAddress,
    required this.items,
    required this.subtotal,
    required this.deliveryFee,
    required this.tax,
    required this.discount,
    required this.total,
    required this.status,
    required this.paymentMethod,
    required this.deliveryTime,
    this.scheduledTime,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.statusHistory = const [],
    this.storeId,
    this.storeName,
    this.deliveryPersonId,
    this.deliveryPersonName,
    this.deliveryPersonPhone,
    this.estimatedDeliveryMinutes,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// إنشاء من Map
  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id']?.toString() ?? '',
      userId: map['userId']?.toString() ?? '',
      userName: map['userName']?.toString() ?? '',
      userPhone: map['userPhone']?.toString() ?? '',
      userEmail: map['userEmail']?.toString() ?? '',
      deliveryAddress: Address.fromMap(map['deliveryAddress'] ?? {}),
      items: (map['items'] as List?)
              ?.map((item) => CartItem.fromMap(item))
              .toList() ??
          [],
      subtotal: (map['subtotal'] ?? 0).toDouble(),
      deliveryFee: (map['deliveryFee'] ?? 0).toDouble(),
      tax: (map['tax'] ?? 0).toDouble(),
      discount: (map['discount'] ?? 0).toDouble(),
      total: (map['total'] ?? 0).toDouble(),
      status: OrderStatus.values.firstWhere(
        (e) => e.toString().split('.').last == map['status'],
        orElse: () => OrderStatus.pending,
      ),
      paymentMethod: PaymentMethod.values.firstWhere(
        (e) => e.toString().split('.').last == map['paymentMethod'],
        orElse: () => PaymentMethod.cash,
      ),
      deliveryTime: DeliveryTime.values.firstWhere(
        (e) => e.toString().split('.').last == map['deliveryTime'],
        orElse: () => DeliveryTime.asap,
      ),
      scheduledTime: map['scheduledTime'] != null
          ? DateTime.tryParse(map['scheduledTime'].toString())
          : null,
      notes: map['notes']?.toString(),
      createdAt: map['createdAt'] != null
          ? DateTime.tryParse(map['createdAt'].toString()) ?? DateTime.now()
          : DateTime.now(),
      updatedAt: map['updatedAt'] != null
          ? DateTime.tryParse(map['updatedAt'].toString()) ?? DateTime.now()
          : DateTime.now(),
      statusHistory: (map['statusHistory'] as List?)
              ?.map((update) => OrderStatusUpdate.fromMap(update))
              .toList() ??
          [],
      storeId: map['storeId']?.toString(),
      storeName: map['storeName']?.toString(),
      deliveryPersonId: map['deliveryPersonId']?.toString(),
      deliveryPersonName: map['deliveryPersonName']?.toString(),
      deliveryPersonPhone: map['deliveryPersonPhone']?.toString(),
      estimatedDeliveryMinutes: map['estimatedDeliveryMinutes']?.toDouble(),
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'userPhone': userPhone,
      'userEmail': userEmail,
      'deliveryAddress': deliveryAddress.toMap(),
      'items': items.map((item) => item.toMap()).toList(),
      'subtotal': subtotal,
      'deliveryFee': deliveryFee,
      'tax': tax,
      'discount': discount,
      'total': total,
      'status': status.toString().split('.').last,
      'paymentMethod': paymentMethod.toString().split('.').last,
      'deliveryTime': deliveryTime.toString().split('.').last,
      'scheduledTime': scheduledTime?.toIso8601String(),
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'statusHistory': statusHistory.map((update) => update.toMap()).toList(),
      'storeId': storeId,
      'storeName': storeName,
      'deliveryPersonId': deliveryPersonId,
      'deliveryPersonName': deliveryPersonName,
      'deliveryPersonPhone': deliveryPersonPhone,
      'estimatedDeliveryMinutes': estimatedDeliveryMinutes,
    };
  }

  /// نسخ مع تعديل
  Order copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userPhone,
    String? userEmail,
    Address? deliveryAddress,
    List<CartItem>? items,
    double? subtotal,
    double? deliveryFee,
    double? tax,
    double? discount,
    double? total,
    OrderStatus? status,
    PaymentMethod? paymentMethod,
    DeliveryTime? deliveryTime,
    DateTime? scheduledTime,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<OrderStatusUpdate>? statusHistory,
    String? storeId,
    String? storeName,
    String? deliveryPersonId,
    String? deliveryPersonName,
    String? deliveryPersonPhone,
    double? estimatedDeliveryMinutes,
  }) {
    return Order(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userPhone: userPhone ?? this.userPhone,
      userEmail: userEmail ?? this.userEmail,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      tax: tax ?? this.tax,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      deliveryTime: deliveryTime ?? this.deliveryTime,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      statusHistory: statusHistory ?? this.statusHistory,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      deliveryPersonId: deliveryPersonId ?? this.deliveryPersonId,
      deliveryPersonName: deliveryPersonName ?? this.deliveryPersonName,
      deliveryPersonPhone: deliveryPersonPhone ?? this.deliveryPersonPhone,
      estimatedDeliveryMinutes:
          estimatedDeliveryMinutes ?? this.estimatedDeliveryMinutes,
    );
  }

  /// الحصول على عدد العناصر
  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);

  /// التحقق من إمكانية الإلغاء
  bool get canCancel =>
      status == OrderStatus.pending || status == OrderStatus.confirmed;

  /// التحقق من إمكانية التتبع
  bool get canTrack =>
      status != OrderStatus.cancelled && status != OrderStatus.delivered;

  /// الحصول على الوقت المتبقي المقدر
  Duration? get estimatedTimeRemaining {
    if (estimatedDeliveryMinutes == null) return null;
    final elapsed = DateTime.now().difference(createdAt);
    final total = Duration(minutes: estimatedDeliveryMinutes!.toInt());
    final remaining = total - elapsed;
    return remaining.isNegative ? Duration.zero : remaining;
  }

  /// الحصول على المتاجر المختلفة في الطلب
  Map<String, List<CartItem>> get storeGroups {
    Map<String, List<CartItem>> groups = {};
    for (CartItem item in items) {
      String storeName = item.storeName ?? 'متجر غير محدد';
      if (!groups.containsKey(storeName)) {
        groups[storeName] = [];
      }
      groups[storeName]!.add(item);
    }
    return groups;
  }

  /// الحصول على عدد المتاجر في الطلب
  int get storeCount => storeGroups.length;

  /// الحصول على قائمة أسماء المتاجر
  List<String> get storeNames => storeGroups.keys.toList();

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Order && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Order(id: $id, status: $status, total: $total, items: ${items.length})';
  }
}

/// تحديث حالة الطلب
class OrderStatusUpdate {
  final OrderStatus status;
  final DateTime timestamp;
  final String? message;
  final String? updatedBy;
  final String? updatedByRole; // customer, store, delivery, system

  OrderStatusUpdate({
    required this.status,
    required this.timestamp,
    this.message,
    this.updatedBy,
    this.updatedByRole,
  });

  factory OrderStatusUpdate.fromMap(Map<String, dynamic> map) {
    return OrderStatusUpdate(
      status: OrderStatus.values.firstWhere(
        (e) => e.toString().split('.').last == map['status'],
        orElse: () => OrderStatus.pending,
      ),
      timestamp:
          DateTime.tryParse(map['timestamp'].toString()) ?? DateTime.now(),
      message: map['message']?.toString(),
      updatedBy: map['updatedBy']?.toString(),
      updatedByRole: map['updatedByRole']?.toString(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'status': status.toString().split('.').last,
      'timestamp': timestamp.toIso8601String(),
      'message': message,
      'updatedBy': updatedBy,
      'updatedByRole': updatedByRole,
    };
  }
}

/// مساعدات حالة الطلب
class OrderStatusHelper {
  /// الحصول على اسم الحالة بالعربية
  static String getStatusName(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'في الانتظار';
      case OrderStatus.confirmed:
        return 'تم التأكيد';
      case OrderStatus.preparing:
        return 'قيد التجهيز';
      case OrderStatus.ready:
        return 'جاهز للاستلام';
      case OrderStatus.pickedUp:
        return 'تم الاستلام';
      case OrderStatus.onTheWay:
        return 'في الطريق';
      case OrderStatus.delivered:
        return 'تم التوصيل';
      case OrderStatus.cancelled:
        return 'ملغي';
    }
  }

  /// الحصول على أيقونة الحالة
  static String getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return '⏳';
      case OrderStatus.confirmed:
        return '✅';
      case OrderStatus.preparing:
        return '👨‍🍳';
      case OrderStatus.ready:
        return '📦';
      case OrderStatus.pickedUp:
        return '🚗';
      case OrderStatus.onTheWay:
        return '🛣️';
      case OrderStatus.delivered:
        return '🎉';
      case OrderStatus.cancelled:
        return '❌';
    }
  }

  /// الحصول على لون الحالة
  static Color getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.confirmed:
        return Colors.blue;
      case OrderStatus.preparing:
        return Colors.purple;
      case OrderStatus.ready:
        return Colors.green;
      case OrderStatus.pickedUp:
        return Colors.teal;
      case OrderStatus.onTheWay:
        return Colors.indigo;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }

  /// الحصول على رسالة الحالة
  static String getStatusMessage(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'طلبك قيد المراجعة من المحل';
      case OrderStatus.confirmed:
        return 'تم تأكيد طلبك وبدء التجهيز';
      case OrderStatus.preparing:
        return 'يتم تجهيز طلبك الآن';
      case OrderStatus.ready:
        return 'طلبك جاهز وفي انتظار عامل التوصيل';
      case OrderStatus.pickedUp:
        return 'تم استلام طلبك من عامل التوصيل';
      case OrderStatus.onTheWay:
        return 'عامل التوصيل في الطريق إليك';
      case OrderStatus.delivered:
        return 'تم توصيل طلبك بنجاح';
      case OrderStatus.cancelled:
        return 'تم إلغاء الطلب';
    }
  }
}
