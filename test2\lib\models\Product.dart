/// نموذج المنتج
class Product {
  final String id;
  final String name;
  final String description;
  final double price;
  final String imageUrl;
  final String category;
  final double rating;
  final int reviewCount;
  final bool isAvailable;
  final int orderCount;
  final bool isFeatured;
  final double? discountPercentage;
  final String? storeId;
  final String? storeName;
  final Map<String, dynamic>? nutritionInfo;
  final List<String>? tags;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.imageUrl,
    required this.category,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.isAvailable = true,
    this.orderCount = 0,
    this.isFeatured = false,
    this.discountPercentage,
    this.storeId,
    this.storeName,
    this.nutritionInfo,
    this.tags,
    this.createdAt,
    this.updatedAt,
  });

  /// إنشاء Product من Map (من DataManager)
  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id']?.toString() ?? '',
      name: map['name']?.toString() ?? '',
      description: map['description']?.toString() ?? '',
      price: _parseDouble(map['price']),
      imageUrl: map['imageUrl']?.toString() ?? 'images/${map['id']}.png',
      category: map['category']?.toString() ?? '',
      rating: _parseDouble(map['rating']),
      reviewCount: _parseInt(map['reviewCount']),
      isAvailable: map['isAvailable'] ?? true,
      orderCount: _parseInt(map['orderCount']),
      isFeatured: map['isFeatured'] ?? false,
      discountPercentage: map['discountPercentage'] != null
          ? _parseDouble(map['discountPercentage'])
          : null,
      storeId: map['storeId']?.toString(),
      storeName: map['storeName']?.toString(),
      nutritionInfo: map['nutritionInfo'] as Map<String, dynamic>?,
      tags: map['tags'] != null ? List<String>.from(map['tags']) : null,
      createdAt: map['createdAt'] != null
          ? DateTime.tryParse(map['createdAt'].toString())
          : null,
      updatedAt: map['updatedAt'] != null
          ? DateTime.tryParse(map['updatedAt'].toString())
          : null,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'imageUrl': imageUrl,
      'category': category,
      'rating': rating,
      'reviewCount': reviewCount,
      'isAvailable': isAvailable,
      'orderCount': orderCount,
      'isFeatured': isFeatured,
      'discountPercentage': discountPercentage,
      'storeId': storeId,
      'storeName': storeName,
      'nutritionInfo': nutritionInfo,
      'tags': tags,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// حساب السعر بعد الخصم
  double get discountedPrice {
    if (discountPercentage != null && discountPercentage! > 0) {
      return price * (1 - discountPercentage! / 100);
    }
    return price;
  }

  /// التحقق من وجود خصم
  bool get hasDiscount {
    return discountPercentage != null && discountPercentage! > 0;
  }

  /// الحصول على نص الخصم
  String get discountText {
    if (hasDiscount) {
      return '-${discountPercentage!.toInt()}%';
    }
    return '';
  }

  /// الحصول على السعر المنسق
  String get formattedPrice {
    return '${price.toStringAsFixed(2)} ريال';
  }

  /// الحصول على السعر بعد الخصم المنسق
  String get formattedDiscountedPrice {
    return '${discountedPrice.toStringAsFixed(2)} ريال';
  }

  /// الحصول على التقييم المنسق
  String get formattedRating {
    return rating.toStringAsFixed(1);
  }

  /// الحصول على الصورة (للتوافق مع SearchService)
  String get image => imageUrl;

  /// نسخ مع تعديل
  Product copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? imageUrl,
    String? category,
    double? rating,
    int? reviewCount,
    bool? isAvailable,
    int? orderCount,
    bool? isFeatured,
    double? discountPercentage,
    String? storeId,
    String? storeName,
    Map<String, dynamic>? nutritionInfo,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isAvailable: isAvailable ?? this.isAvailable,
      orderCount: orderCount ?? this.orderCount,
      isFeatured: isFeatured ?? this.isFeatured,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      nutritionInfo: nutritionInfo ?? this.nutritionInfo,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Product(id: $id, name: $name, price: $price, category: $category)';
  }

  /// دوال مساعدة لتحويل البيانات
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  static int _parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }
}

/// فئات المنتجات
class ProductCategory {
  static const String all = 'الكل';
  static const String food = 'طعام';
  static const String drinks = 'مشروبات';
  static const String sweets = 'حلويات';
  static const String groceries = 'بقالة';
  static const String pharmacy = 'صيدلية';
  static const String electronics = 'إلكترونيات';
  static const String clothing = 'ملابس';
  static const String books = 'كتب';
  static const String sports = 'رياضة';
  static const String beauty = 'تجميل';
  static const String home = 'منزل';
  static const String automotive = 'سيارات';
  static const String toys = 'ألعاب';
  static const String pets = 'حيوانات أليفة';
  static const String other = 'أخرى';

  static const List<String> allCategories = [
    all,
    food,
    drinks,
    sweets,
    groceries,
    pharmacy,
    electronics,
    clothing,
    books,
    sports,
    beauty,
    home,
    automotive,
    toys,
    pets,
    other,
  ];

  /// الحصول على أيقونة الفئة
  static String getCategoryIcon(String category) {
    switch (category) {
      case food:
        return '🍽️';
      case drinks:
        return '🥤';
      case sweets:
        return '🍰';
      case groceries:
        return '🛒';
      case pharmacy:
        return '💊';
      case electronics:
        return '📱';
      case clothing:
        return '👕';
      case books:
        return '📚';
      case sports:
        return '⚽';
      case beauty:
        return '💄';
      case home:
        return '🏠';
      case automotive:
        return '🚗';
      case toys:
        return '🧸';
      case pets:
        return '🐕';
      default:
        return '📦';
    }
  }
}
