import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:test2/providers/CartProvider.dart';
import 'package:test2/providers/CustomerDataProvider.dart';
import 'package:test2/providers/OrderProvider.dart';
import 'package:test2/models/Address.dart';
import 'package:test2/widgets/CustomSnackBars.dart';
import 'package:test2/pages/MyOrdersPage.dart';
import '../services/AddressService.dart';


class NewCheckoutPage extends StatefulWidget {
  @override
  _NewCheckoutPageState createState() => _NewCheckoutPageState();
}

class _NewCheckoutPageState extends State<NewCheckoutPage> {
  // متغيرات النموذج
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _notesController = TextEditingController();

  // العنوان المختار
  Address? _selectedAddress;

  // خيارات الدفع
  String _selectedPaymentMethod = 'cash';
  final List<Map<String, dynamic>> _paymentMethods = [
    {
      'id': 'cash',
      'name': 'الدفع عند الاستلام',
      'icon': Icons.money,
      'description': 'ادفع نقداً عند وصول الطلب',
      'color': Colors.green,
      'fees': 0.0,
    },
    {
      'id': 'card',
      'name': 'بطاقة ائتمان',
      'icon': Icons.credit_card,
      'description': 'فيزا، ماستركارد، مدى',
      'color': Colors.blue,
      'fees': 2.5, // رسوم 2.5%
    },
    {
      'id': 'apple_pay',
      'name': 'Apple Pay',
      'icon': Icons.apple,
      'description': 'دفع سريع وآمن',
      'color': Colors.black,
      'fees': 0.0,
    },
    {
      'id': 'stc_pay',
      'name': 'STC Pay',
      'icon': Icons.phone_android,
      'description': 'محفظة STC الرقمية',
      'color': Color(0xFF6B1E9B),
      'fees': 1.0, // رسوم 1%
    },
    {
      'id': 'mada',
      'name': 'مدى',
      'icon': Icons.payment,
      'description': 'شبكة المدفوعات السعودية',
      'color': Color(0xFF00A651),
      'fees': 1.5, // رسوم 1.5%
    },
  ];

  // متغيرات أخرى
  bool _isLoading = false;
  String _selectedDeliveryTime = 'asap';

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }


  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// تحميل بيانات العميل
  void _loadUserProfile() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final customerProvider =
          Provider.of<CustomerDataProvider>(context, listen: false);
      await customerProvider.ensureDataLoaded();

      if (customerProvider.isLoggedIn) {
        _nameController.text = customerProvider.fullName;
        _phoneController.text = customerProvider.phone;
        _selectedAddress = customerProvider.defaultAddress;
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: _buildAppBar(),
      body: Consumer<CartProvider>(
        builder: (context, cartProvider, child) {
          if (cartProvider.itemCount == 0) {
            return _buildEmptyCart();
          }

          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildOrderSummary(cartProvider),
                        SizedBox(height: 20),
                        _buildDeliveryInfo(),
                        SizedBox(height: 20),
                        _buildPaymentMethods(),
                        SizedBox(height: 20),
                        _buildOrderNotes(),
                        SizedBox(height: 100), // مساحة للزر السفلي
                      ],
                    ),
                  ),
                ),
              ),
              _buildBottomSection(cartProvider),
            ],
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Color(0xFF4C53A5),
      elevation: 0,
      centerTitle: true,
      title: Text(
        'إتمام الطلب',
        style: TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildEmptyCart() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 80,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'السلة فارغة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'أضف منتجات للمتابعة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          SizedBox(height: 20),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFF4C53A5),
              padding: EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            ),
            child: Text(
              'العودة للتسوق',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummary(CartProvider cartProvider) {
    final summary = cartProvider.cartSummary;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.shopping_cart, color: Color(0xFF4C53A5)),
              SizedBox(width: 8),
              Text(
                'ملخص الطلب (${cartProvider.itemCount} منتج)',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),
          SizedBox(height: 12),

          // عرض المنتجات
          ...cartProvider.cartItems
              .map((item) => _buildCartItem(item))
              .toList(),

          Divider(),
          _buildSummaryRow(
              'المجموع الفرعي', '${summary.subtotal.toStringAsFixed(2)} ريال'),
          _buildSummaryRow(
              'الضريبة (15%)', '${summary.tax.toStringAsFixed(2)} ريال'),
          _buildSummaryRow(
              'رسوم الشحن',
              summary.shipping == 0
                  ? 'مجاني'
                  : '${summary.shipping.toStringAsFixed(2)} ريال'),
          if (summary.discount > 0)
            _buildSummaryRow(
                'الخصم', '-${summary.discount.toStringAsFixed(2)} ريال',
                color: Colors.green),

          // رسوم الدفع
          if (_getSelectedPaymentFees() > 0)
            _buildSummaryRow('رسوم الدفع',
                '${_getSelectedPaymentFees().toStringAsFixed(2)} ريال',
                color: Colors.orange),

          Divider(thickness: 2),
          _buildSummaryRow(
            'المجموع الإجمالي',
            '${_getTotalWithFees(summary.total).toStringAsFixed(2)} ريال',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildCartItem(item) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // صورة المنتج
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: AssetImage(item.imageUrl),
                fit: BoxFit.cover,
              ),
            ),
          ),
          SizedBox(width: 12),

          // معلومات المنتج
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'الكمية: ${item.quantity}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          // السعر
          Text(
            '${item.totalPrice.toStringAsFixed(2)} ريال',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value,
      {bool isTotal = false, Color? color}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: color ?? (isTotal ? Color(0xFF4C53A5) : Colors.grey[600]),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: color ?? (isTotal ? Color(0xFF4C53A5) : Colors.black),
            ),
          ),
        ],
      ),
    );
  }

  double _getSelectedPaymentFees() {
    final method =
        _paymentMethods.firstWhere((m) => m['id'] == _selectedPaymentMethod);
    final feesPercent = method['fees'] as double;
    return feesPercent > 0
        ? (Provider.of<CartProvider>(context, listen: false).totalPrice *
            feesPercent /
            100)
        : 0.0;
  }

  double _getTotalWithFees(double originalTotal) {
    return originalTotal + _getSelectedPaymentFees();
  }

  Widget _buildDeliveryInfo() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: Color(0xFF4C53A5)),
              SizedBox(width: 8),
              Text(
                'معلومات التوصيل',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),

          // عرض معلومات العميل من البروفايل
          Consumer<CustomerDataProvider>(
            builder: (context, customerProvider, child) {
              if (customerProvider.isLoggedIn) {
                return Container(
                  padding: EdgeInsets.all(16),
                  margin: EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.account_circle,
                              color: Color(0xFF4C53A5), size: 24),
                          SizedBox(width: 8),
                          Text(
                            'معلومات العميل',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF4C53A5),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 12),
                      _buildProfileInfoRow(
                          Icons.person, 'الاسم', customerProvider.fullName),
                      SizedBox(height: 8),
                      _buildProfileInfoRow(
                          Icons.phone, 'الهاتف', customerProvider.phone),
                      if (customerProvider.email.isNotEmpty &&
                          customerProvider.email != '<EMAIL>') ...[
                        SizedBox(height: 8),
                        _buildProfileInfoRow(Icons.email, 'البريد الإلكتروني',
                            customerProvider.email),
                      ],
                      if (customerProvider.defaultAddress != null) ...[
                        SizedBox(height: 8),
                        _buildProfileInfoRow(
                            Icons.location_on,
                            'العنوان المحفوظ',
                            customerProvider.defaultAddress!.title),
                      ],
                      SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () =>
                                  _updateFromProfile(customerProvider),
                              icon: Icon(Icons.refresh, size: 16),
                              label: Text('تحديث من البروفايل',
                                  style: TextStyle(fontSize: 12)),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Color(0xFF4C53A5),
                                side: BorderSide(color: Color(0xFF4C53A5)),
                                padding: EdgeInsets.symmetric(vertical: 8),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              }
              return SizedBox.shrink();
            },
          ),

          Consumer<CustomerDataProvider>(
            builder: (context, customerProvider, child) {
              return TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: 'الاسم الكامل',
                  hintText: customerProvider.isLoggedIn &&
                          customerProvider.fullName.isNotEmpty
                      ? 'محدث من البروفايل: ${customerProvider.fullName}'
                      : 'أدخل اسمك الكامل',
                  prefixIcon: Icon(Icons.person),
                  suffixIcon: customerProvider.isLoggedIn &&
                          customerProvider.fullName.isNotEmpty
                      ? Icon(Icons.verified_user, color: Colors.green, size: 20)
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال الاسم';
                  }
                  return null;
                },
              );
            },
          ),
          SizedBox(height: 12),
          Consumer<CustomerDataProvider>(
            builder: (context, customerProvider, child) {
              return TextFormField(
                controller: _phoneController,
                decoration: InputDecoration(
                  labelText: 'رقم الهاتف',
                  hintText: customerProvider.isLoggedIn &&
                          customerProvider.phone.isNotEmpty
                      ? 'محدث من البروفايل: ${customerProvider.phone}'
                      : 'أدخل رقم هاتفك',
                  prefixIcon: Icon(Icons.phone),
                  suffixIcon: customerProvider.isLoggedIn &&
                          customerProvider.phone.isNotEmpty
                      ? Icon(Icons.verified_user, color: Colors.green, size: 20)
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال رقم الهاتف';
                  }
                  return null;
                },
              );
            },
          ),
          SizedBox(height: 12),
          // اختيار العنوان
          Consumer<CustomerDataProvider>(
            builder: (context, customerProvider, child) {
              final addresses = customerProvider.addresses;

              if (addresses.isEmpty) {
                return Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.location_off, color: Colors.grey),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'لا توجد عناوين محفوظة. يرجى إضافة عنوان من الملف الشخصي.',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ),
                    ],
                  ),
                );
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'اختر عنوان التوصيل',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF4C53A5),
                    ),
                  ),
                  SizedBox(height: 12),
                  ...addresses
                      .map((address) => _buildAddressOption(address))
                      .toList(),
                ],
              );
            },
          ),
          SizedBox(height: 16),
          Text(
            'وقت التوصيل',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 8),
          _buildDeliveryTimeOptions(),
        ],
      ),
    );
  }

  Widget _buildDeliveryTimeOptions() {
    return Column(
      children: [
        RadioListTile<String>(
          title: Text('في أسرع وقت ممكن'),
          subtitle: Text('30-45 دقيقة'),
          value: 'asap',
          groupValue: _selectedDeliveryTime,
          onChanged: (value) {
            setState(() {
              _selectedDeliveryTime = value!;
            });
          },
        ),
        RadioListTile<String>(
          title: Text('وقت محدد'),
          subtitle: Text('اختر الوقت المناسب'),
          value: 'scheduled',
          groupValue: _selectedDeliveryTime,
          onChanged: (value) {
            setState(() {
              _selectedDeliveryTime = value!;
            });
          },
        ),
      ],
    );
  }

  Widget _buildPaymentMethods() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.payment, color: Color(0xFF4C53A5)),
              SizedBox(width: 8),
              Text(
                'طريقة الدفع',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          ..._paymentMethods
              .map((method) => _buildPaymentMethodTile(method))
              .toList(),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodTile(Map<String, dynamic> method) {
    final isSelected = _selectedPaymentMethod == method['id'];
    final fees = method['fees'] as double;

    return Container(
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border.all(
          color: isSelected ? Color(0xFF4C53A5) : Colors.grey.shade300,
          width: isSelected ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
        color: isSelected ? Color(0xFF4C53A5).withOpacity(0.05) : Colors.white,
      ),
      child: RadioListTile<String>(
        value: method['id'],
        groupValue: _selectedPaymentMethod,
        onChanged: (value) {
          setState(() {
            _selectedPaymentMethod = value!;
          });
        },
        title: Row(
          children: [
            Icon(
              method['icon'],
              color: method['color'],
              size: 24,
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    method['name'],
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Color(0xFF4C53A5) : Colors.black,
                    ),
                  ),
                  Text(
                    method['description'],
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  if (fees > 0)
                    Text(
                      'رسوم: ${fees}%',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.orange,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
        activeColor: Color(0xFF4C53A5),
      ),
    );
  }

  Widget _buildOrderNotes() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.note_add, color: Color(0xFF4C53A5)),
              SizedBox(width: 8),
              Text(
                'ملاحظات إضافية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          TextFormField(
            controller: _notesController,
            decoration: InputDecoration(
              labelText: 'ملاحظات (اختياري)',
              hintText: 'أضف أي ملاحظات للطلب...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              prefixIcon: Icon(Icons.note),
            ),
            maxLines: 3,
            maxLength: 200,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomSection(CartProvider cartProvider) {
    final totalWithFees = _getTotalWithFees(cartProvider.cartSummary.total);

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المبلغ الإجمالي:',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
              Text(
                '${totalWithFees.toStringAsFixed(2)} ريال',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Container(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed:
                  _isLoading ? null : () => _proceedToPayment(cartProvider),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF4C53A5),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              child: _isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        ),
                        SizedBox(width: 12),
                        Text(
                          'جاري المعالجة...',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    )
                  : Text(
                      'تأكيد الطلب',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// تحديث معلومات التوصيل من البروفايل
  void _updateFromProfile(CustomerDataProvider customerProvider) {
    if (customerProvider.isLoggedIn) {
      // تحديث الاسم
      if (customerProvider.fullName.isNotEmpty) {
        _nameController.text = customerProvider.fullName;
      }

      // تحديث رقم الهاتف
      if (customerProvider.phone.isNotEmpty) {
        _phoneController.text = customerProvider.phone;
      }

      // تحديث العنوان من العنوان الافتراضي
      final defaultAddress = customerProvider.defaultAddress;
      if (defaultAddress != null) {
        _selectedAddress = defaultAddress;
      }

      setState(() {});

      // عرض رسالة تأكيد
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 8),
              Text('تم تحديث المعلومات من البروفايل'),
            ],
          ),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  /// بناء صف معلومات البروفايل
  Widget _buildProfileInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: Colors.grey.shade600, size: 18),
        SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade700,
          ),
        ),
        Expanded(
          child: Text(
            value.isNotEmpty ? value : 'غير محدد',
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// بناء خيار العنوان
  Widget _buildAddressOption(Address address) {
    final isSelected = _selectedAddress?.id == address.id;

    return Container(
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border.all(
          color: isSelected ? Color(0xFF4C53A5) : Colors.grey.shade300,
          width: isSelected ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
        color: isSelected ? Color(0xFF4C53A5).withOpacity(0.05) : Colors.white,
      ),
      child: RadioListTile<String>(
        value: address.id,
        groupValue: _selectedAddress?.id,
        onChanged: (value) {
          setState(() {
            _selectedAddress = address;
          });
        },
        title: Row(
          children: [
            Text(
              AddressType.getIcon(address.title),
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    address.title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Color(0xFF4C53A5) : Colors.black,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    address.formattedAddress,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (address.landmark != null &&
                      address.landmark!.isNotEmpty) ...[
                    SizedBox(height: 2),
                    Text(
                      'علامة مميزة: ${address.landmark}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
        activeColor: Color(0xFF4C53A5),
      ),
    );
  }

  void _proceedToPayment(CartProvider cartProvider) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من اختيار العنوان
    if (_selectedAddress == null) {
      CustomSnackBars.showError(
        context,
        message: 'يرجى اختيار عنوان التوصيل',
        subtitle: 'اختر عنوان من القائمة أو أضف عنوان جديد',
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // الحصول على مزودي الحالة
      final customerProvider =
          Provider.of<CustomerDataProvider>(context, listen: false);
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);

      // التأكد من تحميل بيانات العميل
      await customerProvider.ensureDataLoaded();

      // التحقق من وجود بيانات العميل
      if (!customerProvider.isLoggedIn) {
        throw Exception('لم يتم العثور على بيانات العميل');
      }

      // إنشاء الطلب باستخدام CustomerDataProvider
      final order = await orderProvider.createOrderFromCustomerData(
        deliveryAddress: _selectedAddress!,
        items: cartProvider.cartItems,
        paymentMethod: _selectedPaymentMethod,
        deliveryTime:
            _selectedDeliveryTime == 'asap' ? 'في أقرب وقت' : 'وقت محدد',
        scheduledTime: _selectedDeliveryTime == 'scheduled'
            ? DateTime.now().add(Duration(hours: 2)) // مثال لوقت محدد
            : null,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      );

      if (order != null) {
        // مسح السلة
        await cartProvider.clearCart();

        // عرض رسالة نجاح
        CustomSnackBars.showSuccess(
          context,
          message: 'تم تأكيد طلبك بنجاح!',
          subtitle:
              'رقم الطلب: ${order.id}\nسيتم التوصيل إلى: ${_selectedAddress!.title}',
        );

        // الانتقال لصفحة الطلبات
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => MyOrdersPage(initialOrderId: order.id),
          ),
        );
      } else {
        throw Exception('فشل في إنشاء الطلب');
      }
    } catch (e) {
      CustomSnackBars.showError(
        context,
        message: 'فشل في معالجة الطلب',
        subtitle: e.toString(),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
