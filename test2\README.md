# زاد - <PERSON>ad

تطبيق توصيل طعام ومتاجر شامل مبني بـ Flutter

## 📱 نظرة عامة

زاد هو تطبيق توصيل شامل يهدف إلى ربط المستخدمين بالمطاعم والمتاجر المحلية. يوفر التطبيق تجربة مستخدم سلسة وواجهة عربية جميلة مع دعم كامل للغة العربية.

## ✨ الميزات الرئيسية

### 🏠 الصفحة الرئيسية
- شريط بحث ذكي
- عرض التصنيفات المختلفة
- المطاعم والمتاجر الأكثر شهرة
- واجهة مستخدم جذابة

### 🛍️ إدارة المتاجر
- تصفح المتاجر القريبة
- تصنيفات متنوعة (مطاعم، بقالة، صيدليات، ملابس، إلكترونيات)
- تقييمات ومراجعات
- أوقات التوصيل المتوقعة

### 🛒 نظام السلة
- إضافة وإزالة المنتجات
- تحديث الكميات
- حساب المجموع التلقائي
- كوبونات الخصم

### 📦 إدارة الطلبات
- عرض الطلبات الحالية والسابقة
- تتبع حالة الطلب
- تفاصيل كاملة لكل طلب
- إمكانية إعادة الطلب

### 👤 الملف الشخصي
- إدارة المعلومات الشخصية
- عناوين التوصيل
- طرق الدفع
- المفضلة
- الإعدادات

### ⚙️ الإعدادات
- إعدادات الإشعارات
- تحديد الموقع
- الوضع الليلي
- اختيار اللغة
- إعدادات الخصوصية

## 🎨 التصميم

### الألوان الأساسية
- **اللون الأساسي**: `#C3243B` (أحمر يمني)
- **لون الخلفية**: `#EDECF2` (رمادي فاتح)
- **لون النص**: `#4C53A5` (أزرق داكن)
- **لون التمييز**: `#F5AA49` (برتقالي)

### المكونات
- تصميم متجاوب مع جميع أحجام الشاشات
- دعم كامل للغة العربية (RTL)
- رسوم متحركة سلسة
- واجهة مستخدم بديهية

## 🛠️ التقنيات المستخدمة

### إطار العمل
- **Flutter**: إطار العمل الأساسي
- **Dart**: لغة البرمجة

### المكتبات الرئيسية
- `flutter_screenutil`: للتصميم المتجاوب
- `curved_navigation_bar`: شريط التنقل المنحني
- `badges`: للشارات والإشعارات
- `clippy_flutter`: للأشكال المقصوصة
- `flutter_rating_bar`: نظام التقييم

## 📁 هيكل المشروع

```
lib/
├── main.dart                 # نقطة البداية
├── pages/                    # صفحات التطبيق
│   ├── SplashScreen.dart    # شاشة البداية
│   ├── HomePages.dart       # الصفحة الرئيسية
│   ├── StoresPage.dart      # صفحة المتاجر
│   ├── CartPage.dart        # صفحة السلة
│   ├── OrdersPage.dart      # صفحة الطلبات
│   ├── ProfilePage.dart     # صفحة الملف الشخصي
│   └── SettingsPage.dart    # صفحة الإعدادات
├── widgets/                  # المكونات القابلة لإعادة الاستخدام
│   ├── CustomBottomNavBar.dart
│   ├── HomeAppBar.dart
│   └── ...
└── utils/                    # الأدوات المساعدة
    ├── AppColors.dart       # الألوان والأنماط
    ├── AppConfig.dart       # إعدادات التطبيق
    └── AppState.dart        # إدارة الحالة
```

## 🚀 التشغيل

### المتطلبات
- Flutter SDK (3.4.3 أو أحدث)
- Dart SDK
- Android Studio / VS Code
- جهاز Android أو iOS أو محاكي

### خطوات التشغيل
1. استنساخ المشروع
```bash
git clone [repository-url]
cd test2
```

2. تثبيت التبعيات
```bash
flutter pub get
```

3. تشغيل التطبيق
```bash
flutter run
```

## 📱 المنصات المدعومة
- ✅ Android
- ✅ iOS
- ✅ Web
- ✅ Windows
- ✅ macOS
- ✅ Linux

## 🔮 الميزات المستقبلية
- [ ] نظام دفع متكامل
- [ ] تتبع GPS للطلبات
- [ ] دردشة مع خدمة العملاء
- [ ] نظام نقاط الولاء
- [ ] تطبيق للمطاعم والمتاجر
- [ ] لوحة تحكم إدارية

## 🤝 المساهمة
نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال طلبات السحب.

## 📄 الترخيص
هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## 📞 التواصل
- البريد الإلكتروني: <EMAIL>
- الهاتف: +967 777 123 456
- واتساب: +967 777 123 456

---
**طعم الأصالة في كل وجبة** 🇾🇪
