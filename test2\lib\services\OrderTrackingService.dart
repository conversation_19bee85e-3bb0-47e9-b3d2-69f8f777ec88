import 'dart:async';
import 'dart:math';
import 'package:geolocator/geolocator.dart';
import 'package:test2/services/LocationService.dart';
import 'package:test2/models/OrderTrackingData.dart';

/// خدمة تتبع الطلبات - تدير موقع المستخدم وموقع السائق والمسار
class OrderTrackingService {
  // إنشاء مثيل واحد من الخدمة (Singleton Pattern)
  static final OrderTrackingService _instance = OrderTrackingService._internal();
  factory OrderTrackingService() => _instance;
  OrderTrackingService._internal();

  final LocationService _locationService = LocationService();
  
  // متغيرات التتبع
  StreamController<OrderTrackingData>? _trackingController;
  Timer? _trackingTimer;
  Position? _userPosition;
  Position? _driverPosition;
  String? _currentOrderId;
  
  // إعدادات المحاكاة
  final double _driverSpeed = 0.0005; // سرعة السائق (درجات لكل ثانية)
  final int _updateInterval = 3; // تحديث كل 3 ثواني

  /// بدء تتبع طلب معين
  Future<Stream<OrderTrackingData>?> startTracking(String orderId) async {
    try {
      print('بدء تتبع الطلب: $orderId');
      
      // التحقق من تفعيل GPS
      if (!await _checkGPSEnabled()) {
        throw Exception('GPS غير مفعل. يرجى تفعيل GPS أولاً.');
      }

      // إيقاف التتبع السابق إن وجد
      await stopTracking();
      
      _currentOrderId = orderId;
      _trackingController = StreamController<OrderTrackingData>.broadcast();
      
      // الحصول على موقع المستخدم
      await _getUserLocation();
      
      // تهيئة موقع السائق (محاكاة)
      _initializeDriverLocation();
      
      // بدء التحديث الدوري
      _startPeriodicUpdates();
      
      print('تم بدء تتبع الطلب بنجاح');
      return _trackingController!.stream;
      
    } catch (e) {
      print('خطأ في بدء تتبع الطلب: $e');
      return null;
    }
  }

  /// إيقاف تتبع الطلب
  Future<void> stopTracking() async {
    try {
      print('إيقاف تتبع الطلب...');
      
      _trackingTimer?.cancel();
      _trackingTimer = null;
      
      await _trackingController?.close();
      _trackingController = null;
      
      _currentOrderId = null;
      _userPosition = null;
      _driverPosition = null;
      
      print('تم إيقاف تتبع الطلب');
    } catch (e) {
      print('خطأ في إيقاف تتبع الطلب: $e');
    }
  }

  /// التحقق من تفعيل GPS
  Future<bool> _checkGPSEnabled() async {
    try {
      Map<String, bool> status = await _locationService.checkLocationStatus();
      return status['fullyEnabled'] ?? false;
    } catch (e) {
      print('خطأ في فحص GPS: $e');
      return false;
    }
  }

  /// الحصول على موقع المستخدم
  Future<void> _getUserLocation() async {
    try {
      Position? position = await _locationService.getCurrentLocation();
      if (position != null) {
        _userPosition = position;
        print('تم الحصول على موقع المستخدم: ${position.latitude}, ${position.longitude}');
      } else {
        throw Exception('لم يتم العثور على موقع المستخدم');
      }
    } catch (e) {
      print('خطأ في الحصول على موقع المستخدم: $e');
      rethrow;
    }
  }

  /// تهيئة موقع السائق (محاكاة)
  void _initializeDriverLocation() {
    if (_userPosition == null) return;
    
    // إنشاء موقع السائق على بعد عشوائي من المستخدم
    Random random = Random();
    double offsetLat = (random.nextDouble() - 0.5) * 0.02; // ±0.01 درجة
    double offsetLng = (random.nextDouble() - 0.5) * 0.02;
    
    _driverPosition = Position(
      latitude: _userPosition!.latitude + offsetLat,
      longitude: _userPosition!.longitude + offsetLng,
      timestamp: DateTime.now(),
      accuracy: 10.0,
      altitude: 0.0,
      heading: 0.0,
      speed: 0.0,
      speedAccuracy: 0.0,
      altitudeAccuracy: 0.0,
      headingAccuracy: 0.0,
    );
    
    print('تم تهيئة موقع السائق: ${_driverPosition!.latitude}, ${_driverPosition!.longitude}');
  }

  /// بدء التحديث الدوري
  void _startPeriodicUpdates() {
    _trackingTimer = Timer.periodic(Duration(seconds: _updateInterval), (timer) {
      _updateTracking();
    });
    
    // إرسال التحديث الأول فوراً
    _updateTracking();
  }

  /// تحديث بيانات التتبع
  void _updateTracking() {
    try {
      if (_userPosition == null || _driverPosition == null || _trackingController == null) {
        return;
      }

      // تحديث موقع السائق (محاكاة الحركة نحو المستخدم)
      _updateDriverPosition();
      
      // حساب المسافة والوقت المتوقع
      double distance = Geolocator.distanceBetween(
        _driverPosition!.latitude,
        _driverPosition!.longitude,
        _userPosition!.latitude,
        _userPosition!.longitude,
      );
      
      // حساب الوقت المتوقع (بناءً على السرعة المتوسطة 30 كم/ساعة)
      int estimatedTime = (distance / 8.33).round(); // 8.33 متر/ثانية = 30 كم/ساعة
      
      // إنشاء بيانات التتبع
      OrderTrackingData trackingData = OrderTrackingData(
        orderId: _currentOrderId!,
        userPosition: _userPosition!,
        driverPosition: _driverPosition!,
        distance: distance,
        estimatedTime: estimatedTime,
        status: _getOrderStatus(distance),
        lastUpdate: DateTime.now(),
      );
      
      // إرسال البيانات
      _trackingController!.add(trackingData);
      
      // إيقاف التتبع عند وصول السائق
      if (distance < 50) { // أقل من 50 متر
        print('وصل السائق إلى الوجهة');
        Future.delayed(Duration(seconds: 2), () => stopTracking());
      }
      
    } catch (e) {
      print('خطأ في تحديث التتبع: $e');
    }
  }

  /// تحديث موقع السائق (محاكاة الحركة)
  void _updateDriverPosition() {
    if (_userPosition == null || _driverPosition == null) return;
    
    // حساب الاتجاه نحو المستخدم
    double latDiff = _userPosition!.latitude - _driverPosition!.latitude;
    double lngDiff = _userPosition!.longitude - _driverPosition!.longitude;
    
    // تطبيع الاتجاه
    double distance = sqrt(latDiff * latDiff + lngDiff * lngDiff);
    if (distance > 0) {
      double normalizedLat = latDiff / distance;
      double normalizedLng = lngDiff / distance;
      
      // تحريك السائق نحو المستخدم
      double newLat = _driverPosition!.latitude + (normalizedLat * _driverSpeed);
      double newLng = _driverPosition!.longitude + (normalizedLng * _driverSpeed);
      
      _driverPosition = Position(
        latitude: newLat,
        longitude: newLng,
        timestamp: DateTime.now(),
        accuracy: 10.0,
        altitude: 0.0,
        heading: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
        altitudeAccuracy: 0.0,
        headingAccuracy: 0.0,
      );
    }
  }

  /// تحديد حالة الطلب بناءً على المسافة
  String _getOrderStatus(double distance) {
    if (distance < 50) {
      return 'وصل السائق';
    } else if (distance < 500) {
      return 'السائق قريب جداً';
    } else if (distance < 1000) {
      return 'السائق في الطريق';
    } else if (distance < 2000) {
      return 'السائق قادم';
    } else {
      return 'تم تأكيد الطلب';
    }
  }

  /// الحصول على الحالة الحالية للتتبع
  bool get isTracking => _trackingController != null && !_trackingController!.isClosed;
  
  /// الحصول على معرف الطلب الحالي
  String? get currentOrderId => _currentOrderId;
  
  /// تنظيف الموارد
  void dispose() {
    stopTracking();
  }
}
