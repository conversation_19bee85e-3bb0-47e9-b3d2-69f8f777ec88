import 'package:flutter/material.dart';
import '../services/SearchService.dart';
import '../models/Product.dart';
import '../models/Store.dart';

/// مزود حالة البحث المتقدم
class SearchProvider extends ChangeNotifier {
  final SearchService _searchService = SearchService();

  // حالة البحث
  bool _isLoading = false;
  bool _isSearching = false;
  String _currentQuery = '';
  String? _error;

  // النتائج
  List<Product> _productResults = [];
  List<Store> _storeResults = [];
  List<String> _suggestions = [];

  // الفلاتر والترتيب
  FilterOptions _currentFilters = FilterOptions();
  SortOption _currentSort = SortOption.popular;

  // التاريخ والاقتراحات
  List<String> _searchHistory = [];
  List<String> _popularSearches = [];
  List<String> _recentSearches = [];

  // Getters
  bool get isLoading => _isLoading;
  bool get isSearching => _isSearching;
  String get currentQuery => _currentQuery;
  String? get error => _error;
  
  List<Product> get productResults => List.unmodifiable(_productResults);
  List<Store> get storeResults => List.unmodifiable(_storeResults);
  List<String> get suggestions => List.unmodifiable(_suggestions);
  
  FilterOptions get currentFilters => _currentFilters;
  SortOption get currentSort => _currentSort;
  
  List<String> get searchHistory => List.unmodifiable(_searchHistory);
  List<String> get popularSearches => List.unmodifiable(_popularSearches);
  List<String> get recentSearches => List.unmodifiable(_recentSearches);

  bool get hasResults => _productResults.isNotEmpty || _storeResults.isNotEmpty;
  int get totalResults => _productResults.length + _storeResults.length;

  /// تهيئة البحث
  Future<void> initializeSearch() async {
    try {
      await _searchService.loadSearchHistory();
      _updateHistoryData();
    } catch (e) {
      _setError('فشل في تحميل تاريخ البحث');
    }
  }

  /// بدء البحث
  Future<void> startSearch(String query) async {
    if (query.trim().isEmpty) {
      clearSearch();
      return;
    }

    _setLoading(true);
    _setError(null);
    _currentQuery = query.trim();
    _isSearching = true;

    try {
      // البحث في المنتجات والمتاجر بشكل متوازي
      final results = await Future.wait([
        _searchService.searchProducts(
          query, 
          filters: _currentFilters,
          sortOption: _currentSort,
        ),
        _searchService.searchStores(query),
      ]);

      _productResults = results[0] as List<Product>;
      _storeResults = results[1] as List<Store>;

      // تحديث التاريخ
      _updateHistoryData();

    } catch (e) {
      _setError('حدث خطأ أثناء البحث: ${e.toString()}');
      _productResults = [];
      _storeResults = [];
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث الاقتراحات
  void updateSuggestions(String query) {
    _suggestions = _searchService.getSearchSuggestions(query);
    notifyListeners();
  }

  /// تطبيق الفلاتر
  Future<void> applyFilters(FilterOptions filters) async {
    _currentFilters = filters;
    
    if (_isSearching && _currentQuery.isNotEmpty) {
      await startSearch(_currentQuery);
    } else {
      notifyListeners();
    }
  }

  /// تطبيق الترتيب
  Future<void> applySorting(SortOption sortOption) async {
    _currentSort = sortOption;
    
    if (_isSearching && _currentQuery.isNotEmpty) {
      await startSearch(_currentQuery);
    } else {
      notifyListeners();
    }
  }

  /// مسح البحث
  void clearSearch() {
    _isSearching = false;
    _currentQuery = '';
    _productResults = [];
    _storeResults = [];
    _suggestions = [];
    _setError(null);
    notifyListeners();
  }

  /// مسح تاريخ البحث
  Future<void> clearSearchHistory() async {
    try {
      await _searchService.clearSearchHistory();
      _updateHistoryData();
    } catch (e) {
      _setError('فشل في مسح تاريخ البحث');
    }
  }

  /// حذف بحث محدد من التاريخ
  Future<void> removeFromHistory(String query) async {
    try {
      await _searchService.removeSearch(query);
      _updateHistoryData();
    } catch (e) {
      _setError('فشل في حذف البحث');
    }
  }

  /// البحث السريع من التاريخ
  Future<void> searchFromHistory(String query) async {
    await startSearch(query);
  }

  /// الحصول على الفئات المتاحة للفلترة
  List<String> getAvailableCategories() {
    return [
      'برجر',
      'بيتزا',
      'شاورما',
      'مشروبات',
      'حلويات',
      'سلطات',
      'مأكولات بحرية',
      'دجاج',
      'لحوم',
      'مقبلات',
      'وجبات سريعة',
      'إيطالي',
      'عربي',
      'آسيوي',
    ];
  }

  /// الحصول على نطاقات الأسعار للفلترة
  List<PriceRange> getPriceRanges() {
    return [
      PriceRange(label: 'أقل من 20 ريال', min: 0, max: 20),
      PriceRange(label: '20 - 50 ريال', min: 20, max: 50),
      PriceRange(label: '50 - 100 ريال', min: 50, max: 100),
      PriceRange(label: 'أكثر من 100 ريال', min: 100, max: null),
    ];
  }

  /// الحصول على خيارات التقييم للفلترة
  List<RatingOption> getRatingOptions() {
    return [
      RatingOption(label: '4 نجوم فأكثر', rating: 4.0),
      RatingOption(label: '3 نجوم فأكثر', rating: 3.0),
      RatingOption(label: '2 نجوم فأكثر', rating: 2.0),
      RatingOption(label: 'جميع التقييمات', rating: 0.0),
    ];
  }

  /// الحصول على خيارات الترتيب
  List<SortOptionData> getSortOptions() {
    return [
      SortOptionData(
        option: SortOption.popular,
        label: 'الأكثر شعبية',
        icon: Icons.trending_up,
      ),
      SortOptionData(
        option: SortOption.ratingDesc,
        label: 'الأعلى تقييماً',
        icon: Icons.star,
      ),
      SortOptionData(
        option: SortOption.priceAsc,
        label: 'السعر: من الأقل للأعلى',
        icon: Icons.arrow_upward,
      ),
      SortOptionData(
        option: SortOption.priceDesc,
        label: 'السعر: من الأعلى للأقل',
        icon: Icons.arrow_downward,
      ),
      SortOptionData(
        option: SortOption.newest,
        label: 'الأحدث',
        icon: Icons.new_releases,
      ),
      SortOptionData(
        option: SortOption.nameAsc,
        label: 'الاسم أبجدياً',
        icon: Icons.sort_by_alpha,
      ),
    ];
  }

  /// إعادة تعيين الفلاتر
  void resetFilters() {
    _currentFilters = FilterOptions();
    notifyListeners();
  }

  /// تحديث بيانات التاريخ
  void _updateHistoryData() {
    _searchHistory = _searchService.searchHistory;
    _popularSearches = _searchService.popularSearches;
    _recentSearches = _searchService.recentSearches;
    notifyListeners();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// تعيين رسالة الخطأ
  void _setError(String? error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _setError(null);
  }

  @override
  void dispose() {
    super.dispose();
  }
}

/// نطاق السعر للفلترة
class PriceRange {
  final String label;
  final double min;
  final double? max;

  PriceRange({
    required this.label,
    required this.min,
    this.max,
  });
}

/// خيار التقييم للفلترة
class RatingOption {
  final String label;
  final double rating;

  RatingOption({
    required this.label,
    required this.rating,
  });
}

/// بيانات خيار الترتيب
class SortOptionData {
  final SortOption option;
  final String label;
  final IconData icon;

  SortOptionData({
    required this.option,
    required this.label,
    required this.icon,
  });
}
