import 'package:flutter/material.dart';
import '../models/Store.dart';
import '../pages/StoreDetailsPage.dart';

/// بطاقة المتجر للبحث
class StoreCard extends StatelessWidget {
  final Store store;
  final VoidCallback? onTap;

  const StoreCard({
    Key? key,
    required this.store,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap ?? () => _navigateToStore(context),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // صورة المتجر
                  _buildStoreImage(),
                  
                  SizedBox(width: 12),
                  
                  // معلومات المتجر
                  Expanded(
                    child: _buildStoreInfo(),
                  ),
                  
                  // حالة المتجر
                  _buildStoreStatus(),
                ],
              ),
              
              SizedBox(height: 12),
              
              // معلومات إضافية
              _buildAdditionalInfo(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStoreImage() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.asset(
          store.image,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: Colors.grey[200],
              child: Icon(
                Icons.store,
                color: Colors.grey[400],
                size: 24,
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStoreInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // اسم المتجر
        Row(
          children: [
            Expanded(
              child: Text(
                store.name,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (store.isVerified)
              Icon(
                Icons.verified,
                color: Colors.blue,
                size: 16,
              ),
          ],
        ),
        
        SizedBox(height: 4),
        
        // وصف المتجر
        Text(
          store.description,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        
        SizedBox(height: 6),
        
        // التقييم والفئة
        Row(
          children: [
            // التقييم
            Row(
              children: [
                Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: 14,
                ),
                SizedBox(width: 2),
                Text(
                  store.rating.toStringAsFixed(1),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
                if (store.reviewCount > 0) ...[
                  SizedBox(width: 4),
                  Text(
                    '(${store.reviewCount})',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ],
            ),
            
            SizedBox(width: 12),
            
            // الفئة
            Container(
              padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Color(0xFF4C53A5).withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                store.category,
                style: TextStyle(
                  fontSize: 10,
                  color: Color(0xFF4C53A5),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStoreStatus() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: store.isOpen ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: store.isOpen ? Colors.green : Colors.red,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 4),
          Text(
            store.statusText,
            style: TextStyle(
              fontSize: 10,
              color: store.isOpen ? Colors.green[700] : Colors.red[700],
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo() {
    return Row(
      children: [
        // رسوم التوصيل
        if (store.deliveryFee != null)
          _buildInfoChip(
            icon: Icons.delivery_dining,
            text: store.deliveryFeeText,
            color: store.deliveryFee == 0 ? Colors.green : Colors.orange,
          ),
        
        SizedBox(width: 8),
        
        // وقت التوصيل
        if (store.deliveryTime != null)
          _buildInfoChip(
            icon: Icons.access_time,
            text: store.deliveryTimeText,
            color: Colors.blue,
          ),
        
        SizedBox(width: 8),
        
        // الحد الأدنى للطلب
        if (store.minimumOrder != null)
          _buildInfoChip(
            icon: Icons.shopping_cart,
            text: store.minimumOrderText,
            color: Colors.purple,
          ),
        
        Spacer(),
        
        // أيقونة الانتقال
        Icon(
          Icons.arrow_forward_ios,
          color: Colors.grey[400],
          size: 14,
        ),
      ],
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: 10,
          ),
          SizedBox(width: 2),
          Text(
            text,
            style: TextStyle(
              fontSize: 9,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToStore(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoreDetailsPage(store: store.id),
      ),
    );
  }
}
