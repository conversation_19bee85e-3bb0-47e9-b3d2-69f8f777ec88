import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/Order.dart';
import '../models/CartItem.dart';
import '../models/Address.dart';
import '../models/UserProfile.dart';
import '../services/OrderService.dart';

/// مزود حالة الطلبات
class OrderProvider extends ChangeNotifier {
  List<Order> _orders = [];
  bool _isLoading = false;
  String? _error;
  Timer? _refreshTimer;

  /// الحصول على قائمة الطلبات
  List<Order> get orders => _orders;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get error => _error;

  /// الحصول على الطلبات النشطة (غير المكتملة)
  List<Order> get activeOrders {
    return _orders.where((order) => 
      order.status != OrderStatus.delivered && 
      order.status != OrderStatus.cancelled
    ).toList();
  }

  /// الحصول على الطلبات المكتملة
  List<Order> get completedOrders {
    return _orders.where((order) => 
      order.status == OrderStatus.delivered || 
      order.status == OrderStatus.cancelled
    ).toList();
  }

  /// الحصول على عدد الطلبات النشطة
  int get activeOrdersCount => activeOrders.length;

  /// إنشاء طلب جديد
  Future<Order?> createOrder({
    required UserProfile userProfile,
    required Address deliveryAddress,
    required List<CartItem> items,
    required String paymentMethod,
    required String deliveryTime,
    DateTime? scheduledTime,
    String? notes,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final order = await OrderService.createOrder(
        userProfile: userProfile,
        deliveryAddress: deliveryAddress,
        items: items,
        paymentMethod: paymentMethod,
        deliveryTime: deliveryTime,
        scheduledTime: scheduledTime,
        notes: notes,
      );

      // إضافة الطلب للقائمة
      _orders.insert(0, order);
      notifyListeners();

      // بدء تحديث دوري للطلبات
      _startPeriodicRefresh();

      return order;
    } catch (e) {
      _setError('فشل في إنشاء الطلب: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل طلبات المستخدم
  Future<void> loadUserOrders(String userId) async {
    _setLoading(true);
    _setError(null);

    try {
      _orders = await OrderService.getUserOrders(userId);
      notifyListeners();

      // بدء تحديث دوري إذا كان هناك طلبات نشطة
      if (activeOrders.isNotEmpty) {
        _startPeriodicRefresh();
      }
    } catch (e) {
      _setError('فشل في تحميل الطلبات: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث طلب محدد
  Future<bool> refreshOrder(String orderId) async {
    try {
      final updatedOrder = await OrderService.getOrderById(orderId);
      if (updatedOrder != null) {
        final index = _orders.indexWhere((order) => order.id == orderId);
        if (index != -1) {
          _orders[index] = updatedOrder;
          notifyListeners();
          return true;
        }
      }
      return false;
    } catch (e) {
      print('خطأ في تحديث الطلب: $e');
      return false;
    }
  }

  /// تحديث جميع الطلبات النشطة
  Future<void> refreshActiveOrders() async {
    try {
      bool hasUpdates = false;
      
      for (final order in activeOrders) {
        final updated = await refreshOrder(order.id);
        if (updated) hasUpdates = true;
      }

      if (hasUpdates) {
        // إيقاف التحديث الدوري إذا لم تعد هناك طلبات نشطة
        if (activeOrders.isEmpty) {
          _stopPeriodicRefresh();
        }
      }
    } catch (e) {
      print('خطأ في تحديث الطلبات النشطة: $e');
    }
  }

  /// إلغاء طلب
  Future<bool> cancelOrder(String orderId, String reason) async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await OrderService.cancelOrder(orderId, reason);
      
      if (success) {
        await refreshOrder(orderId);
        return true;
      } else {
        _setError('فشل في إلغاء الطلب');
        return false;
      }
    } catch (e) {
      _setError('خطأ في إلغاء الطلب: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على طلب بالمعرف
  Order? getOrderById(String orderId) {
    try {
      return _orders.firstWhere((order) => order.id == orderId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على آخر طلب
  Order? get latestOrder {
    return _orders.isNotEmpty ? _orders.first : null;
  }

  /// التحقق من وجود طلبات نشطة
  bool get hasActiveOrders => activeOrders.isNotEmpty;

  /// بدء التحديث الدوري
  void _startPeriodicRefresh() {
    _stopPeriodicRefresh(); // إيقاف أي تحديث سابق
    
    _refreshTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      refreshActiveOrders();
    });
  }

  /// إيقاف التحديث الدوري
  void _stopPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// مسح جميع الطلبات (للتطوير)
  Future<void> clearAllOrders() async {
    try {
      await OrderService.clearAllOrders();
      _orders.clear();
      _stopPeriodicRefresh();
      notifyListeners();
    } catch (e) {
      _setError('فشل في مسح الطلبات: $e');
    }
  }

  /// إحصائيات الطلبات
  Map<String, int> get orderStats {
    final stats = <String, int>{};
    
    for (final status in OrderStatus.values) {
      stats[status.toString().split('.').last] = 
          _orders.where((order) => order.status == status).length;
    }
    
    return stats;
  }

  /// الحصول على الطلبات حسب الحالة
  List<Order> getOrdersByStatus(OrderStatus status) {
    return _orders.where((order) => order.status == status).toList();
  }

  /// البحث في الطلبات
  List<Order> searchOrders(String query) {
    if (query.isEmpty) return _orders;
    
    final lowerQuery = query.toLowerCase();
    return _orders.where((order) {
      return order.id.toLowerCase().contains(lowerQuery) ||
             order.storeName?.toLowerCase().contains(lowerQuery) == true ||
             order.items.any((item) => 
                 item.name.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  @override
  void dispose() {
    _stopPeriodicRefresh();
    super.dispose();
  }
}
