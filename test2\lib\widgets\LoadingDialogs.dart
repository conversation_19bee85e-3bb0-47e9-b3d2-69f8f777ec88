import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:test2/utils/AppColors.dart';

/// مجموعة حوارات التحميل والرسائل الجميلة
class LoadingDialogs {
  
  /// عرض حوار تحميل جميل مع رسالة مخصصة
  static void showLoadingDialog(
    BuildContext context, {
    required String message,
    String? subtitle,
    IconData? icon,
    Color? iconColor,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => LoadingDialog(
        message: message,
        subtitle: subtitle,
        icon: icon,
        iconColor: iconColor,
      ),
    );
  }

  /// عرض حوار تحميل لفحص أذونات الموقع
  static void showLocationPermissionLoading(BuildContext context) {
    showLoadingDialog(
      context,
      message: 'جاري فحص أذونات الموقع',
      subtitle: 'يرجى الانتظار...',
      icon: Icons.location_searching,
      iconColor: AppColors.primaryColor,
    );
  }

  /// عرض حوار تحميل لتهيئة نظام التتبع
  static void showTrackingInitLoading(BuildContext context) {
    showLoadingDialog(
      context,
      message: 'جاري تهيئة نظام التتبع',
      subtitle: 'تحضير الخريطة والموقع...',
      icon: Icons.map,
      iconColor: AppColors.primaryColor,
    );
  }

  /// عرض حوار تحميل للحصول على الموقع
  static void showLocationLoading(BuildContext context) {
    showLoadingDialog(
      context,
      message: 'جاري تحديد موقعك',
      subtitle: 'البحث عن أفضل إشارة GPS...',
      icon: Icons.gps_fixed,
      iconColor: Colors.blue,
    );
  }

  /// عرض حوار تحميل لتطبيق الإعدادات
  static void showSettingsLoading(BuildContext context, bool enabling) {
    showLoadingDialog(
      context,
      message: enabling ? 'جاري تفعيل خدمة الموقع' : 'جاري إلغاء تفعيل خدمة الموقع',
      subtitle: 'تطبيق الإعدادات الجديدة...',
      icon: enabling ? Icons.location_on : Icons.location_off,
      iconColor: enabling ? Colors.green : Colors.orange,
    );
  }

  /// عرض حوار تحميل لاختبار GPS
  static void showGPSTestLoading(BuildContext context) {
    showLoadingDialog(
      context,
      message: 'جاري اختبار خدمة الموقع',
      subtitle: 'فحص GPS والحصول على الإحداثيات...',
      icon: Icons.speed,
      iconColor: Colors.purple,
    );
  }

  /// إغلاق حوار التحميل
  static void hideLoadingDialog(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.of(context).pop();
    }
  }
}

/// حوار التحميل الجميل
class LoadingDialog extends StatefulWidget {
  final String message;
  final String? subtitle;
  final IconData? icon;
  final Color? iconColor;

  const LoadingDialog({
    Key? key,
    required this.message,
    this.subtitle,
    this.icon,
    this.iconColor,
  }) : super(key: key);

  @override
  _LoadingDialogState createState() => _LoadingDialogState();
}

class _LoadingDialogState extends State<LoadingDialog>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    // تحريك دوران الأيقونة
    _rotationController = AnimationController(
      duration: Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    // تحريك تكبير وتصغير
    _scaleController = AnimationController(
      duration: Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(reverse: true);
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: AppColors.whiteColor,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              spreadRadius: 5,
              offset: Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // الأيقونة المتحركة
            AnimatedBuilder(
              animation: _rotationAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _rotationAnimation.value * 2 * 3.14159,
                  child: AnimatedBuilder(
                    animation: _scaleAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _scaleAnimation.value,
                        child: Container(
                          width: 80.w,
                          height: 80.h,
                          decoration: BoxDecoration(
                            color: (widget.iconColor ?? AppColors.primaryColor).withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            widget.icon ?? Icons.refresh,
                            size: 40.sp,
                            color: widget.iconColor ?? AppColors.primaryColor,
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
            
            SizedBox(height: 24.h),
            
            // الرسالة الرئيسية
            Text(
              widget.message,
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            
            if (widget.subtitle != null) ...[
              SizedBox(height: 8.h),
              Text(
                widget.subtitle!,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            
            SizedBox(height: 24.h),
            
            // مؤشر التحميل الخطي
            Container(
              width: double.infinity,
              height: 4.h,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(2.r),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(2.r),
                child: LinearProgressIndicator(
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    widget.iconColor ?? AppColors.primaryColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
