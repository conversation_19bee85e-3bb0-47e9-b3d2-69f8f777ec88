import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PerformanceOptimizer {
  // تحسين الذاكرة
  static void optimizeMemory() {
    // تنظيف الذاكرة المؤقتة للصور
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
    
    // تقليل حجم الذاكرة المؤقتة للصور
    PaintingBinding.instance.imageCache.maximumSize = 50;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 << 20; // 50 MB
  }

  // تحسين الرسوم المتحركة
  static void optimizeAnimations() {
    // تقليل معدل الإطارات للأجهزة الضعيفة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // يمكن إضافة تحسينات إضافية هنا
    });
  }

  // تحسين الشبكة
  static void optimizeNetwork() {
    // تحسين إعدادات الشبكة
    // يمكن إضافة تحسينات للشبكة هنا
  }

  // تحسين عام للتطبيق
  static void optimizeApp() {
    optimizeMemory();
    optimizeAnimations();
    optimizeNetwork();
    
    // تحسين الاستجابة
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }

  // تنظيف الموارد
  static void cleanup() {
    PaintingBinding.instance.imageCache.clear();
  }
}
