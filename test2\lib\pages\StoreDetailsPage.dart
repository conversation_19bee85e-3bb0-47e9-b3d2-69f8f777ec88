import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:test2/pages/StoresPage.dart';
import 'package:test2/providers/CartProvider.dart';
import 'package:test2/models/Product.dart';
import 'package:test2/widgets/CustomSnackBars.dart';
import 'package:test2/pages/CartPage.dart';
import 'package:badges/badges.dart' as badges;

class StoreDetailsPage extends StatelessWidget {
  final Map<String, dynamic> store;

  StoreDetailsPage({required this.store});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(context),
          SliverToBoxAdapter(
            child: _buildStoreInfo(),
          ),
          SliverToBoxAdapter(
            child: _buildCategoriesTab(),
          ),
          SliverPadding(
            padding: EdgeInsets.all(15),
            sliver: SliverGrid(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.7,
                mainAxisSpacing: 15,
                crossAxisSpacing: 15,
              ),
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  // إنشاء بيانات المنتج
                  final productData = {
                    'id': '${store["id"]}_${index + 1}',
                    'name': '${store["name"]} - منتج ${index + 1}',
                    'description': 'منتج عالي الجودة من ${store["name"]}',
                    'price': (index + 1) * 15.0,
                    'imageUrl': 'images/${index + 1}.png',
                    'category': 'منتجات ${store["name"]}',
                    'rating': 4.5,
                    'reviewCount': 10,
                    'isAvailable': true,
                    'isFeatured': false,
                  };

                  return Consumer<CartProvider>(
                    builder: (context, cartProvider, child) {
                      final productId = productData['id'].toString();
                      final isInCart = cartProvider.isProductInCart(productId);
                      final quantity =
                          cartProvider.getProductQuantity(productId);

                      return Container(
                        padding: EdgeInsets.only(left: 12, right: 12, top: 8),
                        margin:
                            EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                        decoration: BoxDecoration(
                          color: Color(0xFFEDECF2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          textDirection: TextDirection.rtl,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Center(
                                child: Image.asset(
                                  "images/${index + 1}.png",
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                            Text(
                              productData['name'].toString(),
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF4C53A5),
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: 3),
                            Text(
                              productData['description'].toString(),
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: 6),
                            Row(
                              textDirection: TextDirection.rtl,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "${productData['price']} ريال",
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF4C53A5),
                                  ),
                                ),
                                GestureDetector(
                                  onTap: () => _addToCart(
                                      context, productData, cartProvider),
                                  child: Container(
                                    padding: EdgeInsets.all(6),
                                    decoration: BoxDecoration(
                                      color: isInCart
                                          ? Colors.green
                                          : Color(0xFF4C53A5),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          isInCart
                                              ? Icons.check
                                              : Icons.add_circle,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                        if (isInCart && quantity > 0) ...[
                                          SizedBox(width: 4),
                                          Text(
                                            quantity.toString(),
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 8),
                          ],
                        ),
                      );
                    },
                  );
                },
                childCount: 6,
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: Consumer<CartProvider>(
        builder: (context, cartProvider, child) {
          return badges.Badge(
            showBadge: cartProvider.totalQuantity > 0,
            badgeContent: Text(
              cartProvider.totalQuantity > 9
                  ? '9+'
                  : cartProvider.totalQuantity.toString(),
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
            child: FloatingActionButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => CartPage()),
                );
              },
              backgroundColor: Color(0xFFC3243B),
              child: Icon(
                Icons.shopping_cart,
                color: Color.fromARGB(255, 245, 170, 73),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSliverAppBar(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: Color(0xFFC3243B),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          store["name"],
          style: TextStyle(color: Colors.white),
        ),
        background: Image.asset(
          store["image"],
          fit: BoxFit.cover,
        ),
      ),
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => StoresPage(),
            ),
          );
        },
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.favorite_border, color: Colors.white),
          onPressed: () {},
        ),
        IconButton(
          icon: Icon(Icons.search, color: Colors.white),
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildStoreInfo() {
    return Container(
      padding: EdgeInsets.all(15),
      color: Colors.white,
      child: Column(
        textDirection: TextDirection.rtl,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            textDirection: TextDirection.rtl,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                textDirection: TextDirection.rtl,
                children: [
                  Icon(Icons.star, color: Colors.amber),
                  SizedBox(width: 5),
                  Text(
                    "${store["rating"]}",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(width: 15),
                  Icon(Icons.access_time, color: Colors.grey),
                  SizedBox(width: 5),
                  Text(
                    store["deliveryTime"],
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  "مفتوح",
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 15),
          Text(
            "عن المتجر",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 5),
          Text(
            textAlign: TextAlign.right,
            "هذا المتجر يقدم مجموعة متنوعة من المنتجات عالية الجودة بأسعار مناسبة. نحن نسعى دائمًا لتقديم أفضل خدمة للعملاء وضمان رضاهم.",
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 15),
          Row(
            textDirection: TextDirection.rtl,
            children: [
              Icon(Icons.location_on, color: Color(0xFF4C53A5)),
              SizedBox(width: 5),
              Text(
                "شارع الملك فهد، حي الورود، الرياض",
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesTab() {
    return Container(
      height: 50,
      margin: EdgeInsets.only(top: 15),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 15),
        children: [
          _buildCategoryTab("الكل", true),
          _buildCategoryTab("الأكثر مبيعًا", false),
          _buildCategoryTab("العروض", false),
          _buildCategoryTab("الجديد", false),
          _buildCategoryTab("الوجبات", false),
          _buildCategoryTab("المشروبات", false),
        ],
      ),
    );
  }

  Widget _buildCategoryTab(String title, bool isActive) {
    return Container(
      margin: EdgeInsets.only(right: 10),
      padding: EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: isActive ? Color(0xFF4C53A5) : Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isActive ? Color(0xFF4C53A5) : Colors.grey,
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          title,
          style: TextStyle(
            color: isActive ? Colors.white : Colors.grey[700],
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// إضافة المنتج إلى السلة
  void _addToCart(BuildContext context, Map<String, dynamic> productData,
      CartProvider cartProvider) {
    try {
      final product = Product.fromMap(productData);

      cartProvider.addToCart(product).then((success) {
        if (success) {
          CustomSnackBars.showSuccess(
            context,
            message: 'تم إضافة ${product.name} إلى السلة',
            subtitle: 'يمكنك مراجعة سلتك من الأسفل',
          );
        } else {
          CustomSnackBars.showError(
            context,
            message: 'فشل في إضافة المنتج',
            subtitle: 'يرجى المحاولة مرة أخرى',
          );
        }
      });
    } catch (e) {
      CustomSnackBars.showError(
        context,
        message: 'خطأ في إضافة المنتج',
        subtitle: e.toString(),
      );
    }
  }
}
