import 'package:flutter/material.dart';
import 'package:test2/pages/StoresPage.dart';

class StoreDetailsPage extends StatelessWidget {
  final Map<String, dynamic> store;
  
  StoreDetailsPage({required this.store});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(context),
          SliverToBoxAdapter(
            child: _buildStoreInfo(),
          ),
          SliverToBoxAdapter(
            child: _buildCategoriesTab(),
          ),
          SliverPadding(
            padding: EdgeInsets.all(15),
            sliver: SliverGrid(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.7,
                mainAxisSpacing: 15,
                crossAxisSpacing: 15,
              ),
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  return Container(
                    padding: EdgeInsets.only(left: 15 , right: 15 , top: 10),
                    margin: EdgeInsets.symmetric(vertical: 5 , horizontal: 8),
                    decoration: BoxDecoration(
                      color: Color(0xFFEDECF2),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Column(
                      textDirection: TextDirection.rtl,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Center(
                            child: Image.asset("images/${index + 1}.png"),
                          ),
                        ),
                        Text(
                          "منتج ${index + 1}",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF4C53A5),
                          ),
                        ),
                        SizedBox(height: 5),
                        Text(
                          "وصف المنتج هنا",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        SizedBox(height: 5),
                        Row(
                          textDirection: TextDirection.rtl,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "\$${(index + 1) * 10}",
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF4C53A5),
                              ),
                            ),
                            Icon(
                              Icons.add_circle,
                              color: Color(0xFF4C53A5),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
                childCount: 6,
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {},
        backgroundColor: Color(0xFFC3243B),
        child: Icon(Icons.shopping_cart , color:Color.fromARGB(255, 245, 170, 73),),
      ),
    );
  }

  Widget _buildSliverAppBar(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: Color(0xFFC3243B),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          store["name"],
          style: TextStyle(color: Colors.white),
        ),
        background: Image.asset(
          store["image"],
          fit: BoxFit.cover,
        ),
      ),
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => StoresPage(),
              ),
            );
        },
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.favorite_border, color: Colors.white),
          onPressed: () {},
        ),
        IconButton(
          icon: Icon(Icons.search, color: Colors.white),
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildStoreInfo() {
    return Container(
      padding: EdgeInsets.all(15),
      color: Colors.white,
      child: Column(
        textDirection: TextDirection.rtl,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            textDirection: TextDirection.rtl,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                textDirection: TextDirection.rtl,
                children: [
                  Icon(Icons.star, color: Colors.amber),
                  SizedBox(width: 5),
                  Text(
                    "${store["rating"]}",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(width: 15),
                  Icon(Icons.access_time, color: Colors.grey),
                  SizedBox(width: 5),
                  Text(
                    store["deliveryTime"],
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  "مفتوح",
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 15),
          Text(
            "عن المتجر",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 5),
          Text(
            textAlign: TextAlign.right,
            "هذا المتجر يقدم مجموعة متنوعة من المنتجات عالية الجودة بأسعار مناسبة. نحن نسعى دائمًا لتقديم أفضل خدمة للعملاء وضمان رضاهم.",
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 15),
          Row(
            textDirection: TextDirection.rtl,
            children: [
              Icon(Icons.location_on, color: Color(0xFF4C53A5)),
              SizedBox(width: 5),
              Text(
                "شارع الملك فهد، حي الورود، الرياض",
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesTab() {
    return Container(
      height: 50,
      margin: EdgeInsets.only(top: 15),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 15),
        children: [
          _buildCategoryTab("الكل", true),
          _buildCategoryTab("الأكثر مبيعًا", false),
          _buildCategoryTab("العروض", false),
          _buildCategoryTab("الجديد", false),
          _buildCategoryTab("الوجبات", false),
          _buildCategoryTab("المشروبات", false),
        ],
      ),
    );
  }

  Widget _buildCategoryTab(String title, bool isActive) {
    return Container(
      margin: EdgeInsets.only(right: 10),
      padding: EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: isActive ? Color(0xFF4C53A5) : Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isActive ? Color(0xFF4C53A5) : Colors.grey,
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          title,
          style: TextStyle(
            color: isActive ? Colors.white : Colors.grey[700],
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}