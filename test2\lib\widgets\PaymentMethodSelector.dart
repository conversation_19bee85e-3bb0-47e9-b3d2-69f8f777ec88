import 'package:flutter/material.dart';
import 'package:test2/models/PaymentMethod.dart';
import 'package:test2/services/PaymentService.dart';

class PaymentMethodSelector extends StatefulWidget {
  final double orderAmount;
  final PaymentMethod? selectedMethod;
  final Function(PaymentMethod?) onMethodSelected;
  final bool showFees;

  const PaymentMethodSelector({
    Key? key,
    required this.orderAmount,
    this.selectedMethod,
    required this.onMethodSelected,
    this.showFees = true,
  }) : super(key: key);

  @override
  _PaymentMethodSelectorState createState() => _PaymentMethodSelectorState();
}

class _PaymentMethodSelectorState extends State<PaymentMethodSelector> {
  final PaymentService _paymentService = PaymentService();
  List<PaymentMethod> _paymentMethods = [];
  PaymentMethod? _selectedMethod;

  @override
  void initState() {
    super.initState();
    _loadPaymentMethods();
    _selectedMethod = widget.selectedMethod;
  }

  void _loadPaymentMethods() {
    setState(() {
      _paymentMethods = _paymentService.getEnabledPaymentMethods();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Color(0xFF4C53A5).withOpacity(0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.payment,
                  color: Color(0xFF4C53A5),
                  size: 24,
                ),
                SizedBox(width: 12),
                Text(
                  'طريقة الدفع',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C53A5),
                  ),
                ),
              ],
            ),
          ),

          // قائمة طرق الدفع
          ListView.separated(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: _paymentMethods.length,
            separatorBuilder: (context, index) => Divider(height: 1),
            itemBuilder: (context, index) {
              final method = _paymentMethods[index];
              final isSelected = _selectedMethod?.id == method.id;
              final fees = method.calculateFees(widget.orderAmount);
              final total = widget.orderAmount + fees;

              return InkWell(
                onTap: () {
                  setState(() {
                    _selectedMethod = isSelected ? null : method;
                  });
                  widget.onMethodSelected(_selectedMethod);
                },
                child: Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? method.getColor().withOpacity(0.1) 
                        : Colors.transparent,
                    border: isSelected 
                        ? Border.all(color: method.getColor(), width: 2)
                        : null,
                  ),
                  child: Row(
                    children: [
                      // أيقونة طريقة الدفع
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: method.getColor().withOpacity(0.1),
                          borderRadius: BorderRadius.circular(25),
                        ),
                        child: Icon(
                          method.getIcon(),
                          color: method.getColor(),
                          size: 28,
                        ),
                      ),

                      SizedBox(width: 16),

                      // معلومات طريقة الدفع
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // اسم طريقة الدفع
                            Text(
                              method.nameAr,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: isSelected 
                                    ? method.getColor() 
                                    : Colors.black87,
                              ),
                            ),

                            SizedBox(height: 4),

                            // وصف طريقة الدفع
                            Text(
                              method.descriptionAr,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                            ),

                            // عرض الرسوم إذا كانت موجودة
                            if (widget.showFees && fees > 0) ...[
                              SizedBox(height: 4),
                              Text(
                                'رسوم: ${fees.toStringAsFixed(2)} ريال',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.orange.shade700,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],

                            // عرض معلومات الحساب إذا كانت متاحة
                            if (method.requiresAccountInfo && 
                                method.accountNumber != null) ...[
                              SizedBox(height: 4),
                              Text(
                                'رقم الحساب: ${method.accountNumber}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),

                      // مؤشر الاختيار
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isSelected 
                                ? method.getColor() 
                                : Colors.grey.shade400,
                            width: 2,
                          ),
                          color: isSelected 
                              ? method.getColor() 
                              : Colors.transparent,
                        ),
                        child: isSelected
                            ? Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 16,
                              )
                            : null,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),

          // ملخص المبلغ
          if (_selectedMethod != null && widget.showFees) ...[
            Divider(),
            Container(
              padding: EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'مبلغ الطلب:',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                      ),
                      Text(
                        '${widget.orderAmount.toStringAsFixed(2)} ريال',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),

                  if (_selectedMethod!.calculateFees(widget.orderAmount) > 0) ...[
                    SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'رسوم الدفع:',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        Text(
                          '${_selectedMethod!.calculateFees(widget.orderAmount).toStringAsFixed(2)} ريال',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.orange.shade700,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],

                  Divider(),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'المبلغ الإجمالي:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF4C53A5),
                        ),
                      ),
                      Text(
                        '${_paymentService.calculateTotalAmount(widget.orderAmount, _selectedMethod!).toStringAsFixed(2)} ريال',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF4C53A5),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
