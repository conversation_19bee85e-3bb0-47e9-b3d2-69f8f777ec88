import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:test2/utils/AppColors.dart';

class ProfileAppBar extends StatelessWidget implements PreferredSizeWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.primaryColor,
      padding: EdgeInsets.all(AppDimensions.paddingMedium),
      margin: EdgeInsets.only(top: AppDimensions.marginMedium),
      child: Row(
        textDirection: TextDirection.rtl,
        children: [
          // عنوان الصفحة
          Expanded(
            child: Text(
              "الملف الشخصي",
              textAlign: TextAlign.center,
              style: AppTextStyles.appBarTitle,
            ),
          ),

          // زر الرجوع
          InkWell(
            onTap: () {
              // Navigator.pop(context);
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }else{
                Navigator.pushReplacementNamed(context, "/home");
              }
            },
            borderRadius:
                BorderRadius.circular(AppDimensions.borderRadiusSmall),
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                Icons.arrow_back,
                size: AppDimensions.iconSizeMedium,
                color: AppColors.whiteColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(AppDimensions.appBarHeight);
}
