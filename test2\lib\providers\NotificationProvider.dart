import 'package:flutter/material.dart';
import '../services/NotificationService.dart';
import '../models/Notification.dart';

/// مزود حالة الإشعارات
class NotificationProvider extends ChangeNotifier {
  final NotificationService _notificationService = NotificationService();

  // حالة الإشعارات
  bool _isLoading = false;
  String? _error;

  // البيانات
  List<AppNotification> _notifications = [];
  NotificationSettings _settings = NotificationSettings();
  int _badgeCount = 0;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<AppNotification> get notifications => List.unmodifiable(_notifications);
  NotificationSettings get settings => _settings;
  int get badgeCount => _badgeCount;
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  bool get hasNotifications => _notifications.isNotEmpty;
  bool get hasUnreadNotifications => unreadCount > 0;

  /// تهيئة الإشعارات
  Future<void> initialize() async {
    _setLoading(true);
    try {
      await _notificationService.initialize();
      await _loadData();
    } catch (e) {
      _setError('فشل في تهيئة الإشعارات: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// إضافة إشعار جديد
  Future<void> addNotification({
    required String title,
    required String message,
    required NotificationType type,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? actionUrl,
  }) async {
    try {
      await _notificationService.addNotification(
        title: title,
        message: message,
        type: type,
        data: data,
      );
      await _loadData();
    } catch (e) {
      _setError('فشل في إضافة الإشعار: ${e.toString()}');
    }
  }

  /// قراءة إشعار
  Future<void> markAsRead(String notificationId) async {
    try {
      await _notificationService.markAsRead(notificationId);
      await _loadData();
    } catch (e) {
      _setError('فشل في قراءة الإشعار: ${e.toString()}');
    }
  }

  /// قراءة جميع الإشعارات
  Future<void> markAllAsRead() async {
    try {
      await _notificationService.markAllAsRead();
      await _loadData();
    } catch (e) {
      _setError('فشل في قراءة جميع الإشعارات: ${e.toString()}');
    }
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _notificationService.deleteNotification(notificationId);
      await _loadData();
    } catch (e) {
      _setError('فشل في حذف الإشعار: ${e.toString()}');
    }
  }

  /// مسح جميع الإشعارات
  Future<void> clearAllNotifications() async {
    try {
      await _notificationService.clearAllNotifications();
      await _loadData();
    } catch (e) {
      _setError('فشل في مسح الإشعارات: ${e.toString()}');
    }
  }

  /// تحديث الإعدادات
  Future<void> updateSettings(NotificationSettings newSettings) async {
    try {
      await _notificationService.updateSettings(newSettings);
      _settings = newSettings;
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحديث الإعدادات: ${e.toString()}');
    }
  }

  /// الحصول على الإشعارات حسب النوع
  List<AppNotification> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  /// الحصول على الإشعارات غير المقروءة
  List<AppNotification> getUnreadNotifications() {
    return _notifications.where((n) => !n.isRead).toList();
  }

  /// الحصول على الإشعارات الأخيرة
  List<AppNotification> getRecentNotifications({int limit = 10}) {
    return _notifications.take(limit).toList();
  }

  /// إضافة إشعار طلب
  Future<void> addOrderNotification({
    required String orderId,
    required String orderStatus,
    required String message,
  }) async {
    try {
      await _notificationService.addOrderNotification(
        orderId: orderId,
        status: orderStatus,
        message: message,
      );
      await _loadData();
    } catch (e) {
      _setError('فشل في إضافة إشعار الطلب: ${e.toString()}');
    }
  }

  /// إضافة إشعار عرض
  Future<void> addOfferNotification({
    required String title,
    required String storeName,
    String? discountPercentage,
  }) async {
    try {
      await _notificationService.addOfferNotification(
        title: title,
        storeName: storeName,
        discountPercentage: discountPercentage,
      );
      await _loadData();
    } catch (e) {
      _setError('فشل في إضافة إشعار العرض: ${e.toString()}');
    }
  }

  /// تبديل تفعيل الإشعارات
  Future<void> toggleNotifications(bool enabled) async {
    final newSettings = _settings.copyWith(isEnabled: enabled);
    await updateSettings(newSettings);
  }

  /// تبديل تفعيل إشعارات الطلبات
  Future<void> toggleOrderNotifications(bool enabled) async {
    final newSettings = _settings.copyWith(orderUpdates: enabled);
    await updateSettings(newSettings);
  }

  /// تبديل تفعيل إشعارات العروض
  Future<void> toggleOfferNotifications(bool enabled) async {
    final newSettings = _settings.copyWith(offers: enabled);
    await updateSettings(newSettings);
  }

  /// تبديل تفعيل الصوت
  Future<void> toggleSound(bool enabled) async {
    final newSettings = _settings.copyWith(sound: enabled);
    await updateSettings(newSettings);
  }

  /// تبديل تفعيل الاهتزاز
  Future<void> toggleVibration(bool enabled) async {
    final newSettings = _settings.copyWith(vibration: enabled);
    await updateSettings(newSettings);
  }

  /// تحديث ساعات الهدوء
  Future<void> updateQuietHours({
    required String startTime,
    required String endTime,
    required bool enabled,
  }) async {
    final newSettings = _settings.copyWith(
      quietHoursStart: startTime,
      quietHoursEnd: endTime,
      quietHoursEnabled: enabled,
    );
    await updateSettings(newSettings);
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    try {
      _notifications = _notificationService.notifications;
      _settings = _notificationService.settings;
      _badgeCount = _notificationService.badgeCount;
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل البيانات: ${e.toString()}');
    }
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// تعيين رسالة الخطأ
  void _setError(String? error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _setError(null);
  }

  /// إعادة تحميل البيانات
  Future<void> refresh() async {
    await _loadData();
  }

  /// الحصول على إحصائيات الإشعارات
  Map<String, int> getNotificationStats() {
    final stats = <String, int>{};

    for (final type in NotificationType.values) {
      stats[type.toString()] =
          _notifications.where((n) => n.type == type).length;
    }

    stats['total'] = _notifications.length;
    stats['unread'] = unreadCount;
    stats['read'] = _notifications.length - unreadCount;

    return stats;
  }

  /// الحصول على الإشعارات المجمعة حسب التاريخ
  Map<String, List<AppNotification>> getNotificationsGroupedByDate() {
    final grouped = <String, List<AppNotification>>{};

    for (final notification in _notifications) {
      final dateKey = _getDateKey(notification.timestamp);
      if (!grouped.containsKey(dateKey)) {
        grouped[dateKey] = [];
      }
      grouped[dateKey]!.add(notification);
    }

    return grouped;
  }

  /// الحصول على مفتاح التاريخ
  String _getDateKey(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(Duration(days: 1));
    final notificationDate = DateTime(date.year, date.month, date.day);

    if (notificationDate == today) {
      return 'اليوم';
    } else if (notificationDate == yesterday) {
      return 'أمس';
    } else if (now.difference(notificationDate).inDays < 7) {
      return 'هذا الأسبوع';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
