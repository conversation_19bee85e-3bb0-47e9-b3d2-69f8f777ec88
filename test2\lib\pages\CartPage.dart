import 'package:flutter/material.dart';
import '../widgets/CartAppBar.dart';
import '../widgets/CartItemSamples.dart';
import '../widgets/CustomBottomNavBar.dart';

class CartPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CartAppBar(),
      backgroundColor: Color(0xFFC3243B),
      body: ListView(
        children: [
          Container(
            height: 500,
            padding: EdgeInsets.only(top: 15),
            decoration: BoxDecoration(
                color: Color(0xFFEDECF2),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(35),
                  topRight: Radius.circular(35),
                )),
            child: <PERSON><PERSON>n(
              children: [
                CartItemSamples(),
                Container(
                  // decoration: BoxDecoration(

                  // ),

                  margin: EdgeInsets.symmetric(vertical: 20, horizontal: 15),
                  padding: EdgeInsets.all(10),
                  child: Row(
                    textDirection: TextDirection.rtl,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Color(0xcC3243B),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Icon(
                          Icons.add,
                          color: Color.fromARGB(255, 245, 170, 73),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: Text(
                          "إضافة كوبون",
                          style: TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                              fontSize: 15),
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          )
        ],
      ),
      bottomNavigationBar: FlatBottomNavBar(
        currentIndex: 3, // صفحة السلة
        onTap: (index) {
          NavigationHelper.navigateToPage(context, index);
        },
      ),
    );
  }
}
