import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:test2/providers/CartProvider.dart';
import 'package:test2/models/CartItem.dart';
import 'package:test2/utils/AppColors.dart';
import 'package:test2/widgets/CustomSnackBars.dart';
import 'package:test2/widgets/CustomDialogs.dart';
import 'package:test2/pages/NewCheckoutPage.dart';
import 'package:test2/widgets/AuthGuard.dart';

import '../widgets/CustomBottomNavBar.dart';

/// صفحة السلة
class CartPage extends StatefulWidget {
  @override
  _CartPageState createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: _buildAppBar(),
      body: Consumer<CartProvider>(
        builder: (context, cartProvider, child) {
          if (cartProvider.isLoading) {
            return _buildLoadingWidget();
          }

          if (cartProvider.itemCount == 0) {
            return _buildEmptyCartWidget();
          }

          return Column(
            children: [
              // قائمة المنتجات
              Expanded(
                child: _buildCartItemsList(cartProvider),
              ),

              // ملخص السلة
              _buildCartSummary(cartProvider),
            ],
          );
        },
      ),
      bottomNavigationBar: SafeArea(
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: Offset(0, -2),
              ),
            ],
          ),
          child: FlatBottomNavBar(
            currentIndex: 3, // صفحة السلة
            onTap: (index) {
              NavigationHelper.navigateToPage(context, index);
            },
          ),
        ),
      ),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryColor,
      foregroundColor: Colors.white,
      title: Consumer<CartProvider>(
        builder: (context, cartProvider, child) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'سلة التسوق',
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (cartProvider.itemCount > 0)
                Text(
                  cartProvider.getCartStatusMessage(),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.white70,
                  ),
                ),
            ],
          );
        },
      ),
      actions: [
        Consumer<CartProvider>(
          builder: (context, cartProvider, child) {
            if (cartProvider.itemCount > 0) {
              return IconButton(
                onPressed: () => _showClearCartDialog(cartProvider),
                icon: Icon(Icons.delete_outline),
                tooltip: 'مسح السلة',
              );
            }
            return SizedBox.shrink();
          },
        ),
      ],
    );
  }

  /// بناء واجهة التحميل
  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppColors.primaryColor),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل السلة...',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء واجهة السلة الفارغة
  Widget _buildEmptyCartWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 120.sp,
            color: AppColors.textSecondaryColor.withOpacity(0.5),
          ),
          SizedBox(height: 24.h),
          Text(
            'سلة التسوق فارغة',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimaryColor,
            ),
          ),
          SizedBox(height: 12.h),
          Text(
            'ابدأ بإضافة منتجات إلى سلتك',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondaryColor,
            ),
          ),
          SizedBox(height: 32.h),
          ElevatedButton.icon(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              padding: EdgeInsets.symmetric(
                horizontal: 32.w,
                vertical: 16.h,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            icon: Icon(Icons.shopping_bag_outlined, color: Colors.white),
            label: Text(
              'تصفح المنتجات',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة عناصر السلة
  Widget _buildCartItemsList(CartProvider cartProvider) {
    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: cartProvider.cartItems.length,
      itemBuilder: (context, index) {
        final item = cartProvider.cartItems[index];
        return _buildCartItemCard(item, cartProvider);
      },
    );
  }

  /// بناء بطاقة عنصر السلة
  Widget _buildCartItemCard(CartItem item, CartProvider cartProvider) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Row(
          children: [
            // صورة المنتج
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                item.imageUrl,
                width: 70,
                height: 70,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 70,
                    height: 70,
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.grey[400],
                      size: 30,
                    ),
                  );
                },
              ),
            ),

            SizedBox(width: 12),

            // تفاصيل المنتج
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.name,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimaryColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4),
                  Text(
                    item.category,
                    style: TextStyle(
                      fontSize: 11,
                      color: AppColors.textSecondaryColor,
                    ),
                  ),
                  SizedBox(height: 6),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          '${item.price.toStringAsFixed(2)} ريال',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primaryColor,
                          ),
                        ),
                      ),
                      Text(
                        'المجموع: ${item.totalPrice.toStringAsFixed(2)} ريال',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            SizedBox(width: 8),

            // أدوات التحكم في الكمية
            Column(
              children: [
                // زر الحذف
                IconButton(
                  onPressed: () => _removeItem(item, cartProvider),
                  icon: Icon(
                    Icons.delete_outline,
                    color: Colors.red,
                    size: 18,
                  ),
                  constraints: BoxConstraints(
                    minWidth: 30,
                    minHeight: 30,
                  ),
                ),

                SizedBox(height: 6),

                // أدوات الكمية
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // زر تقليل الكمية
                      InkWell(
                        onTap: () =>
                            cartProvider.decreaseQuantity(item.productId),
                        child: Container(
                          width: 28,
                          height: 28,
                          child: Icon(
                            Icons.remove,
                            size: 14,
                            color: AppColors.textSecondaryColor,
                          ),
                        ),
                      ),

                      // عرض الكمية
                      Container(
                        width: 32,
                        height: 28,
                        decoration: BoxDecoration(
                          border: Border.symmetric(
                            vertical: BorderSide(color: Colors.grey[300]!),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            item.quantity.toString(),
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimaryColor,
                            ),
                          ),
                        ),
                      ),

                      // زر زيادة الكمية
                      InkWell(
                        onTap: () =>
                            cartProvider.increaseQuantity(item.productId),
                        child: Container(
                          width: 28,
                          height: 28,
                          child: Icon(
                            Icons.add,
                            size: 14,
                            color: AppColors.primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء ملخص السلة
  Widget _buildCartSummary(CartProvider cartProvider) {
    final summary = cartProvider.cartSummary;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // تفاصيل الأسعار
          _buildPriceRow('المجموع الفرعي', summary.subtotal),
          _buildPriceRow('الضريبة (15%)', summary.tax),
          _buildPriceRow('الشحن', summary.shipping),
          if (summary.discount > 0)
            _buildPriceRow('الخصم', -summary.discount, isDiscount: true),

          Divider(height: 20),

          // المجموع الإجمالي
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المجموع الإجمالي',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimaryColor,
                ),
              ),
              Text(
                '${summary.total.toStringAsFixed(2)} ريال',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryColor,
                ),
              ),
            ],
          ),

          SizedBox(height: 16),

          // زر إتمام الطلب
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: cartProvider.canCheckout()
                  ? () => _proceedToCheckout()
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: Text(
                'إتمام الطلب',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صف السعر
  Widget _buildPriceRow(String label, double amount,
      {bool isDiscount = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 3),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 13,
              color: AppColors.textSecondaryColor,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ريال',
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: isDiscount ? Colors.green : AppColors.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// إزالة عنصر من السلة
  Future<void> _removeItem(CartItem item, CartProvider cartProvider) async {
    final confirmed = await CustomDialogs.showConfirmDialog(
      context,
      title: 'إزالة من السلة',
      message: 'هل تريد إزالة "${item.name}" من السلة؟',
      confirmText: 'إزالة',
      cancelText: 'إلغاء',
      icon: Icons.delete_outline,
      iconColor: Colors.red,
      isDangerous: true,
    );

    if (confirmed == true) {
      final message =
          await cartProvider.removeFromCartWithMessage(item.productId);
      CustomSnackBars.showInfo(context, message: message);
    }
  }

  /// عرض حوار مسح السلة
  Future<void> _showClearCartDialog(CartProvider cartProvider) async {
    final confirmed = await CustomDialogs.showConfirmDialog(
      context,
      title: 'مسح السلة',
      message: 'هل تريد مسح جميع المنتجات من السلة؟',
      confirmText: 'مسح الكل',
      cancelText: 'إلغاء',
      icon: Icons.delete_sweep,
      iconColor: Colors.red,
      isDangerous: true,
    );

    if (confirmed == true) {
      final message = await cartProvider.clearCartWithMessage();
      CustomSnackBars.showInfo(context, message: message);
    }
  }

  /// المتابعة لإتمام الطلب
  void _proceedToCheckout() async {
    // التحقق من المصادقة قبل إتمام الطلب
    final authSuccess = await AuthHelper.checkAuthBeforeAction(
      context,
      message: 'يرجى إعداد حسابك أولاً لإتمام الطلب',
    );

    if (authSuccess) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => NewCheckoutPage(),
        ),
      );
    }
  }
}
