import 'package:flutter/material.dart';
import '../services/SearchService.dart';
import '../providers/SearchProvider.dart';

/// ورقة ترتيب البحث
class SearchSortSheet extends StatelessWidget {
  final SortOption currentSort;
  final List<SortOptionData> sortOptions;
  final Function(SortOption) onSortSelected;

  const SearchSortSheet({
    Key? key,
    required this.currentSort,
    required this.sortOptions,
    required this.onSortSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // العنوان
          _buildHeader(context),
          
          // خيارات الترتيب
          ...sortOptions.map((option) => _buildSortOption(context, option)),
          
          SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Color(0xFF4C53A5),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.sort, color: Colors.white, size: 24),
          SizedBox(width: 12),
          Text(
            'ترتيب النتائج',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Spacer(),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildSortOption(BuildContext context, SortOptionData option) {
    final isSelected = option.option == currentSort;
    
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: isSelected 
              ? Color(0xFF4C53A5).withOpacity(0.1)
              : Colors.grey[100],
          borderRadius: BorderRadius.circular(10),
        ),
        child: Icon(
          option.icon,
          color: isSelected ? Color(0xFF4C53A5) : Colors.grey[600],
          size: 20,
        ),
      ),
      title: Text(
        option.label,
        style: TextStyle(
          fontSize: 16,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          color: isSelected ? Color(0xFF4C53A5) : Colors.black87,
        ),
      ),
      trailing: isSelected
          ? Icon(
              Icons.check_circle,
              color: Color(0xFF4C53A5),
              size: 24,
            )
          : null,
      onTap: () {
        onSortSelected(option.option);
      },
    );
  }
}
