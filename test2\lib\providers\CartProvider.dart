import 'package:flutter/foundation.dart';
import 'package:test2/models/CartItem.dart';
import 'package:test2/models/Product.dart';
import 'package:test2/services/CartService.dart';

/// مزود حالة السلة
class CartProvider with ChangeNotifier {
  final CartService _cartService = CartService();
  bool _isLoading = false;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// عناصر السلة
  List<CartItem> get cartItems => _cartService.cartItems;

  /// عدد العناصر
  int get itemCount => _cartService.itemCount;

  /// إجمالي الكمية
  int get totalQuantity => _cartService.totalQuantity;

  /// إجمالي السعر
  double get totalPrice => _cartService.totalPrice;

  /// ملخص السلة
  CartSummary get cartSummary => _cartService.cartSummary;

  /// التحقق من وجود منتج في السلة
  bool isProductInCart(String productId) {
    return _cartService.isProductInCart(productId);
  }

  /// الحصول على كمية منتج معين
  int getProductQuantity(String productId) {
    return _cartService.getProductQuantity(productId);
  }

  /// تحميل السلة عند بدء التطبيق
  Future<void> loadCart() async {
    _setLoading(true);
    try {
      await _cartService.loadCart();
    } catch (e) {
      print('خطأ في تحميل السلة: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// إضافة منتج إلى السلة
  Future<bool> addToCart(Product product, {int quantity = 1}) async {
    try {
      final success = await _cartService.addToCart(product, quantity: quantity);
      if (success) {
        notifyListeners();
      }
      return success;
    } catch (e) {
      print('خطأ في إضافة المنتج للسلة: $e');
      return false;
    }
  }

  /// إزالة منتج من السلة
  Future<bool> removeFromCart(String productId) async {
    try {
      final success = await _cartService.removeFromCart(productId);
      if (success) {
        notifyListeners();
      }
      return success;
    } catch (e) {
      print('خطأ في إزالة المنتج من السلة: $e');
      return false;
    }
  }

  /// تحديث كمية منتج
  Future<bool> updateQuantity(String productId, int newQuantity) async {
    try {
      final success = await _cartService.updateQuantity(productId, newQuantity);
      if (success) {
        notifyListeners();
      }
      return success;
    } catch (e) {
      print('خطأ في تحديث كمية المنتج: $e');
      return false;
    }
  }

  /// زيادة كمية منتج
  Future<bool> increaseQuantity(String productId) async {
    try {
      final success = await _cartService.increaseQuantity(productId);
      if (success) {
        notifyListeners();
      }
      return success;
    } catch (e) {
      print('خطأ في زيادة كمية المنتج: $e');
      return false;
    }
  }

  /// تقليل كمية منتج
  Future<bool> decreaseQuantity(String productId) async {
    try {
      final success = await _cartService.decreaseQuantity(productId);
      if (success) {
        notifyListeners();
      }
      return success;
    } catch (e) {
      print('خطأ في تقليل كمية المنتج: $e');
      return false;
    }
  }

  /// مسح السلة بالكامل
  Future<bool> clearCart() async {
    try {
      final success = await _cartService.clearCart();
      if (success) {
        notifyListeners();
      }
      return success;
    } catch (e) {
      print('خطأ في مسح السلة: $e');
      return false;
    }
  }

  /// البحث عن عنصر في السلة
  CartItem? findCartItem(String productId) {
    return _cartService.findCartItem(productId);
  }

  /// الحصول على عناصر السلة حسب الفئة
  List<CartItem> getItemsByCategory(String category) {
    return _cartService.getItemsByCategory(category);
  }

  /// ترتيب عناصر السلة
  void sortItems({bool byName = false, bool byPrice = false, bool byDate = false}) {
    _cartService.sortItems(byName: byName, byPrice: byPrice, byDate: byDate);
    notifyListeners();
  }

  /// إحصائيات السلة
  Map<String, dynamic> getCartStatistics() {
    return _cartService.getCartStatistics();
  }

  /// الحصول على عدد العناصر المحفوظ
  Future<int> getSavedCartCount() async {
    return await _cartService.getSavedCartCount();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// إضافة منتج مع رسالة تأكيد
  Future<String> addToCartWithMessage(Product product, {int quantity = 1}) async {
    try {
      final wasInCart = isProductInCart(product.id);
      final oldQuantity = getProductQuantity(product.id);
      
      final success = await addToCart(product, quantity: quantity);
      
      if (success) {
        if (wasInCart) {
          return 'تم زيادة كمية ${product.name} إلى ${oldQuantity + quantity}';
        } else {
          return 'تم إضافة ${product.name} إلى السلة';
        }
      } else {
        return 'فشل في إضافة المنتج إلى السلة';
      }
    } catch (e) {
      return 'خطأ في إضافة المنتج: $e';
    }
  }

  /// إزالة منتج مع رسالة تأكيد
  Future<String> removeFromCartWithMessage(String productId) async {
    try {
      final item = findCartItem(productId);
      final success = await removeFromCart(productId);
      
      if (success && item != null) {
        return 'تم إزالة ${item.name} من السلة';
      } else {
        return 'فشل في إزالة المنتج من السلة';
      }
    } catch (e) {
      return 'خطأ في إزالة المنتج: $e';
    }
  }

  /// مسح السلة مع رسالة تأكيد
  Future<String> clearCartWithMessage() async {
    try {
      final itemCount = this.itemCount;
      final success = await clearCart();
      
      if (success) {
        return 'تم مسح جميع المنتجات ($itemCount عنصر) من السلة';
      } else {
        return 'فشل في مسح السلة';
      }
    } catch (e) {
      return 'خطأ في مسح السلة: $e';
    }
  }

  /// التحقق من إمكانية الطلب
  bool canCheckout() {
    return itemCount > 0 && totalPrice > 0;
  }

  /// الحصول على رسالة حالة السلة
  String getCartStatusMessage() {
    if (itemCount == 0) {
      return 'السلة فارغة';
    } else if (itemCount == 1) {
      return 'منتج واحد في السلة';
    } else {
      return '$itemCount منتجات في السلة';
    }
  }

  /// الحصول على رسالة إجمالي السعر
  String getTotalPriceMessage() {
    if (totalPrice == 0) {
      return 'المجموع: 0 ريال';
    } else {
      return 'المجموع: ${totalPrice.toStringAsFixed(2)} ريال';
    }
  }
}
