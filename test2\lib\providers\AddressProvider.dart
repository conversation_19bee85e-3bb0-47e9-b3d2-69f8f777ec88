import 'package:flutter/material.dart';
import '../models/Address.dart';
import '../services/AddressService.dart';

/// مزود حالة العناوين
class AddressProvider extends ChangeNotifier {
  List<Address> _addresses = [];
  Address? _defaultAddress;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Address> get addresses => _addresses;
  Address? get defaultAddress => _defaultAddress;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasAddresses => _addresses.isNotEmpty;
  int get addressCount => _addresses.length;

  /// تحميل جميع العناوين
  Future<void> loadAddresses() async {
    _setLoading(true);
    _setError(null);

    try {
      _addresses = await AddressService.loadAddresses();
      _defaultAddress = await AddressService.getDefaultAddress();
      print('تم تحميل ${_addresses.length} عنوان');
    } catch (e) {
      _setError('خطأ في تحميل العناوين: $e');
      print('خطأ في تحميل العناوين: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// إضافة عنوان جديد
  Future<bool> addAddress(Address address) async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await AddressService.addAddress(address);
      
      if (success) {
        await loadAddresses(); // إعادة تحميل العناوين
        print('تم إضافة العنوان: ${address.title}');
        return true;
      } else {
        _setError('فشل في إضافة العنوان');
        return false;
      }
    } catch (e) {
      _setError('خطأ في إضافة العنوان: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث عنوان موجود
  Future<bool> updateAddress(Address address) async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await AddressService.updateAddress(address);
      
      if (success) {
        await loadAddresses(); // إعادة تحميل العناوين
        print('تم تحديث العنوان: ${address.title}');
        return true;
      } else {
        _setError('فشل في تحديث العنوان');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تحديث العنوان: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف عنوان
  Future<bool> deleteAddress(String addressId) async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await AddressService.deleteAddress(addressId);
      
      if (success) {
        await loadAddresses(); // إعادة تحميل العناوين
        print('تم حذف العنوان');
        return true;
      } else {
        _setError('فشل في حذف العنوان');
        return false;
      }
    } catch (e) {
      _setError('خطأ في حذف العنوان: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تعيين عنوان كافتراضي
  Future<bool> setDefaultAddress(String addressId) async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await AddressService.setDefaultAddress(addressId);
      
      if (success) {
        await loadAddresses(); // إعادة تحميل العناوين
        print('تم تعيين العنوان الافتراضي');
        return true;
      } else {
        _setError('فشل في تعيين العنوان الافتراضي');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تعيين العنوان الافتراضي: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على عنوان بالمعرف
  Address? getAddressById(String addressId) {
    try {
      return _addresses.firstWhere((addr) => addr.id == addressId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على العناوين حسب النوع
  List<Address> getAddressesByType(String type) {
    return _addresses.where((addr) => addr.title == type).toList();
  }

  /// البحث في العناوين
  List<Address> searchAddresses(String query) {
    if (query.isEmpty) return _addresses;
    
    final lowerQuery = query.toLowerCase();
    return _addresses.where((addr) {
      return addr.title.toLowerCase().contains(lowerQuery) ||
             addr.fullAddress.toLowerCase().contains(lowerQuery) ||
             addr.city.toLowerCase().contains(lowerQuery) ||
             addr.district.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// مسح جميع العناوين
  Future<bool> clearAllAddresses() async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await AddressService.clearAllAddresses();
      
      if (success) {
        _addresses.clear();
        _defaultAddress = null;
        notifyListeners();
        print('تم مسح جميع العناوين');
        return true;
      } else {
        _setError('فشل في مسح العناوين');
        return false;
      }
    } catch (e) {
      _setError('خطأ في مسح العناوين: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// إنشاء عنوان جديد بمعرف فريد
  Address createNewAddress({
    required String title,
    required String fullAddress,
    required String city,
    required String district,
    required String street,
    required String buildingNumber,
    String apartmentNumber = '',
    String? landmark,
    String? additionalInfo,
    double? latitude,
    double? longitude,
  }) {
    return Address(
      id: 'addr_${DateTime.now().millisecondsSinceEpoch}',
      title: title,
      fullAddress: fullAddress,
      city: city,
      district: district,
      street: street,
      buildingNumber: buildingNumber,
      apartmentNumber: apartmentNumber,
      landmark: landmark,
      additionalInfo: additionalInfo,
      latitude: latitude,
      longitude: longitude,
      isDefault: _addresses.isEmpty, // أول عنوان يكون افتراضي
    );
  }

  /// التحقق من صحة العنوان
  String? validateAddress(Address address) {
    if (address.title.trim().isEmpty) {
      return 'يرجى إدخال عنوان العنوان';
    }
    if (address.city.trim().isEmpty) {
      return 'يرجى إدخال المدينة';
    }
    if (address.district.trim().isEmpty) {
      return 'يرجى إدخال الحي';
    }
    if (address.street.trim().isEmpty) {
      return 'يرجى إدخال الشارع';
    }
    if (address.buildingNumber.trim().isEmpty) {
      return 'يرجى إدخال رقم المبنى';
    }
    return null;
  }

  /// الحصول على أنواع العناوين المتاحة
  List<String> getAvailableAddressTypes() {
    return ['المنزل', 'العمل', 'الأصدقاء', 'العائلة', 'أخرى'];
  }

  /// الحصول على إحصائيات العناوين
  Map<String, int> getAddressStatistics() {
    Map<String, int> stats = {};
    for (Address address in _addresses) {
      stats[address.title] = (stats[address.title] ?? 0) + 1;
    }
    return stats;
  }

  /// تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تحديث رسالة الخطأ
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// إعادة تحميل العناوين
  Future<void> refresh() async {
    await loadAddresses();
  }

  @override
  void dispose() {
    _addresses.clear();
    _defaultAddress = null;
    super.dispose();
  }
}
