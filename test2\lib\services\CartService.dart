import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:test2/models/CartItem.dart';
import 'package:test2/models/Product.dart';

/// خدمة إدارة السلة
class CartService {
  static const String _cartKey = 'shopping_cart';
  static const String _cartCountKey = 'cart_count';

  List<CartItem> _cartItems = [];

  /// الحصول على عناصر السلة
  List<CartItem> get cartItems => List.unmodifiable(_cartItems);

  /// عدد العناصر في السلة
  int get itemCount => _cartItems.length;

  /// إجمالي الكمية في السلة
  int get totalQuantity =>
      _cartItems.fold(0, (sum, item) => sum + item.quantity);

  /// إجمالي السعر
  double get totalPrice =>
      _cartItems.fold(0.0, (sum, item) => sum + item.totalPrice);

  /// ملخص السلة
  CartSummary get cartSummary => CartSummary.fromCartItems(_cartItems);

  /// التحقق من وجود منتج في السلة
  bool isProductInCart(String productId) {
    return _cartItems.any((item) => item.productId == productId);
  }

  /// الحصول على كمية منتج معين في السلة
  int getProductQuantity(String productId) {
    final item = _cartItems.firstWhere(
      (item) => item.productId == productId,
      orElse: () => CartItem(
        id: '',
        productId: '',
        name: '',
        description: '',
        price: 0,
        imageUrl: '',
        category: '',
        quantity: 0,
      ),
    );
    return item.quantity;
  }

  /// إضافة منتج إلى السلة
  Future<bool> addToCart(Product product, {int quantity = 1}) async {
    try {
      // البحث عن المنتج في السلة
      final existingItemIndex = _cartItems.indexWhere(
        (item) => item.productId == product.id,
      );

      if (existingItemIndex != -1) {
        // المنتج موجود - زيادة الكمية
        _cartItems[existingItemIndex].quantity += quantity;
      } else {
        // منتج جديد - إضافة إلى السلة
        final cartItem = CartItem.fromProduct(product, quantity: quantity);
        _cartItems.add(cartItem);
      }

      await _saveCart();
      return true;
    } catch (e) {
      print('خطأ في إضافة المنتج للسلة: $e');
      return false;
    }
  }

  /// إزالة منتج من السلة
  Future<bool> removeFromCart(String productId) async {
    try {
      _cartItems.removeWhere((item) => item.productId == productId);
      await _saveCart();
      return true;
    } catch (e) {
      print('خطأ في إزالة المنتج من السلة: $e');
      return false;
    }
  }

  /// تحديث كمية منتج في السلة
  Future<bool> updateQuantity(String productId, int newQuantity) async {
    try {
      if (newQuantity <= 0) {
        return await removeFromCart(productId);
      }

      final itemIndex = _cartItems.indexWhere(
        (item) => item.productId == productId,
      );

      if (itemIndex != -1) {
        _cartItems[itemIndex].updateQuantity(newQuantity);
        await _saveCart();
        return true;
      }
      return false;
    } catch (e) {
      print('خطأ في تحديث كمية المنتج: $e');
      return false;
    }
  }

  /// زيادة كمية منتج
  Future<bool> increaseQuantity(String productId) async {
    try {
      final itemIndex = _cartItems.indexWhere(
        (item) => item.productId == productId,
      );

      if (itemIndex != -1) {
        _cartItems[itemIndex].increaseQuantity();
        await _saveCart();
        return true;
      }
      return false;
    } catch (e) {
      print('خطأ في زيادة كمية المنتج: $e');
      return false;
    }
  }

  /// تقليل كمية منتج
  Future<bool> decreaseQuantity(String productId) async {
    try {
      final itemIndex = _cartItems.indexWhere(
        (item) => item.productId == productId,
      );

      if (itemIndex != -1) {
        if (_cartItems[itemIndex].quantity > 1) {
          _cartItems[itemIndex].decreaseQuantity();
          await _saveCart();
        } else {
          // إزالة المنتج إذا كانت الكمية 1
          return await removeFromCart(productId);
        }
        return true;
      }
      return false;
    } catch (e) {
      print('خطأ في تقليل كمية المنتج: $e');
      return false;
    }
  }

  /// مسح السلة بالكامل
  Future<bool> clearCart() async {
    try {
      _cartItems.clear();
      await _saveCart();
      return true;
    } catch (e) {
      print('خطأ في مسح السلة: $e');
      return false;
    }
  }

  /// حفظ السلة في التخزين المحلي
  Future<void> _saveCart() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حفظ عناصر السلة
      final cartJson = _cartItems.map((item) => item.toMap()).toList();
      await prefs.setString(_cartKey, jsonEncode(cartJson));

      // حفظ عدد العناصر للوصول السريع
      await prefs.setInt(_cartCountKey, totalQuantity);
    } catch (e) {
      print('خطأ في حفظ السلة: $e');
    }
  }

  /// تحميل السلة من التخزين المحلي
  Future<void> loadCart() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartString = prefs.getString(_cartKey);

      if (cartString != null) {
        final cartJson = jsonDecode(cartString) as List;
        _cartItems = cartJson
            .map((item) => CartItem.fromMap(item as Map<String, dynamic>))
            .toList();
      }
    } catch (e) {
      print('خطأ في تحميل السلة: $e');
      _cartItems = [];
    }
  }

  /// الحصول على عدد العناصر المحفوظ (للوصول السريع)
  Future<int> getSavedCartCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_cartCountKey) ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// البحث عن عنصر في السلة
  CartItem? findCartItem(String productId) {
    try {
      return _cartItems.firstWhere((item) => item.productId == productId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على عناصر السلة حسب الفئة
  List<CartItem> getItemsByCategory(String category) {
    return _cartItems.where((item) => item.category == category).toList();
  }

  /// الحصول على عناصر السلة مجمعة حسب المتجر
  Map<String, List<CartItem>> getItemsGroupedByStore() {
    Map<String, List<CartItem>> groupedItems = {};

    for (CartItem item in _cartItems) {
      String storeKey = item.storeName ?? 'متجر غير محدد';

      if (!groupedItems.containsKey(storeKey)) {
        groupedItems[storeKey] = [];
      }

      groupedItems[storeKey]!.add(item);
    }

    return groupedItems;
  }

  /// الحصول على قائمة المتاجر الموجودة في السلة
  List<String> getStoresInCart() {
    Set<String> stores = {};

    for (CartItem item in _cartItems) {
      if (item.storeName != null && item.storeName!.isNotEmpty) {
        stores.add(item.storeName!);
      }
    }

    return stores.toList()..sort();
  }

  /// حساب إجمالي السعر لمتجر معين
  double getTotalPriceForStore(String storeName) {
    return _cartItems
        .where((item) => item.storeName == storeName)
        .fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  /// حساب عدد العناصر لمتجر معين
  int getItemCountForStore(String storeName) {
    return _cartItems.where((item) => item.storeName == storeName).length;
  }

  /// حساب إجمالي الكمية لمتجر معين
  int getTotalQuantityForStore(String storeName) {
    return _cartItems
        .where((item) => item.storeName == storeName)
        .fold(0, (sum, item) => sum + item.quantity);
  }

  /// ترتيب عناصر السلة
  void sortItems(
      {bool byName = false, bool byPrice = false, bool byDate = false}) {
    if (byName) {
      _cartItems.sort((a, b) => a.name.compareTo(b.name));
    } else if (byPrice) {
      _cartItems.sort((a, b) => b.price.compareTo(a.price));
    } else if (byDate) {
      _cartItems.sort((a, b) => b.addedAt.compareTo(a.addedAt));
    }
  }

  /// إحصائيات السلة
  Map<String, dynamic> getCartStatistics() {
    final categories = <String, int>{};
    double maxPrice = 0;
    double minPrice = double.infinity;

    for (final item in _cartItems) {
      categories[item.category] = (categories[item.category] ?? 0) + 1;
      if (item.price > maxPrice) maxPrice = item.price;
      if (item.price < minPrice) minPrice = item.price;
    }

    return {
      'totalItems': itemCount,
      'totalQuantity': totalQuantity,
      'totalPrice': totalPrice,
      'categories': categories,
      'maxPrice': maxPrice,
      'minPrice': minPrice == double.infinity ? 0 : minPrice,
      'averagePrice': itemCount > 0 ? totalPrice / totalQuantity : 0,
    };
  }
}

/// ملخص السلة
class CartSummary {
  final double subtotal;
  final double tax;
  final double shipping;
  final double discount;
  final double total;
  final int itemCount;
  final int totalQuantity;

  CartSummary({
    required this.subtotal,
    required this.tax,
    required this.shipping,
    required this.discount,
    required this.total,
    required this.itemCount,
    required this.totalQuantity,
  });

  /// إنشاء ملخص من عناصر السلة
  factory CartSummary.fromCartItems(List<CartItem> cartItems) {
    final subtotal = cartItems.fold(0.0, (sum, item) => sum + item.totalPrice);
    final tax = subtotal * 0.15; // ضريبة 15%
    final shipping = subtotal >= 200 ? 0.0 : 25.0; // شحن مجاني فوق 200 ريال
    const discount = 0.0; // يمكن إضافة منطق الخصومات لاحقاً
    final total = subtotal + tax + shipping - discount;
    final itemCount = cartItems.length;
    final totalQuantity = cartItems.fold(0, (sum, item) => sum + item.quantity);

    return CartSummary(
      subtotal: subtotal,
      tax: tax,
      shipping: shipping,
      discount: discount,
      total: total,
      itemCount: itemCount,
      totalQuantity: totalQuantity,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'subtotal': subtotal,
      'tax': tax,
      'shipping': shipping,
      'discount': discount,
      'total': total,
      'itemCount': itemCount,
      'totalQuantity': totalQuantity,
    };
  }

  /// إنشاء من Map
  factory CartSummary.fromMap(Map<String, dynamic> map) {
    return CartSummary(
      subtotal: (map['subtotal'] ?? 0.0).toDouble(),
      tax: (map['tax'] ?? 0.0).toDouble(),
      shipping: (map['shipping'] ?? 0.0).toDouble(),
      discount: (map['discount'] ?? 0.0).toDouble(),
      total: (map['total'] ?? 0.0).toDouble(),
      itemCount: map['itemCount'] ?? 0,
      totalQuantity: map['totalQuantity'] ?? 0,
    );
  }

  /// نسخ مع تعديل
  CartSummary copyWith({
    double? subtotal,
    double? tax,
    double? shipping,
    double? discount,
    double? total,
    int? itemCount,
    int? totalQuantity,
  }) {
    return CartSummary(
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      shipping: shipping ?? this.shipping,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      itemCount: itemCount ?? this.itemCount,
      totalQuantity: totalQuantity ?? this.totalQuantity,
    );
  }

  @override
  String toString() {
    return 'CartSummary(subtotal: $subtotal, tax: $tax, shipping: $shipping, discount: $discount, total: $total, itemCount: $itemCount, totalQuantity: $totalQuantity)';
  }
}
