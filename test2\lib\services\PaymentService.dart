import 'package:flutter/material.dart';
import 'package:test2/models/PaymentMethod.dart';

class PaymentService {
  static final PaymentService _instance = PaymentService._internal();
  factory PaymentService() => _instance;
  PaymentService._internal();

  // قائمة طرق الدفع المتاحة
  static final List<PaymentMethod> _availablePaymentMethods = [
    // كاش عند الاستلام
    PaymentMethod(
      id: 'cash_on_delivery',
      type: PaymentType.cash,
      name: 'Cash on Delivery',
      nameAr: 'كاش عند الاستلام',
      description: 'Pay when you receive your order',
      descriptionAr: 'ادفع عند استلام طلبك',
      icon: Icons.money,
      color: Colors.green,
      isEnabled: true,
      requiresAccountInfo: false,
      fees: 0.0,
    ),

    // حوالة بنكية
    PaymentMethod(
      id: 'bank_transfer',
      type: PaymentType.bankTransfer,
      name: 'Bank Transfer',
      nameAr: 'حوالة بنكية',
      description: 'Transfer money to our bank account',
      descriptionAr: 'حول المبلغ إلى حسابنا البنكي',
      icon: Icons.account_balance,
      color: Colors.blue,
      isEnabled: true,
      requiresAccountInfo: true,
      accountNumber: '**********',
      accountName: 'زاد اليمن للتجارة',
      fees: 5.0,
    ),

    // محفظة جيب
    PaymentMethod(
      id: 'jeeb_wallet',
      type: PaymentType.jeebWallet,
      name: 'Jeeb Wallet',
      nameAr: 'محفظة جيب',
      description: 'Pay using Jeeb digital wallet',
      descriptionAr: 'ادفع باستخدام محفظة جيب الرقمية',
      icon: Icons.account_balance_wallet,
      color: Color(0xFF1E88E5),
      isEnabled: true,
      requiresAccountInfo: true,
      accountNumber: '*********',
      accountName: 'زاد اليمن',
      fees: 2.0,
    ),

    // أم فلوس
    PaymentMethod(
      id: 'om_flous',
      type: PaymentType.omFlous,
      name: 'Om Flous',
      nameAr: 'أم فلوس',
      description: 'Pay using Om Flous wallet',
      descriptionAr: 'ادفع باستخدام محفظة أم فلوس',
      icon: Icons.payment,
      color: Color(0xFFFF6B35),
      isEnabled: true,
      requiresAccountInfo: true,
      accountNumber: '*********',
      accountName: 'زاد اليمن',
      fees: 2.5,
    ),

    // كاش موبايل
    PaymentMethod(
      id: 'cash_mobile',
      type: PaymentType.cashMobile,
      name: 'Cash Mobile',
      nameAr: 'كاش موبايل',
      description: 'Pay using Cash Mobile service',
      descriptionAr: 'ادفع باستخدام خدمة كاش موبايل',
      icon: Icons.phone_android,
      color: Color(0xFF4CAF50),
      isEnabled: true,
      requiresAccountInfo: true,
      accountNumber: '*********',
      accountName: 'زاد اليمن',
      fees: 3.0,
    ),

    // محافظ أخرى
    PaymentMethod(
      id: 'other_wallet',
      type: PaymentType.otherWallet,
      name: 'Other Wallets',
      nameAr: 'محافظ أخرى',
      description: 'Pay using other digital wallets',
      descriptionAr: 'ادفع باستخدام محافظ رقمية أخرى',
      icon: Icons.wallet,
      color: Color(0xFF9C27B0),
      isEnabled: true,
      requiresAccountInfo: true,
      accountNumber: '*********',
      accountName: 'زاد اليمن',
      fees: 2.0,
    ),
  ];

  // الحصول على جميع طرق الدفع
  List<PaymentMethod> getAllPaymentMethods() {
    return _availablePaymentMethods;
  }

  // الحصول على طرق الدفع المفعلة فقط
  List<PaymentMethod> getEnabledPaymentMethods() {
    return _availablePaymentMethods.where((method) => method.isEnabled).toList();
  }

  // الحصول على طريقة دفع بالمعرف
  PaymentMethod? getPaymentMethodById(String id) {
    try {
      return _availablePaymentMethods.firstWhere((method) => method.id == id);
    } catch (e) {
      return null;
    }
  }

  // الحصول على طرق الدفع حسب النوع
  List<PaymentMethod> getPaymentMethodsByType(PaymentType type) {
    return _availablePaymentMethods
        .where((method) => method.type == type && method.isEnabled)
        .toList();
  }

  // التحقق من صحة طريقة الدفع
  bool validatePaymentMethod(PaymentMethod method, {String? userAccountNumber}) {
    if (!method.isEnabled) return false;
    
    if (method.requiresAccountInfo) {
      if (method.type == PaymentType.bankTransfer) {
        // للحوالة البنكية، نحتاج رقم حساب المستخدم
        return userAccountNumber != null && userAccountNumber.isNotEmpty;
      }
      // للمحافظ الرقمية، نحتاج رقم المحفظة
      return userAccountNumber != null && userAccountNumber.isNotEmpty;
    }
    
    return true;
  }

  // حساب إجمالي المبلغ مع الرسوم
  double calculateTotalAmount(double orderAmount, PaymentMethod method) {
    double fees = method.calculateFees(orderAmount);
    return orderAmount + fees;
  }

  // معالجة الدفع
  Future<PaymentResult> processPayment({
    required PaymentMethod method,
    required double amount,
    String? userAccountNumber,
    String? notes,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (!validatePaymentMethod(method, userAccountNumber: userAccountNumber)) {
        return PaymentResult(
          success: false,
          message: 'بيانات الدفع غير صحيحة',
          messageAr: 'بيانات الدفع غير صحيحة',
        );
      }

      // حساب الرسوم والإجمالي
      double fees = method.calculateFees(amount);
      double total = amount + fees;

      // محاكاة معالجة الدفع
      await Future.delayed(Duration(seconds: 2));

      // إنشاء معرف المعاملة
      String transactionId = _generateTransactionId();

      // إنشاء معلومات الدفع
      PaymentInfo paymentInfo = PaymentInfo(
        method: method,
        amount: amount,
        fees: fees,
        total: total,
        transactionId: transactionId,
        accountNumber: userAccountNumber,
        notes: notes,
        timestamp: DateTime.now(),
      );

      return PaymentResult(
        success: true,
        message: 'Payment processed successfully',
        messageAr: 'تم معالجة الدفع بنجاح',
        transactionId: transactionId,
        paymentInfo: paymentInfo,
      );

    } catch (e) {
      return PaymentResult(
        success: false,
        message: 'Payment processing failed: ${e.toString()}',
        messageAr: 'فشل في معالجة الدفع',
      );
    }
  }

  // إنشاء معرف معاملة فريد
  String _generateTransactionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'TXN${timestamp}$random';
  }

  // الحصول على تعليمات الدفع
  String getPaymentInstructions(PaymentMethod method, {String? accountNumber}) {
    switch (method.type) {
      case PaymentType.cash:
        return 'سيتم تحصيل المبلغ عند تسليم الطلب. تأكد من توفر المبلغ المطلوب نقداً.';
      
      case PaymentType.bankTransfer:
        return 'قم بتحويل المبلغ إلى الحساب التالي:\n'
               'رقم الحساب: ${method.accountNumber}\n'
               'اسم الحساب: ${method.accountName}\n'
               'ثم أرسل صورة الإيصال عبر الواتساب.';
      
      case PaymentType.jeebWallet:
        return 'قم بتحويل المبلغ إلى محفظة جيب:\n'
               'رقم المحفظة: ${method.accountNumber}\n'
               'اسم المحفظة: ${method.accountName}\n'
               'ثم أرسل رقم العملية.';
      
      case PaymentType.omFlous:
        return 'قم بتحويل المبلغ إلى محفظة أم فلوس:\n'
               'رقم المحفظة: ${method.accountNumber}\n'
               'اسم المحفظة: ${method.accountName}\n'
               'ثم أرسل رقم العملية.';
      
      case PaymentType.cashMobile:
        return 'قم بتحويل المبلغ عبر كاش موبايل:\n'
               'الرقم: ${method.accountNumber}\n'
               'الاسم: ${method.accountName}\n'
               'ثم أرسل رقم العملية.';
      
      case PaymentType.otherWallet:
        return 'قم بتحويل المبلغ إلى المحفظة:\n'
               'رقم المحفظة: ${method.accountNumber}\n'
               'اسم المحفظة: ${method.accountName}\n'
               'ثم أرسل تأكيد العملية.';
    }
  }

  // الحصول على أيقونة طريقة الدفع
  Widget getPaymentMethodIcon(PaymentMethod method, {double size = 24}) {
    return Icon(
      method.getIcon(),
      color: method.getColor(),
      size: size,
    );
  }

  // الحصول على لون طريقة الدفع
  Color getPaymentMethodColor(PaymentMethod method) {
    return method.getColor();
  }
}

// فئة نتيجة الدفع
class PaymentResult {
  final bool success;
  final String message;
  final String messageAr;
  final String? transactionId;
  final PaymentInfo? paymentInfo;
  final String? errorCode;

  PaymentResult({
    required this.success,
    required this.message,
    required this.messageAr,
    this.transactionId,
    this.paymentInfo,
    this.errorCode,
  });

  Map<String, dynamic> toMap() {
    return {
      'success': success,
      'message': message,
      'messageAr': messageAr,
      'transactionId': transactionId,
      'paymentInfo': paymentInfo?.toMap(),
      'errorCode': errorCode,
    };
  }

  factory PaymentResult.fromMap(Map<String, dynamic> map) {
    return PaymentResult(
      success: map['success'],
      message: map['message'],
      messageAr: map['messageAr'],
      transactionId: map['transactionId'],
      paymentInfo: map['paymentInfo'] != null 
          ? PaymentInfo.fromMap(map['paymentInfo']) 
          : null,
      errorCode: map['errorCode'],
    );
  }
}
