{"logs": [{"outputFile": "com.example.test2.app-mergeDebugResources-49:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a6944d895559a9e64d476809b757492a\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "359", "startColumns": "4", "startOffsets": "21446", "endColumns": "42", "endOffsets": "21484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6d8587edf13ce113db373e0ea22bdae2\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "319,335,363,3022,3027", "startColumns": "4,4,4,4,4", "startOffsets": "19424,20178,21653,171888,172058", "endLines": "319,335,363,3026,3030", "endColumns": "56,64,63,24,24", "endOffsets": "19476,20238,21712,172053,172202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bc4a5afbadec1452e88719b386e4569f\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,318,2233,2239,3620,3628,3643", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19364,143188,143383,190045,190327,190941", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,318,2238,2243,3627,3642,3658", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19419,143378,143536,190322,190936,191590"}}, {"source": "F:\\FlutterApps\\augmentsAPP\\appss\\test2\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1525,1529", "startColumns": "4,4", "startOffsets": "97253,97434", "endLines": "1528,1531", "endColumns": "12,12", "endOffsets": "97429,97598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fe89009948d46fc15d9f02484b776d7a\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "332,360", "startColumns": "4,4", "startOffsets": "20034,21489", "endColumns": "41,59", "endOffsets": "20071,21544"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7d00bd86c9728072d21116e569b66936\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "362", "startColumns": "4", "startOffsets": "21603", "endColumns": "49", "endOffsets": "21648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2fb1c87eb9bd4c095f30b9ec565cd4a6\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3603,3675,3747,3820,3877,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3670,3742,3815,3872,3930,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,68,69,70,71,72,73,76,77,78,79,80,81,82,83,84,85,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,124,125,126,127,129,130,131,132,133,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,229,230,234,235,236,237,238,239,240,270,271,272,273,274,275,276,277,313,314,315,316,322,330,331,336,358,364,365,367,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,431,436,437,438,439,440,441,449,450,454,458,462,467,473,480,484,488,493,497,501,505,509,513,517,523,527,533,537,543,547,552,556,559,563,569,573,579,583,589,592,596,600,604,608,612,613,614,615,618,621,624,627,631,632,633,634,635,638,640,642,644,649,650,654,660,664,665,667,678,679,683,689,693,694,695,699,726,730,731,735,763,933,959,1130,1156,1187,1195,1201,1215,1237,1242,1247,1257,1266,1275,1279,1286,1294,1301,1302,1311,1314,1317,1321,1325,1329,1332,1333,1338,1343,1353,1358,1365,1371,1372,1375,1379,1384,1386,1388,1391,1394,1396,1400,1403,1410,1413,1416,1420,1422,1426,1428,1430,1432,1436,1444,1452,1464,1470,1479,1482,1493,1496,1497,1502,1503,1532,1601,1671,1672,1682,1691,1843,1845,1849,1852,1855,1858,1861,1864,1867,1870,1874,1877,1880,1883,1887,1890,1894,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1920,1922,1923,1924,1925,1926,1927,1928,1929,1931,1932,1934,1935,1937,1939,1940,1942,1943,1944,1945,1946,1947,1949,1950,1951,1952,1953,1970,1972,1974,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1990,1991,1992,1993,1994,1995,1997,2001,2005,2006,2007,2008,2009,2010,2014,2015,2016,2017,2019,2021,2023,2025,2027,2028,2029,2030,2032,2034,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2050,2051,2052,2053,2055,2057,2058,2060,2061,2063,2065,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2080,2081,2082,2083,2085,2086,2087,2088,2089,2091,2093,2095,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2129,2204,2207,2210,2213,2227,2244,2286,2315,2342,2351,2413,2777,2808,2946,3070,3094,3100,3192,3213,3337,3365,3371,3515,3541,3608,3679,3779,3799,3854,3866,3892", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2533,2614,2675,2750,2826,2903,3141,3226,3308,3384,3460,3537,3615,3721,3827,3906,3986,4043,4903,4977,5052,5117,5183,5243,5304,5376,5449,5516,5584,5643,5702,5761,5820,5879,5933,5987,6040,6094,6148,6202,6546,6620,6699,6772,6917,6989,7061,7134,7191,7322,7396,7470,7545,7617,7690,7760,7831,7891,7952,8021,8090,8160,8234,8310,8374,8451,8527,8604,8669,8738,8815,8890,8959,9027,9104,9170,9231,9328,9393,9462,9561,9632,9691,9749,9806,9865,9929,10000,10072,10144,10216,10288,10355,10423,10491,10550,10613,10677,10767,10858,10918,10984,11051,11117,11187,11251,11304,11371,11432,11499,11612,11670,11733,11798,11863,11938,12011,12083,12132,12193,12254,12315,12377,12441,12505,12569,12634,12697,12757,12818,12884,12943,13003,13065,13136,13196,13752,13838,14088,14178,14265,14353,14435,14518,14608,16545,16597,16655,16700,16766,16830,16887,16944,19121,19178,19226,19275,19568,19938,19985,20243,21414,21717,21781,21903,22224,22298,22368,22446,22500,22570,22655,22703,22749,22810,22873,22939,23003,23074,23137,23202,23266,23327,23388,23440,23513,23587,23656,23731,23805,23879,24020,27296,27657,27735,27825,27913,28009,28099,28681,28770,29017,29298,29550,29835,30228,30705,30927,31149,31425,31652,31882,32112,32342,32572,32799,33218,33444,33869,34099,34527,34746,35029,35237,35368,35595,36021,36246,36673,36894,37319,37439,37715,38016,38340,38631,38945,39082,39213,39318,39560,39727,39931,40139,40410,40522,40634,40739,40856,41070,41216,41356,41442,41790,41878,42124,42542,42791,42873,42971,43563,43663,43915,44339,44594,44688,44777,45014,47038,47280,47382,47635,49791,60323,61839,72470,73998,75755,76381,76801,77862,79127,79383,79619,80166,80660,81265,81463,82043,82607,82982,83100,83638,83795,83991,84264,84520,84690,84831,84895,85260,85627,86303,86567,86905,87258,87352,87538,87844,88106,88231,88358,88597,88808,88927,89120,89297,89752,89933,90055,90314,90427,90614,90716,90823,90952,91227,91735,92231,93108,93402,93972,94121,94853,95025,95109,95445,95537,97603,102849,108238,108300,108878,109462,117409,117522,117751,117911,118063,118234,118400,118569,118736,118899,119142,119312,119485,119656,119930,120129,120334,120664,120748,120844,120940,121038,121138,121240,121342,121444,121546,121648,121748,121844,121956,122085,122208,122339,122470,122568,122682,122776,122916,123050,123146,123258,123358,123474,123570,123682,123782,123922,124058,124222,124352,124510,124660,124801,124945,125080,125192,125342,125470,125598,125734,125866,125996,126126,126238,127518,127664,127808,127946,128012,128102,128178,128282,128372,128474,128582,128690,128790,128870,128962,129060,129170,129248,129354,129446,129550,129660,129782,129945,130102,130182,130282,130372,130482,130572,130813,130907,131013,131105,131205,131317,131431,131547,131663,131757,131871,131983,132085,132205,132327,132409,132513,132633,132759,132857,132951,133039,133151,133267,133389,133501,133676,133792,133878,133970,134082,134206,134273,134399,134467,134595,134739,134867,134936,135031,135146,135259,135358,135467,135578,135689,135790,135895,135995,136125,136216,136339,136433,136545,136631,136735,136831,136919,137037,137141,137245,137371,137459,137567,137667,137757,137867,137951,138053,138137,138191,138255,138361,138447,138557,138641,139665,142281,142399,142514,142594,142955,143541,144945,146289,147650,148038,150813,160902,161942,168755,173056,173807,174069,176423,176802,181080,181934,182163,186771,187781,189733,192133,196257,197001,199132,199472,200783", "endLines": "4,27,28,59,60,61,62,68,69,70,71,72,73,76,77,78,79,80,81,82,83,84,85,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,124,125,126,127,129,130,131,132,133,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,229,230,234,235,236,237,238,239,240,270,271,272,273,274,275,276,277,313,314,315,316,322,330,331,336,358,364,365,367,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,431,436,437,438,439,440,448,449,453,457,461,466,472,479,483,487,492,496,500,504,508,512,516,522,526,532,536,542,546,551,555,558,562,568,572,578,582,588,591,595,599,603,607,611,612,613,614,617,620,623,626,630,631,632,633,634,637,639,641,643,648,649,653,659,663,664,666,677,678,682,688,692,693,694,698,725,729,730,734,762,932,958,1129,1155,1186,1194,1200,1214,1236,1241,1246,1256,1265,1274,1278,1285,1293,1300,1301,1310,1313,1316,1320,1324,1328,1331,1332,1337,1342,1352,1357,1364,1370,1371,1374,1378,1383,1385,1387,1390,1393,1395,1399,1402,1409,1412,1415,1419,1421,1425,1427,1429,1431,1435,1443,1451,1463,1469,1478,1481,1492,1495,1496,1501,1502,1507,1600,1670,1671,1681,1690,1691,1844,1848,1851,1854,1857,1860,1863,1866,1869,1873,1876,1879,1882,1886,1889,1893,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1919,1921,1922,1923,1924,1925,1926,1927,1928,1930,1931,1933,1934,1936,1938,1939,1941,1942,1943,1944,1945,1946,1948,1949,1950,1951,1952,1953,1971,1973,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1989,1990,1991,1992,1993,1994,1996,2000,2004,2005,2006,2007,2008,2009,2013,2014,2015,2016,2018,2020,2022,2024,2026,2027,2028,2029,2031,2033,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2049,2050,2051,2052,2054,2056,2057,2059,2060,2062,2064,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2079,2080,2081,2082,2084,2085,2086,2087,2088,2090,2092,2094,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2203,2206,2209,2212,2226,2232,2253,2314,2341,2350,2412,2771,2780,2835,2963,3093,3099,3105,3212,3336,3356,3370,3374,3520,3575,3619,3744,3798,3853,3865,3891,3898", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2609,2670,2745,2821,2898,2976,3221,3303,3379,3455,3532,3610,3716,3822,3901,3981,4038,4096,4972,5047,5112,5178,5238,5299,5371,5444,5511,5579,5638,5697,5756,5815,5874,5928,5982,6035,6089,6143,6197,6251,6615,6694,6767,6841,6984,7056,7129,7186,7244,7391,7465,7540,7612,7685,7755,7826,7886,7947,8016,8085,8155,8229,8305,8369,8446,8522,8599,8664,8733,8810,8885,8954,9022,9099,9165,9226,9323,9388,9457,9556,9627,9686,9744,9801,9860,9924,9995,10067,10139,10211,10283,10350,10418,10486,10545,10608,10672,10762,10853,10913,10979,11046,11112,11182,11246,11299,11366,11427,11494,11607,11665,11728,11793,11858,11933,12006,12078,12127,12188,12249,12310,12372,12436,12500,12564,12629,12692,12752,12813,12879,12938,12998,13060,13131,13191,13259,13833,13920,14173,14260,14348,14430,14513,14603,14694,16592,16650,16695,16761,16825,16882,16939,16993,19173,19221,19270,19321,19597,19980,20029,20284,21441,21776,21838,21955,22293,22363,22441,22495,22565,22650,22698,22744,22805,22868,22934,22998,23069,23132,23197,23261,23322,23383,23435,23508,23582,23651,23726,23800,23874,24015,24085,27344,27730,27820,27908,28004,28094,28676,28765,29012,29293,29545,29830,30223,30700,30922,31144,31420,31647,31877,32107,32337,32567,32794,33213,33439,33864,34094,34522,34741,35024,35232,35363,35590,36016,36241,36668,36889,37314,37434,37710,38011,38335,38626,38940,39077,39208,39313,39555,39722,39926,40134,40405,40517,40629,40734,40851,41065,41211,41351,41437,41785,41873,42119,42537,42786,42868,42966,43558,43658,43910,44334,44589,44683,44772,45009,47033,47275,47377,47630,49786,60318,61834,72465,73993,75750,76376,76796,77857,79122,79378,79614,80161,80655,81260,81458,82038,82602,82977,83095,83633,83790,83986,84259,84515,84685,84826,84890,85255,85622,86298,86562,86900,87253,87347,87533,87839,88101,88226,88353,88592,88803,88922,89115,89292,89747,89928,90050,90309,90422,90609,90711,90818,90947,91222,91730,92226,93103,93397,93967,94116,94848,95020,95104,95440,95532,95810,102844,108233,108295,108873,109457,109548,117517,117746,117906,118058,118229,118395,118564,118731,118894,119137,119307,119480,119651,119925,120124,120329,120659,120743,120839,120935,121033,121133,121235,121337,121439,121541,121643,121743,121839,121951,122080,122203,122334,122465,122563,122677,122771,122911,123045,123141,123253,123353,123469,123565,123677,123777,123917,124053,124217,124347,124505,124655,124796,124940,125075,125187,125337,125465,125593,125729,125861,125991,126121,126233,126373,127659,127803,127941,128007,128097,128173,128277,128367,128469,128577,128685,128785,128865,128957,129055,129165,129243,129349,129441,129545,129655,129777,129940,130097,130177,130277,130367,130477,130567,130808,130902,131008,131100,131200,131312,131426,131542,131658,131752,131866,131978,132080,132200,132322,132404,132508,132628,132754,132852,132946,133034,133146,133262,133384,133496,133671,133787,133873,133965,134077,134201,134268,134394,134462,134590,134734,134862,134931,135026,135141,135254,135353,135462,135573,135684,135785,135890,135990,136120,136211,136334,136428,136540,136626,136730,136826,136914,137032,137136,137240,137366,137454,137562,137662,137752,137862,137946,138048,138132,138186,138250,138356,138442,138552,138636,138756,142276,142394,142509,142589,142950,143183,144053,146284,147645,148033,150808,160712,161032,163294,169322,173802,174064,174264,176797,181075,181681,182158,182309,186981,188859,190040,195154,196996,199127,199467,200778,200981"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\71303a5a371ce3bcd89a8ecc89eb33fe\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "122,128,134,266,267,268,269,366,1959,1961,1962,1967,1969", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6388,6846,7249,16333,16386,16439,16492,21843,126696,126872,126994,127256,127451", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "6472,6912,7317,16381,16434,16487,16540,21898,126757,126989,127050,127317,127513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\07c8d82ce007794779a5c5ed16d7cd9e\\transformed\\jetified-play-services-base-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "90,91,92,93,94,95,96,97,407,408,409,410,411,412,413,414,416,417,418,419,420,421,422,423,424,3116,3589", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4232,4322,4402,4492,4582,4662,4743,4823,24684,24789,24970,25095,25202,25382,25505,25621,25891,26079,26184,26365,26490,26665,26813,26876,26938,174601,189316", "endLines": "90,91,92,93,94,95,96,97,407,408,409,410,411,412,413,414,416,417,418,419,420,421,422,423,424,3128,3607", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4317,4397,4487,4577,4657,4738,4818,4898,24784,24965,25090,25197,25377,25500,25616,25719,26074,26179,26360,26485,26660,26808,26871,26933,27012,174911,189728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4560e8224758ac8d8fc066fe7120503d\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "320,321,326,333,334,353,354,355,356,357", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19481,19521,19738,20076,20131,21148,21202,21254,21303,21364", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19516,19563,19776,20126,20173,21197,21249,21298,21359,21409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\88e3d0d424506a1c1cef1c33bd4edbb3\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "368,415", "startColumns": "4,4", "startOffsets": "21960,25724", "endColumns": "67,166", "endOffsets": "22023,25886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cf97fcc817d260f850a9811dd743a790\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "399", "startColumns": "4", "startOffsets": "24090", "endColumns": "82", "endOffsets": "24168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3d03fe9731b2e1b01913ae2b47f6c626\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "231,232,233,241,242,243,323,3521", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "13925,13984,14032,14699,14774,14850,19602,186986", "endLines": "231,232,233,241,242,243,323,3540", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "13979,14027,14083,14769,14845,14917,19663,187776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d053047d2b84b3a6f7afaef38f7da9f\\transformed\\jetified-android-maps-utils-3.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,7,10", "startColumns": "4,4,4,4", "startOffsets": "55,93,301,461", "endLines": "2,6,9,14", "endColumns": "37,12,12,12", "endOffsets": "88,296,456,708"}, "to": {"startLines": "317,2117,2121,2124", "startColumns": "4,4,4,4", "startOffsets": "19326,139045,139253,139413", "endLines": "317,2120,2123,2128", "endColumns": "37,12,12,12", "endOffsets": "19359,139248,139408,139660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\988e907a890df94d30db83a31844424d\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2254,2270,2276,3659,3675", "startColumns": "4,4,4,4,4", "startOffsets": "144058,144483,144661,191595,192006", "endLines": "2269,2275,2285,3674,3678", "endColumns": "24,24,24,24,24", "endOffsets": "144478,144656,144940,192001,192128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\883da05e951611f8406b16df7b2c9aa9\\transformed\\work-runtime-2.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "64,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "2273,2338,2408,2472", "endColumns": "64,69,63,60", "endOffsets": "2333,2403,2467,2528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\47ac933c65727cbf2c22795a6a720497\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,123,259,260,261,262,263,264,265,327,328,329,369,370,425,426,427,428,433,434,435,1508,1692,1695,1701,1707,1710,1716,1720,1723,1730,1736,1739,1745,1750,1755,1762,1764,1770,1776,1784,1789,1796,1801,1807,1811,1818,1822,1828,1834,1837,1841,1842,2772,2787,2926,2964,3106,3357,3375,3439,3449,3459,3466,3472,3576,3745,3762", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6477,15900,15964,16019,16087,16154,16219,16276,19781,19829,19877,22028,22091,27017,27055,27112,27156,27420,27559,27609,95815,109553,109658,109903,110241,110387,110727,110939,111102,111509,111847,111970,112309,112548,112805,113176,113236,113574,113860,114309,114601,114989,115294,115638,115883,116213,116420,116688,116961,117105,117306,117353,160717,161240,168026,169327,174269,181686,182314,184239,184521,184826,185088,185348,188864,195159,195689", "endLines": "63,123,259,260,261,262,263,264,265,327,328,329,369,370,425,426,427,430,433,434,435,1524,1694,1700,1706,1709,1715,1719,1722,1729,1735,1738,1744,1749,1754,1761,1763,1769,1775,1783,1788,1795,1800,1806,1810,1817,1821,1827,1833,1836,1840,1841,1842,2776,2797,2945,2967,3115,3364,3438,3448,3458,3465,3471,3514,3588,3761,3778", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,6541,15959,16014,16082,16149,16214,16271,16328,19824,19872,19933,22086,22149,27050,27107,27151,27291,27554,27604,27652,97248,109653,109898,110236,110382,110722,110934,111097,111504,111842,111965,112304,112543,112800,113171,113231,113569,113855,114304,114596,114984,115289,115633,115878,116208,116415,116683,116956,117100,117301,117348,117404,160897,161636,168750,169471,174596,181929,184234,184516,184821,185083,185343,186766,189311,195684,196252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4f9d4625548a64e0918456efe245c9bb\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,74,75,88,89,120,121,222,223,224,225,226,227,228,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,324,325,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,371,400,401,402,403,404,405,406,432,1954,1955,1960,1963,1968,2112,2113,2781,2798,2968,3001,3031,3064", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2981,3053,4101,4166,6256,6325,13264,13334,13402,13474,13544,13605,13679,14922,14983,15044,15106,15170,15232,15293,15361,15461,15521,15587,15660,15729,15786,15838,16998,17070,17146,17211,17270,17329,17389,17449,17509,17569,17629,17689,17749,17809,17869,17929,17988,18048,18108,18168,18228,18288,18348,18408,18468,18528,18588,18647,18707,18767,18826,18885,18944,19003,19062,19668,19703,20289,20344,20407,20462,20520,20578,20639,20702,20759,20810,20860,20921,20978,21044,21078,21113,22154,24173,24240,24312,24381,24450,24524,24596,27349,126378,126495,126762,127055,127322,138761,138833,161037,161641,169476,171207,172207,172889", "endLines": "29,74,75,88,89,120,121,222,223,224,225,226,227,228,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,324,325,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,371,400,401,402,403,404,405,406,432,1954,1958,1960,1966,1968,2112,2113,2786,2807,3000,3021,3063,3069", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,3048,3136,4161,4227,6320,6383,13329,13397,13469,13539,13600,13674,13747,14978,15039,15101,15165,15227,15288,15356,15456,15516,15582,15655,15724,15781,15833,15895,17065,17141,17206,17265,17324,17384,17444,17504,17564,17624,17684,17744,17804,17864,17924,17983,18043,18103,18163,18223,18283,18343,18403,18463,18523,18583,18642,18702,18762,18821,18880,18939,18998,19057,19116,19698,19733,20339,20402,20457,20515,20573,20634,20697,20754,20805,20855,20916,20973,21039,21073,21108,21143,22219,24235,24307,24376,24445,24519,24591,24679,27415,126490,126691,126867,127251,127446,138828,138895,161235,161937,171202,171883,172884,173051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\28517815d8c8a576bd2413afe5492a2c\\transformed\\jetified-play-services-maps-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "167", "endLines": "66", "endColumns": "20", "endOffsets": "1669"}, "to": {"startLines": "3129", "startColumns": "4", "startOffsets": "174916", "endLines": "3191", "endColumns": "20", "endOffsets": "176418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\df93ef38b220bb54f4bc5fec3f1dd8ec\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "361", "startColumns": "4", "startOffsets": "21549", "endColumns": "53", "endOffsets": "21598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\483a6b61ae086fccd0e7a62adbbb50b3\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2114,2836,2842", "startColumns": "4,4,4,4", "startOffsets": "164,138900,163299,163510", "endLines": "3,2116,2841,2925", "endColumns": "60,12,24,24", "endOffsets": "220,139040,163505,168021"}}]}]}