# حالة المحلات (مفتوح/مغلق) في واجهة المحلات
## Store Status (Open/Closed) Implementation

## 🎯 **نظرة عامة**
تم إضافة حالة المحل (مفتوح/مغلق) في واجهة المحلات مع ألوان مميزة لكل حالة لتوضيح حالة المحل للمستخدم.

## ✨ **الميزة المضافة**

### **🏪 حالة المحل:**
- **🟢 مفتوح:** خلفية خضراء مع نص أبيض
- **🔴 مغلق:** خلفية حمراء مع نص أبيض
- **📍 الموضع:** في الصف العلوي بجانب نوع المتجر
- **🎨 التصميم:** شكل دائري مع حشو مناسب

## 🔧 **التطبيق التقني**

### **1. إضافة حالة المحل في DataManager:**
```dart
// قائمة المتاجر مع حالة المحل
static const List<Map<String, dynamic>> allStores = [
  {
    "name": "مطعم الشرق",
    "category": "مطاعم",
    "rating": 4.8,
    "deliveryTime": "30-45 دقيقة",
    "image": "images/1.png",
    "isOpen": true, // مفتوح ✅
  },
  {
    "name": "سوبرماركت الأمانة",
    "category": "بقالة",
    "rating": 4.5,
    "deliveryTime": "20-30 دقيقة",
    "image": "images/2.png",
    "isOpen": false, // مغلق ✅
  },
  {
    "name": "صيدلية الصحة",
    "category": "صيدليات",
    "rating": 4.7,
    "deliveryTime": "15-25 دقيقة",
    "image": "images/3.png",
    "isOpen": true, // مفتوح ✅
  },
  // ... باقي المتاجر
];
```

### **2. عرض حالة المحل في StoreCard:**
```dart
// الصف العلوي - نوع المتجر وحالة المحل والمفضلة
Row(
  mainAxisAlignment: MainAxisAlignment.spaceBetween,
  children: [
    Row(
      children: [
        // نوع المتجر
        Container(
          padding: const EdgeInsets.all(5),
          decoration: BoxDecoration(
            color: const Color(0xFF4C53A5),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Text(
            "متجر",
            style: TextStyle(
              fontSize: 12,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        const SizedBox(width: 8),
        
        // حالة المحل (مفتوح/مغلق) ✅
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: (store["isOpen"] ?? false) 
                ? Colors.green  // أخضر للمفتوح ✅
                : Colors.red,   // أحمر للمغلق ✅
            borderRadius: BorderRadius.circular(15),
          ),
          child: Text(
            (store["isOpen"] ?? false) ? "مفتوح" : "مغلق",
            style: const TextStyle(
              fontSize: 10,
              color: Colors.white, // نص أبيض ✅
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    ),
    const Icon(
      Icons.favorite_border,
      color: Colors.red,
    )
  ],
),
```

## 📊 **حالة كل متجر**

### **🟢 المتاجر المفتوحة:**
| المتجر | التصنيف | الحالة | اللون |
|--------|---------|--------|-------|
| مطعم الشرق | مطاعم | مفتوح | 🟢 أخضر |
| صيدلية الصحة | صيدليات | مفتوح | 🟢 أخضر |
| متجر التقنية | إلكترونيات | مفتوح | 🟢 أخضر |
| حلويات دمشق | حلويات | مفتوح | 🟢 أخضر |

### **🔴 المتاجر المغلقة:**
| المتجر | التصنيف | الحالة | اللون |
|--------|---------|--------|-------|
| سوبرماركت الأمانة | بقالة | مغلق | 🔴 أحمر |
| متجر الأناقة | ملابس | مغلق | 🔴 أحمر |
| مخبز الأسرة | مخابز | مغلق | 🔴 أحمر |
| مطعم البرجر | مطاعم | مغلق | 🔴 أحمر |

## 🎨 **التصميم البصري**

### **🟢 المحل المفتوح:**
```
┌─────────────────────────────────┐
│ [متجر] [مفتوح]           ♡     │
│                                 │
│         [صورة المتجر]           │
│                                 │
│ اسم المتجر                      │
│ تصنيف المتجر                    │
│ ⭐ 4.8        30-45 دقيقة      │
└─────────────────────────────────┘
```

### **🔴 المحل المغلق:**
```
┌─────────────────────────────────┐
│ [متجر] [مغلق]            ♡     │
│                                 │
│         [صورة المتجر]           │
│                                 │
│ اسم المتجر                      │
│ تصنيف المتجر                    │
│ ⭐ 4.5        20-30 دقيقة      │
└─────────────────────────────────┘
```

## 🎯 **مواصفات التصميم**

### **🟢 حالة "مفتوح":**
- **اللون:** `Colors.green` (أخضر)
- **النص:** "مفتوح" باللون الأبيض
- **الخط:** حجم 10، عريض
- **الشكل:** دائري مع حشو 8×4

### **🔴 حالة "مغلق":**
- **اللون:** `Colors.red` (أحمر)
- **النص:** "مغلق" باللون الأبيض
- **الخط:** حجم 10، عريض
- **الشكل:** دائري مع حشو 8×4

### **📐 الأبعاد:**
```dart
Container(
  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
  decoration: BoxDecoration(
    color: isOpen ? Colors.green : Colors.red,
    borderRadius: BorderRadius.circular(15), // شكل دائري
  ),
  child: Text(
    isOpen ? "مفتوح" : "مغلق",
    style: const TextStyle(
      fontSize: 10,        // حجم الخط
      color: Colors.white,  // لون النص
      fontWeight: FontWeight.bold, // عريض
    ),
  ),
),
```

## 🚀 **الفوائد المحققة**

### **1. وضوح المعلومات:**
- ✅ **معرفة فورية:** المستخدم يعرف حالة المحل فوراً
- ✅ **ألوان واضحة:** أخضر للمفتوح، أحمر للمغلق
- ✅ **نص واضح:** كلمات "مفتوح" و "مغلق" باللغة العربية

### **2. تجربة مستخدم محسنة:**
- ✅ **توفير الوقت:** عدم محاولة الطلب من محل مغلق
- ✅ **تخطيط أفضل:** معرفة المحلات المتاحة للطلب
- ✅ **تجنب الإحباط:** عدم اكتشاف إغلاق المحل بعد اختيار المنتجات

### **3. تصميم احترافي:**
- ✅ **تناسق بصري:** ألوان متناسقة مع التطبيق
- ✅ **وضوح المعلومات:** معلومات مهمة في مكان بارز
- ✅ **سهولة الفهم:** رموز ألوان عالمية (أخضر=مفتوح، أحمر=مغلق)

## 📱 **تجربة المستخدم**

### **🎯 السيناريو الكامل:**
1. **المستخدم يفتح التطبيق:** يرى قائمة المحلات
2. **يتصفح المحلات:** يرى حالة كل محل بوضوح
3. **يختار محل مفتوح:** يرى "مفتوح" بخلفية خضراء
4. **يتجنب المحلات المغلقة:** يرى "مغلق" بخلفية حمراء
5. **يتخذ قرار مدروس:** يطلب من المحلات المفتوحة فقط

### **🔄 التفاعل مع الحالات:**
- **محل مفتوح:** يمكن النقر والطلب بشكل طبيعي
- **محل مغلق:** يمكن النقر لرؤية التفاصيل لكن مع تنبيه أنه مغلق

## 🔄 **إدارة الحالات**

### **📊 توزيع الحالات الحالي:**
- **🟢 مفتوح:** 4 متاجر (50%)
- **🔴 مغلق:** 4 متاجر (50%)

### **⏰ إمكانيات التطوير المستقبلية:**
1. **أوقات العمل:** ربط الحالة بأوقات العمل الفعلية
2. **تحديث تلقائي:** تحديث الحالة حسب الوقت الحالي
3. **إشعارات:** إشعار المستخدم عند فتح محل مفضل
4. **فلترة:** إمكانية فلترة المحلات المفتوحة فقط

## 🎨 **التناسق مع التطبيق**

### **🎨 الألوان المستخدمة:**
- **أخضر:** `Colors.green` - لون عالمي للحالة الإيجابية
- **أحمر:** `Colors.red` - لون عالمي للحالة السلبية
- **أبيض:** `Colors.white` - لون النص للوضوح
- **أزرق:** `Color(0xFF4C53A5)` - لون التطبيق الأساسي

### **📐 التناسق مع العناصر الأخرى:**
- **نفس شكل نوع المتجر:** شكل دائري متناسق
- **نفس حجم الخط:** 10-12 بكسل
- **نفس الحشو:** 8×4 بكسل
- **نفس الهوامش:** 8 بكسل بين العناصر

## 🎉 **الخلاصة**

### **✅ تم إنجازه:**
- ✅ **إضافة حالة المحل** لجميع المتاجر في البيانات
- ✅ **عرض الحالة بصرياً** في واجهة المحلات
- ✅ **استخدام الألوان المطلوبة** (أخضر للمفتوح، أحمر للمغلق)
- ✅ **نص أبيض واضح** على الخلفيات الملونة
- ✅ **تصميم متناسق** مع باقي عناصر التطبيق

### **🎯 النتيجة:**
واجهة المحلات في تطبيق "زاد اليمن" أصبحت:
- **أكثر وضوحاً** مع عرض حالة كل محل
- **أكثر فائدة** للمستخدم في اتخاذ القرارات
- **أكثر احترافية** مع ألوان واضحة ومفهومة
- **أسهل في الاستخدام** مع معلومات مرئية فورية

🏪 **المستخدمون الآن يعرفون حالة كل محل بوضوح!**

هذا يساعدهم في اتخاذ قرارات أفضل وتجنب محاولة الطلب من محلات مغلقة، مما يحسن تجربة الاستخدام بشكل كبير.
