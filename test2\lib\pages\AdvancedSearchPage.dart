import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/SearchProvider.dart';
import '../widgets/ProductCard.dart';
import '../widgets/StoreCard.dart';
import '../widgets/SearchFiltersSheet.dart';
import '../widgets/SearchSortSheet.dart';
import '../widgets/CustomSnackBars.dart';

/// صفحة البحث المتقدمة
class AdvancedSearchPage extends StatefulWidget {
  final String? initialQuery;

  const AdvancedSearchPage({Key? key, this.initialQuery}) : super(key: key);

  @override
  _AdvancedSearchPageState createState() => _AdvancedSearchPageState();
}

class _AdvancedSearchPageState extends State<AdvancedSearchPage>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocus = FocusNode();
  
  late TabController _tabController;
  bool _showSuggestions = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    
    // تهيئة البحث
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final searchProvider = Provider.of<SearchProvider>(context, listen: false);
      searchProvider.initializeSearch();
      
      // إذا كان هناك استعلام أولي
      if (widget.initialQuery != null && widget.initialQuery!.isNotEmpty) {
        _searchController.text = widget.initialQuery!;
        searchProvider.startSearch(widget.initialQuery!);
      }
    });

    // مراقبة تغييرات النص
    _searchController.addListener(_onSearchTextChanged);
    _searchFocus.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocus.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _onSearchTextChanged() {
    final query = _searchController.text;
    final searchProvider = Provider.of<SearchProvider>(context, listen: false);
    
    if (query.isNotEmpty) {
      searchProvider.updateSuggestions(query);
    }
  }

  void _onFocusChanged() {
    setState(() {
      _showSuggestions = _searchFocus.hasFocus && _searchController.text.isNotEmpty;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(),
      body: Consumer<SearchProvider>(
        builder: (context, searchProvider, child) {
          return Column(
            children: [
              // شريط البحث
              _buildSearchBar(searchProvider),
              
              // الاقتراحات أو النتائج
              Expanded(
                child: _showSuggestions
                    ? _buildSuggestions(searchProvider)
                    : _buildSearchResults(searchProvider),
              ),
            ],
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Color(0xFF4C53A5),
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'البحث المتقدم',
        style: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        Consumer<SearchProvider>(
          builder: (context, searchProvider, child) {
            return PopupMenuButton<String>(
              icon: Icon(Icons.more_vert, color: Colors.white),
              onSelected: (value) {
                switch (value) {
                  case 'clear_history':
                    _clearSearchHistory(searchProvider);
                    break;
                  case 'reset_filters':
                    searchProvider.resetFilters();
                    CustomSnackBars.showSuccess(context, message: 'تم إعادة تعيين الفلاتر');
                    break;
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'clear_history',
                  child: Row(
                    children: [
                      Icon(Icons.history, color: Colors.grey[600]),
                      SizedBox(width: 8),
                      Text('مسح تاريخ البحث'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'reset_filters',
                  child: Row(
                    children: [
                      Icon(Icons.filter_alt_off, color: Colors.grey[600]),
                      SizedBox(width: 8),
                      Text('إعادة تعيين الفلاتر'),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildSearchBar(SearchProvider searchProvider) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Color(0xFF4C53A5),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // حقل البحث
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocus,
              decoration: InputDecoration(
                hintText: 'ابحث عن المنتجات والمتاجر...',
                prefixIcon: Icon(Icons.search, color: Color(0xFF4C53A5)),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear, color: Colors.grey),
                        onPressed: () {
                          _searchController.clear();
                          searchProvider.clearSearch();
                          setState(() {
                            _showSuggestions = false;
                          });
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
              ),
              onSubmitted: (query) {
                if (query.trim().isNotEmpty) {
                  searchProvider.startSearch(query);
                  _searchFocus.unfocus();
                  setState(() {
                    _showSuggestions = false;
                  });
                }
              },
            ),
          ),
          
          SizedBox(height: 12),
          
          // أزرار الفلترة والترتيب
          Row(
            children: [
              Expanded(
                child: _buildFilterButton(searchProvider),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildSortButton(searchProvider),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButton(SearchProvider searchProvider) {
    final hasActiveFilters = searchProvider.currentFilters.categories.isNotEmpty ||
        searchProvider.currentFilters.minPrice != null ||
        searchProvider.currentFilters.maxPrice != null ||
        searchProvider.currentFilters.minRating != null ||
        searchProvider.currentFilters.availableOnly;

    return ElevatedButton.icon(
      onPressed: () => _showFiltersSheet(searchProvider),
      icon: Icon(
        Icons.filter_list,
        color: hasActiveFilters ? Colors.white : Color(0xFF4C53A5),
        size: 18,
      ),
      label: Text(
        'فلترة',
        style: TextStyle(
          color: hasActiveFilters ? Colors.white : Color(0xFF4C53A5),
          fontWeight: FontWeight.w600,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: hasActiveFilters ? Colors.orange : Colors.white,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }

  Widget _buildSortButton(SearchProvider searchProvider) {
    final sortOption = searchProvider.getSortOptions()
        .firstWhere((option) => option.option == searchProvider.currentSort);

    return ElevatedButton.icon(
      onPressed: () => _showSortSheet(searchProvider),
      icon: Icon(
        sortOption.icon,
        color: Color(0xFF4C53A5),
        size: 18,
      ),
      label: Text(
        'ترتيب',
        style: TextStyle(
          color: Color(0xFF4C53A5),
          fontWeight: FontWeight.w600,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }

  Widget _buildSuggestions(SearchProvider searchProvider) {
    if (searchProvider.suggestions.isEmpty && searchProvider.recentSearches.isEmpty) {
      return _buildEmptyState('لا توجد اقتراحات', Icons.search_off);
    }

    return ListView(
      padding: EdgeInsets.all(16),
      children: [
        // الاقتراحات
        if (searchProvider.suggestions.isNotEmpty) ...[
          _buildSectionHeader('اقتراحات البحث', Icons.lightbulb_outline),
          ...searchProvider.suggestions.map((suggestion) =>
              _buildSuggestionItem(suggestion, searchProvider, false)),
          SizedBox(height: 20),
        ],

        // البحثات الأخيرة
        if (searchProvider.recentSearches.isNotEmpty) ...[
          _buildSectionHeader('البحثات الأخيرة', Icons.history),
          ...searchProvider.recentSearches.map((search) =>
              _buildSuggestionItem(search, searchProvider, true)),
          SizedBox(height: 20),
        ],

        // البحثات الشائعة
        if (searchProvider.popularSearches.isNotEmpty) ...[
          _buildSectionHeader('البحثات الشائعة', Icons.trending_up),
          ...searchProvider.popularSearches.map((search) =>
              _buildSuggestionItem(search, searchProvider, false)),
        ],
      ],
    );
  }

  Widget _buildSearchResults(SearchProvider searchProvider) {
    if (searchProvider.isLoading) {
      return _buildLoadingState();
    }

    if (searchProvider.error != null) {
      return _buildErrorState(searchProvider.error!, searchProvider);
    }

    if (!searchProvider.isSearching) {
      return _buildWelcomeState(searchProvider);
    }

    if (!searchProvider.hasResults) {
      return _buildNoResultsState(searchProvider.currentQuery);
    }

    return Column(
      children: [
        // معلومات النتائج
        _buildResultsInfo(searchProvider),
        
        // التبويبات
        _buildTabBar(),
        
        // محتوى التبويبات
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildProductResults(searchProvider),
              _buildStoreResults(searchProvider),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: Color(0xFF4C53A5), size: 20),
          SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionItem(String text, SearchProvider searchProvider, bool isHistory) {
    return ListTile(
      leading: Icon(
        isHistory ? Icons.history : Icons.search,
        color: Colors.grey[600],
        size: 20,
      ),
      title: Text(
        text,
        style: TextStyle(fontSize: 14),
      ),
      trailing: isHistory
          ? IconButton(
              icon: Icon(Icons.close, color: Colors.grey[400], size: 18),
              onPressed: () => searchProvider.removeFromHistory(text),
            )
          : Icon(Icons.north_west, color: Colors.grey[400], size: 16),
      onTap: () {
        _searchController.text = text;
        searchProvider.startSearch(text);
        _searchFocus.unfocus();
        setState(() {
          _showSuggestions = false;
        });
      },
    );
  }

  Widget _buildResultsInfo(SearchProvider searchProvider) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Row(
        children: [
          Text(
            'النتائج: ${searchProvider.totalResults}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF4C53A5),
            ),
          ),
          Spacer(),
          Text(
            'البحث عن: "${searchProvider.currentQuery}"',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Consumer<SearchProvider>(
      builder: (context, searchProvider, child) {
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(25),
          ),
          child: TabBar(
            controller: _tabController,
            indicator: BoxDecoration(
              color: Color(0xFF4C53A5),
              borderRadius: BorderRadius.circular(25),
            ),
            labelColor: Colors.white,
            unselectedLabelColor: Colors.grey[600],
            labelStyle: TextStyle(fontWeight: FontWeight.w600),
            tabs: [
              Tab(
                text: 'المنتجات (${searchProvider.productResults.length})',
              ),
              Tab(
                text: 'المتاجر (${searchProvider.storeResults.length})',
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProductResults(SearchProvider searchProvider) {
    if (searchProvider.productResults.isEmpty) {
      return _buildEmptyState('لا توجد منتجات', Icons.shopping_bag_outlined);
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: searchProvider.productResults.length,
      itemBuilder: (context, index) {
        final product = searchProvider.productResults[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 12),
          child: ProductCard(product: product),
        );
      },
    );
  }

  Widget _buildStoreResults(SearchProvider searchProvider) {
    if (searchProvider.storeResults.isEmpty) {
      return _buildEmptyState('لا توجد متاجر', Icons.store_outlined);
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: searchProvider.storeResults.length,
      itemBuilder: (context, index) {
        final store = searchProvider.storeResults[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 12),
          child: StoreCard(store: store),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4C53A5)),
          ),
          SizedBox(height: 16),
          Text(
            'جاري البحث...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error, SearchProvider searchProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[300],
          ),
          SizedBox(height: 16),
          Text(
            'حدث خطأ',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red[700],
            ),
          ),
          SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              searchProvider.clearError();
              if (searchProvider.currentQuery.isNotEmpty) {
                searchProvider.startSearch(searchProvider.currentQuery);
              }
            },
            child: Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFF4C53A5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeState(SearchProvider searchProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            'ابحث عن المنتجات والمتاجر',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'استخدم شريط البحث أعلاه للعثور على ما تريد',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          if (searchProvider.popularSearches.isNotEmpty) ...[
            SizedBox(height: 24),
            Text(
              'البحثات الشائعة:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: searchProvider.popularSearches.take(6).map((search) =>
                  ActionChip(
                    label: Text(search),
                    onPressed: () {
                      _searchController.text = search;
                      searchProvider.startSearch(search);
                    },
                    backgroundColor: Color(0xFF4C53A5).withOpacity(0.1),
                    labelStyle: TextStyle(color: Color(0xFF4C53A5)),
                  )).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNoResultsState(String query) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد نتائج',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'لم نجد أي نتائج لـ "$query"',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 20),
          Text(
            'جرب:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            '• تحقق من الإملاء\n• استخدم كلمات أقل أو مختلفة\n• استخدم مصطلحات أكثر عمومية',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 48,
            color: Colors.grey[400],
          ),
          SizedBox(height: 12),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  void _showFiltersSheet(SearchProvider searchProvider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SearchFiltersSheet(
        currentFilters: searchProvider.currentFilters,
        onApplyFilters: (filters) {
          searchProvider.applyFilters(filters);
          Navigator.pop(context);
        },
      ),
    );
  }

  void _showSortSheet(SearchProvider searchProvider) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => SearchSortSheet(
        currentSort: searchProvider.currentSort,
        sortOptions: searchProvider.getSortOptions(),
        onSortSelected: (sortOption) {
          searchProvider.applySorting(sortOption);
          Navigator.pop(context);
        },
      ),
    );
  }

  void _clearSearchHistory(SearchProvider searchProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('مسح تاريخ البحث'),
        content: Text('هل أنت متأكد من مسح جميع البحثات السابقة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              searchProvider.clearSearchHistory();
              Navigator.pop(context);
              CustomSnackBars.showSuccess(context, message: 'تم مسح تاريخ البحث');
            },
            child: Text('مسح'),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
          ),
        ],
      ),
    );
  }
}
