import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:test2/providers/CartProvider.dart';
import 'package:test2/utils/AppColors.dart';
import 'package:test2/pages/CartPage.dart';

/// أيقونة السلة مع عداد العناصر
class CartIcon extends StatelessWidget {
  final Color? iconColor;
  final double? iconSize;
  final bool showBadge;
  final VoidCallback? onTap;

  const CartIcon({
    Key? key,
    this.iconColor,
    this.iconSize,
    this.showBadge = true,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<CartProvider>(
      builder: (context, cartProvider, child) {
        return GestureDetector(
          onTap: onTap ?? () => _navigateToCart(context),
          child: Container(
            padding: EdgeInsets.all(8.w),
            child: Stack(
              children: [
                // أيقونة السلة
                Icon(
                  Icons.shopping_cart_outlined,
                  color: iconColor ?? AppColors.textPrimaryColor,
                  size: iconSize ?? 28.sp,
                ),
                
                // عداد العناصر
                if (showBadge && cartProvider.totalQuantity > 0)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: AnimatedContainer(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.elasticOut,
                      padding: EdgeInsets.symmetric(
                        horizontal: cartProvider.totalQuantity > 9 ? 6.w : 8.w,
                        vertical: 2.h,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.primaryColor,
                        borderRadius: BorderRadius.circular(12.r),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primaryColor.withOpacity(0.3),
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      constraints: BoxConstraints(
                        minWidth: 20.w,
                        minHeight: 20.h,
                      ),
                      child: Text(
                        cartProvider.totalQuantity > 99 
                            ? '99+' 
                            : cartProvider.totalQuantity.toString(),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// الانتقال إلى صفحة السلة
  void _navigateToCart(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CartPage(),
      ),
    );
  }
}

/// أيقونة السلة للشريط العلوي
class AppBarCartIcon extends StatelessWidget {
  final Color? iconColor;

  const AppBarCartIcon({
    Key? key,
    this.iconColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CartIcon(
      iconColor: iconColor ?? Colors.white,
      iconSize: 24.sp,
      showBadge: true,
    );
  }
}

/// أيقونة السلة للشريط السفلي
class BottomNavCartIcon extends StatelessWidget {
  final bool isSelected;

  const BottomNavCartIcon({
    Key? key,
    this.isSelected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<CartProvider>(
      builder: (context, cartProvider, child) {
        return Stack(
          children: [
            // أيقونة السلة
            Icon(
              isSelected ? Icons.shopping_cart : Icons.shopping_cart_outlined,
              color: isSelected ? AppColors.primaryColor : AppColors.textSecondaryColor,
              size: 24.sp,
            ),
            
            // عداد العناصر
            if (cartProvider.totalQuantity > 0)
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  padding: EdgeInsets.all(2.w),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    shape: BoxShape.circle,
                  ),
                  constraints: BoxConstraints(
                    minWidth: 16.w,
                    minHeight: 16.h,
                  ),
                  child: Text(
                    cartProvider.totalQuantity > 9 
                        ? '9+' 
                        : cartProvider.totalQuantity.toString(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

/// زر إضافة إلى السلة
class AddToCartButton extends StatefulWidget {
  final Product product;
  final int quantity;
  final VoidCallback? onAdded;
  final String? customText;
  final IconData? customIcon;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;

  const AddToCartButton({
    Key? key,
    required this.product,
    this.quantity = 1,
    this.onAdded,
    this.customText,
    this.customIcon,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  _AddToCartButtonState createState() => _AddToCartButtonState();
}

class _AddToCartButtonState extends State<AddToCartButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isAdding = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CartProvider>(
      builder: (context, cartProvider, child) {
        final isInCart = cartProvider.isProductInCart(widget.product.id);
        
        return AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                width: widget.width ?? double.infinity,
                height: widget.height ?? 48.h,
                child: ElevatedButton.icon(
                  onPressed: _isAdding ? null : () => _addToCart(context, cartProvider),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.backgroundColor ?? 
                        (isInCart ? Colors.green : AppColors.primaryColor),
                    foregroundColor: widget.textColor ?? Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    elevation: 2,
                  ),
                  icon: _isAdding
                      ? SizedBox(
                          width: 20.w,
                          height: 20.h,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              widget.textColor ?? Colors.white,
                            ),
                          ),
                        )
                      : Icon(
                          widget.customIcon ?? 
                              (isInCart ? Icons.check : Icons.add_shopping_cart),
                          size: 20.sp,
                        ),
                  label: Text(
                    _isAdding
                        ? 'جاري الإضافة...'
                        : widget.customText ?? 
                            (isInCart ? 'في السلة' : 'أضف للسلة'),
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// إضافة المنتج إلى السلة
  Future<void> _addToCart(BuildContext context, CartProvider cartProvider) async {
    setState(() => _isAdding = true);
    
    // تشغيل الحركة
    await _animationController.forward();
    await _animationController.reverse();
    
    try {
      final message = await cartProvider.addToCartWithMessage(
        widget.product,
        quantity: widget.quantity,
      );
      
      // عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
      );
      
      // استدعاء callback إذا كان موجود
      if (widget.onAdded != null) {
        widget.onAdded!();
      }
    } catch (e) {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إضافة المنتج: $e'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
    } finally {
      setState(() => _isAdding = false);
    }
  }
}
