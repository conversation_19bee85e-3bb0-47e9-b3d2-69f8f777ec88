import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:test2/providers/CartProvider.dart';
import 'package:test2/pages/CartPage.dart';
import 'package:badges/badges.dart' as badges;

class StoresAppBar extends StatelessWidget implements PreferredSizeWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color.fromARGB(255, 219, 55, 80),
      padding: EdgeInsets.all(25),
      child: Row(
        textDirection: TextDirection.rtl,
        children: [
          InkWell(
            onTap: () {
              // Navigator.pop(context);
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              } else {
                Navigator.pushReplacementNamed(context, "/home");
              }
            },
            child: Icon(
              Icons.arrow_forward,
              size: 30,
              color: Colors.white,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 20),
            child: Text(
              "المتاجر",
              style: TextStyle(
                fontSize: 23,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          Spacer(),
          // أيقونة السلة مع العداد
          Consumer<CartProvider>(
            builder: (context, cartProvider, child) {
              return badges.Badge(
                badgeStyle: badges.BadgeStyle(
                  badgeColor: Color.fromARGB(255, 245, 170, 73),
                  padding: EdgeInsets.all(4),
                ),
                showBadge: cartProvider.totalQuantity > 0,
                badgeContent: Text(
                  cartProvider.totalQuantity > 9
                      ? '9+'
                      : cartProvider.totalQuantity.toString(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => CartPage()),
                    );
                  },
                  child: Icon(
                    Icons.shopping_cart_outlined,
                    size: 28,
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),
          SizedBox(width: 10),
          Icon(
            Icons.notifications,
            size: 30,
            color: Color.fromARGB(255, 245, 170, 73),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(50);
}
