import 'package:flutter/material.dart';

class StoresAppBar extends StatelessWidget implements PreferredSizeWidget{
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFC3243B),
      padding: EdgeInsets.all(25),
      child: Row(
        textDirection: TextDirection.rtl,
        children: [
          InkWell(
            onTap: () {
              // Navigator.pop(context);
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }else{
                Navigator.pushReplacementNamed(context, "/home");
              }
            },
            child: Icon(
              Icons.arrow_forward,
              size: 30,
              color: Colors.white,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 20),
            child: Text(
              "المتاجر",
              style: TextStyle(
                fontSize: 23,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          Spacer(),
          Icon(
            Icons.notifications,
            size: 30,
            color: Color.fromARGB(255, 245, 170, 73),
          ),
        ],
      ),
    );
  }
  @override
  Size get preferredSize => Size.fromHeight(50);
}