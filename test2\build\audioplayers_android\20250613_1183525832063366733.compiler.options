"-Xallow-no-source-files" "-classpath" "F:\\FlutterApps\\augmentsAPP\\appss\\test2\\build\\audioplayers_android\\intermediates\\compile_r_class_jar\\debug\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\86397c7fb0ecfbb5eb1692c34123b85e\\transformed\\jetified-flutter_embedding_debug-1.0.0-b8800d88be4866db1b15f8b954ab2573bba9960f.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0ee275105abfafca710ee35b21365be5\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\66ea60f8a5fbfcae70ca8697b93ff057\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b7e2cc50faa28786e6043af0e4d14cdc\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dbabe4035f995888899f81ade6d0315c\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\eded37d9d88cc517c20b6537ce4b63eb\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\145ed5708b36c670ce5576adf1321daf\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1f2a1f404739bbf4884e9b526786356c\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f0a189a422103124df9c7e565546b1cb\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b04c5f6e034382e74624d480244aec4e\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\928246cb05317b5fcac17709957e72d7\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c5d64ee5aa1c9c3cfb2be6ded92241ca\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\431bb0361398bbafd464198e508ce92b\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e83b9229f5055d1cbf8cb2e5f78798ab\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4cb38f381ca92612a2e69f612bba933d\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfe434f5fd805b903fadda7718dabf07\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6026b0def2ac2e8a8fc6261fb94471c1\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac51147ae0a419cd7191a5ccf74e7026\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\82df96974659ac3deaaddffaff3f86e8\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3f55842bc85496806b5ae84566ee827e\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0056c97f482bc11858c39b50f5123a1c\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bdabcef6692b7357ba8b5f3927638b90\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd5c6970a18bfc4d422527a14d5bda1e\\transformed\\jetified-kotlinx-coroutines-android-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1e27e7f65bf3097075c2dd184ff0f9d4\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6d687b3738573581d511da0ec6d8b2d2\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7e31dc083741b0a58ae788bbcdb86998\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8f74114678afb65f13992e0e9755ad10\\transformed\\jetified-kotlin-stdlib-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\eb8244b7725c84b20eb2e143ccc88811\\transformed\\jetified-kotlin-stdlib-common-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6f2ab572deeafcc742379f9988d23297\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f1606a6bdd0d8ab258e27a7ffb7d04f3\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3045433908ecab438eefdfdf5f3ede59\\transformed\\jetified-tracing-1.2.0-api.jar;F:\\Android\\Sdk\\platforms\\android-33\\android.jar;F:\\Android\\Sdk\\build-tools\\33.0.1\\core-lambda-stubs.jar" "-d" "F:\\FlutterApps\\augmentsAPP\\appss\\test2\\build\\audioplayers_android\\tmp\\kotlin-classes\\debug" "-jvm-target" "1.8" "-module-name" "audioplayers_android_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\AudioContextAndroid.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\AudioplayersPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\ByteDataSource.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\player\\FocusManager.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\player\\MediaPlayerPlayer.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\player\\Player.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\player\\SoundPoolPlayer.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\player\\WrappedPlayer.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\PlayerMode.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\ReleaseMode.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\source\\BytesSource.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\source\\Source.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audioplayers_android-4.0.3\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\source\\UrlSource.kt"