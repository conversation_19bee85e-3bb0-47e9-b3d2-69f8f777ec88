import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:test2/utils/AppColors.dart';
import 'package:test2/utils/AppConfig.dart';
import 'package:test2/utils/AppState.dart';
import 'package:test2/pages/HomePages.dart';
import 'package:test2/pages/RegisterPage.dart';

class LoginPage extends StatefulWidget {
  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _rememberMe = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          child: Container(
            height: MediaQuery.of(context).size.height,
            child: Column(
              children: [
                // الجزء العلوي مع الشعار
                _buildHeader(),
                
                // نموذج تسجيل الدخول
                Expanded(
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: AppColors.backgroundColor,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(AppDimensions.borderRadiusXLarge),
                        topRight: Radius.circular(AppDimensions.borderRadiusXLarge),
                      ),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(AppDimensions.paddingLarge),
                      child: _buildLoginForm(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 200.h,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // شعار التطبيق
          Container(
            width: 100.w,
            height: 100.h,
            decoration: BoxDecoration(
              color: AppColors.whiteColor,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowColor,
                  blurRadius: 15,
                  spreadRadius: 3,
                  offset: Offset(0, 5),
                ),
              ],
            ),
            child: Icon(
              Icons.restaurant_menu,
              size: 50.sp,
              color: AppColors.primaryColor,
            ),
          ),
          
          SizedBox(height: 15.h),
          
          // اسم التطبيق
          Text(
            AppConfig.appName,
            style: TextStyle(
              fontSize: 28.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.whiteColor,
            ),
          ),
          
          SizedBox(height: 5.h),
          
          Text(
            "مرحباً بك مرة أخرى",
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.whiteColor.withOpacity(0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SizedBox(height: 20.h),
          
          // عنوان تسجيل الدخول
          Text(
            "تسجيل الدخول",
            textAlign: TextAlign.center,
            style: AppTextStyles.titleLarge,
          ),
          
          SizedBox(height: 30.h),
          
          // حقل البريد الإلكتروني
          _buildEmailField(),
          
          SizedBox(height: 20.h),
          
          // حقل كلمة المرور
          _buildPasswordField(),
          
          SizedBox(height: 15.h),
          
          // تذكرني ونسيت كلمة المرور
          _buildRememberAndForgot(),
          
          SizedBox(height: 30.h),
          
          // زر تسجيل الدخول
          _buildLoginButton(),
          
          SizedBox(height: 20.h),
          
          // أو
          _buildDivider(),
          
          SizedBox(height: 20.h),
          
          // تسجيل الدخول بـ Google
          _buildGoogleLoginButton(),
          
          SizedBox(height: 30.h),
          
          // رابط التسجيل
          _buildSignUpLink(),
        ],
      ),
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      textAlign: TextAlign.right,
      decoration: InputDecoration(
        labelText: "البريد الإلكتروني",
        hintText: "أدخل بريدك الإلكتروني",
        prefixIcon: Icon(Icons.email_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusSmall),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusSmall),
          borderSide: BorderSide(color: AppColors.primaryColor, width: 2),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال البريد الإلكتروني';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return 'يرجى إدخال بريد إلكتروني صحيح';
        }
        return null;
      },
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: !_isPasswordVisible,
      textAlign: TextAlign.right,
      decoration: InputDecoration(
        labelText: "كلمة المرور",
        hintText: "أدخل كلمة المرور",
        prefixIcon: Icon(Icons.lock_outlined),
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: () {
            setState(() {
              _isPasswordVisible = !_isPasswordVisible;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusSmall),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusSmall),
          borderSide: BorderSide(color: AppColors.primaryColor, width: 2),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال كلمة المرور';
        }
        if (value.length < 6) {
          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
        return null;
      },
    );
  }

  Widget _buildRememberAndForgot() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        TextButton(
          onPressed: () {
            // فتح صفحة نسيت كلمة المرور
          },
          child: Text(
            "نسيت كلمة المرور؟",
            style: TextStyle(color: AppColors.primaryColor),
          ),
        ),
        Row(
          children: [
            Text("تذكرني"),
            Checkbox(
              value: _rememberMe,
              onChanged: (value) {
                setState(() {
                  _rememberMe = value ?? false;
                });
              },
              activeColor: AppColors.primaryColor,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return ElevatedButton(
      onPressed: _handleLogin,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primaryColor,
        padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingMedium),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusSmall),
        ),
      ),
      child: Text(
        "تسجيل الدخول",
        style: AppTextStyles.buttonText,
      ),
    );
  }

  Widget _buildDivider() {
    return Row(
      children: [
        Expanded(child: Divider()),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Text(
            "أو",
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondaryColor,
            ),
          ),
        ),
        Expanded(child: Divider()),
      ],
    );
  }

  Widget _buildGoogleLoginButton() {
    return OutlinedButton.icon(
      onPressed: () {
        // تسجيل الدخول بـ Google
      },
      icon: Icon(Icons.g_mobiledata, color: AppColors.primaryColor),
      label: Text(
        "تسجيل الدخول بـ Google",
        style: TextStyle(color: AppColors.primaryColor),
      ),
      style: OutlinedButton.styleFrom(
        padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingMedium),
        side: BorderSide(color: AppColors.primaryColor),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusSmall),
        ),
      ),
    );
  }

  Widget _buildSignUpLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "ليس لديك حساب؟ ",
          style: AppTextStyles.bodyMedium,
        ),
        TextButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => RegisterPage()),
            );
          },
          child: Text(
            "إنشاء حساب جديد",
            style: TextStyle(
              color: AppColors.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  void _handleLogin() {
    if (_formKey.currentState!.validate()) {
      // محاكاة عملية تسجيل الدخول
      AppStateHelper.showLoading();
      
      Future.delayed(Duration(seconds: 2), () {
        AppStateHelper.hideLoading();
        
        // محاكاة بيانات المستخدم
        Map<String, dynamic> userData = {
          'id': '1',
          'name': 'أحمد محمد علي',
          'email': _emailController.text,
          'phone': '+967 777 123 456',
          'avatar': null,
        };
        
        appState.login(userData);
        
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => HomePage()),
        );
      });
    }
  }
}
