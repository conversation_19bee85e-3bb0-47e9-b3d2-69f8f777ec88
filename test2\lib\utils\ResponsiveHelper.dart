import 'package:flutter/material.dart';

class ResponsiveHelper {
  static const double _mobileBreakpoint = 600;
  static const double _tabletBreakpoint = 1024;
  static const double _desktopBreakpoint = 1440;

  // تحديد نوع الجهاز
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < _mobileBreakpoint;
  }

  static bool isTablet(BuildContext context) {
    return MediaQuery.of(context).size.width >= _mobileBreakpoint &&
        MediaQuery.of(context).size.width < _tabletBreakpoint;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= _tabletBreakpoint;
  }

  // الحصول على عرض الشاشة
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  // الحصول على ارتفاع الشاشة
  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  // الحصول على نسبة العرض إلى الارتفاع
  static double aspectRatio(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return size.width / size.height;
  }

  // حساب الحجم المتجاوب
  static double responsiveSize(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    if (isDesktop(context)) {
      return desktop ?? tablet ?? mobile;
    } else if (isTablet(context)) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }

  // حساب الخط المتجاوب
  static double responsiveFontSize(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    final baseSize = responsiveSize(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );

    // تعديل حسب كثافة البكسل
    final textScaler = MediaQuery.of(context).textScaler;
    return baseSize / textScaler.scale(1.0);
  }

  // حساب المسافات المتجاوبة
  static double responsivePadding(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return responsiveSize(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  // حساب الهوامش المتجاوبة
  static double responsiveMargin(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return responsiveSize(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  // حساب عرض العنصر المتجاوب
  static double responsiveWidth(
    BuildContext context, {
    required double percentage,
    double? maxWidth,
    double? minWidth,
  }) {
    final screenW = screenWidth(context);
    double width = screenW * (percentage / 100);

    if (maxWidth != null && width > maxWidth) {
      width = maxWidth;
    }

    if (minWidth != null && width < minWidth) {
      width = minWidth;
    }

    return width;
  }

  // حساب ارتفاع العنصر المتجاوب
  static double responsiveHeight(
    BuildContext context, {
    required double percentage,
    double? maxHeight,
    double? minHeight,
  }) {
    final screenH = screenHeight(context);
    double height = screenH * (percentage / 100);

    if (maxHeight != null && height > maxHeight) {
      height = maxHeight;
    }

    if (minHeight != null && height < minHeight) {
      height = minHeight;
    }

    return height;
  }

  // حساب عدد الأعمدة في Grid
  static int responsiveGridColumns(
    BuildContext context, {
    int mobile = 2,
    int? tablet,
    int? desktop,
  }) {
    if (isDesktop(context)) {
      return desktop ?? tablet ?? mobile;
    } else if (isTablet(context)) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }

  // حساب نسبة العرض إلى الارتفاع للعناصر
  static double responsiveAspectRatio(
    BuildContext context, {
    double mobile = 1.0,
    double? tablet,
    double? desktop,
  }) {
    return responsiveSize(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  // الحصول على SafeArea padding
  static EdgeInsets safeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  // التحقق من الاتجاه
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  // حساب الحد الأدنى للمس
  static double get minTouchTarget => 48.0;

  // التأكد من أن العنصر قابل للمس
  static double ensureTouchTarget(double size) {
    return size < minTouchTarget ? minTouchTarget : size;
  }

  // حساب المسافة بين العناصر
  static double responsiveSpacing(
    BuildContext context, {
    double mobile = 8.0,
    double? tablet,
    double? desktop,
  }) {
    return responsiveSize(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  // حساب نصف قطر الحواف
  static double responsiveBorderRadius(
    BuildContext context, {
    double mobile = 8.0,
    double? tablet,
    double? desktop,
  }) {
    return responsiveSize(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  // حساب سمك الحدود
  static double responsiveBorderWidth(
    BuildContext context, {
    double mobile = 1.0,
    double? tablet,
    double? desktop,
  }) {
    return responsiveSize(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  // حساب الظل
  static double responsiveElevation(
    BuildContext context, {
    double mobile = 2.0,
    double? tablet,
    double? desktop,
  }) {
    return responsiveSize(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  // حساب حجم الأيقونة
  static double responsiveIconSize(
    BuildContext context, {
    double mobile = 24.0,
    double? tablet,
    double? desktop,
  }) {
    return responsiveSize(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  // إنشاء EdgeInsets متجاوب
  static EdgeInsets responsiveEdgeInsets(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    final padding = responsivePadding(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
    return EdgeInsets.all(padding);
  }

  // إنشاء EdgeInsets متجاوب مخصص
  static EdgeInsets responsiveEdgeInsetsCustom(
    BuildContext context, {
    double? top,
    double? bottom,
    double? left,
    double? right,
    double? horizontal,
    double? vertical,
    double? all,
  }) {
    if (all != null) {
      final padding = responsivePadding(context, mobile: all);
      return EdgeInsets.all(padding);
    }

    if (horizontal != null || vertical != null) {
      final h = horizontal != null
          ? responsivePadding(context, mobile: horizontal)
          : 0.0;
      final v =
          vertical != null ? responsivePadding(context, mobile: vertical) : 0.0;
      return EdgeInsets.symmetric(horizontal: h, vertical: v);
    }

    return EdgeInsets.only(
      top: top != null ? responsivePadding(context, mobile: top) : 0.0,
      bottom: bottom != null ? responsivePadding(context, mobile: bottom) : 0.0,
      left: left != null ? responsivePadding(context, mobile: left) : 0.0,
      right: right != null ? responsivePadding(context, mobile: right) : 0.0,
    );
  }
}
