import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/Address.dart';

/// خدمة إدارة العناوين
class AddressService {
  static const String _addressesKey = 'user_addresses';
  static const String _defaultAddressKey = 'default_address_id';

  /// حفظ قائمة العناوين
  static Future<bool> saveAddresses(List<Address> addresses) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final addressesJson = addresses.map((address) => address.toMap()).toList();
      final success = await prefs.setString(_addressesKey, jsonEncode(addressesJson));
      print('تم حفظ ${addresses.length} عنوان');
      return success;
    } catch (e) {
      print('خطأ في حفظ العناوين: $e');
      return false;
    }
  }

  /// تحميل قائمة العناوين
  static Future<List<Address>> loadAddresses() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final addressesString = prefs.getString(_addressesKey);
      
      if (addressesString == null || addressesString.isEmpty) {
        print('لا توجد عناوين محفوظة');
        return [];
      }

      final addressesJson = jsonDecode(addressesString) as List;
      final addresses = addressesJson
          .map((json) => Address.fromMap(json as Map<String, dynamic>))
          .toList();
      
      print('تم تحميل ${addresses.length} عنوان');
      return addresses;
    } catch (e) {
      print('خطأ في تحميل العناوين: $e');
      return [];
    }
  }

  /// إضافة عنوان جديد
  static Future<bool> addAddress(Address address) async {
    try {
      final addresses = await loadAddresses();
      
      // إذا كان هذا أول عنوان، اجعله افتراضي
      if (addresses.isEmpty) {
        address = address.copyWith(isDefault: true);
        await setDefaultAddressId(address.id);
      }
      
      addresses.add(address);
      return await saveAddresses(addresses);
    } catch (e) {
      print('خطأ في إضافة العنوان: $e');
      return false;
    }
  }

  /// تحديث عنوان موجود
  static Future<bool> updateAddress(Address updatedAddress) async {
    try {
      final addresses = await loadAddresses();
      final index = addresses.indexWhere((addr) => addr.id == updatedAddress.id);
      
      if (index == -1) {
        print('العنوان غير موجود');
        return false;
      }
      
      addresses[index] = updatedAddress;
      return await saveAddresses(addresses);
    } catch (e) {
      print('خطأ في تحديث العنوان: $e');
      return false;
    }
  }

  /// حذف عنوان
  static Future<bool> deleteAddress(String addressId) async {
    try {
      final addresses = await loadAddresses();
      final addressToDelete = addresses.firstWhere(
        (addr) => addr.id == addressId,
        orElse: () => throw Exception('العنوان غير موجود'),
      );
      
      addresses.removeWhere((addr) => addr.id == addressId);
      
      // إذا كان العنوان المحذوف هو الافتراضي، اختر عنوان آخر كافتراضي
      if (addressToDelete.isDefault && addresses.isNotEmpty) {
        addresses[0] = addresses[0].copyWith(isDefault: true);
        await setDefaultAddressId(addresses[0].id);
      } else if (addresses.isEmpty) {
        await clearDefaultAddress();
      }
      
      return await saveAddresses(addresses);
    } catch (e) {
      print('خطأ في حذف العنوان: $e');
      return false;
    }
  }

  /// تعيين عنوان كافتراضي
  static Future<bool> setDefaultAddress(String addressId) async {
    try {
      final addresses = await loadAddresses();
      
      // إزالة الافتراضي من جميع العناوين
      for (int i = 0; i < addresses.length; i++) {
        addresses[i] = addresses[i].copyWith(isDefault: false);
      }
      
      // تعيين العنوان الجديد كافتراضي
      final index = addresses.indexWhere((addr) => addr.id == addressId);
      if (index == -1) {
        print('العنوان غير موجود');
        return false;
      }
      
      addresses[index] = addresses[index].copyWith(isDefault: true);
      await setDefaultAddressId(addressId);
      
      return await saveAddresses(addresses);
    } catch (e) {
      print('خطأ في تعيين العنوان الافتراضي: $e');
      return false;
    }
  }

  /// الحصول على العنوان الافتراضي
  static Future<Address?> getDefaultAddress() async {
    try {
      final addresses = await loadAddresses();
      return addresses.firstWhere(
        (addr) => addr.isDefault,
        orElse: () => addresses.isNotEmpty ? addresses.first : throw Exception('لا توجد عناوين'),
      );
    } catch (e) {
      print('لا يوجد عنوان افتراضي: $e');
      return null;
    }
  }

  /// الحصول على عنوان بالمعرف
  static Future<Address?> getAddressById(String addressId) async {
    try {
      final addresses = await loadAddresses();
      return addresses.firstWhere(
        (addr) => addr.id == addressId,
        orElse: () => throw Exception('العنوان غير موجود'),
      );
    } catch (e) {
      print('العنوان غير موجود: $e');
      return null;
    }
  }

  /// الحصول على العناوين حسب النوع
  static Future<List<Address>> getAddressesByType(String type) async {
    try {
      final addresses = await loadAddresses();
      return addresses.where((addr) => addr.title == type).toList();
    } catch (e) {
      print('خطأ في البحث عن العناوين: $e');
      return [];
    }
  }

  /// حفظ معرف العنوان الافتراضي
  static Future<bool> setDefaultAddressId(String addressId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_defaultAddressKey, addressId);
    } catch (e) {
      print('خطأ في حفظ معرف العنوان الافتراضي: $e');
      return false;
    }
  }

  /// الحصول على معرف العنوان الافتراضي
  static Future<String?> getDefaultAddressId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_defaultAddressKey);
    } catch (e) {
      print('خطأ في تحميل معرف العنوان الافتراضي: $e');
      return null;
    }
  }

  /// مسح العنوان الافتراضي
  static Future<bool> clearDefaultAddress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_defaultAddressKey);
    } catch (e) {
      print('خطأ في مسح العنوان الافتراضي: $e');
      return false;
    }
  }

  /// مسح جميع العناوين
  static Future<bool> clearAllAddresses() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_addressesKey);
      await prefs.remove(_defaultAddressKey);
      print('تم مسح جميع العناوين');
      return true;
    } catch (e) {
      print('خطأ في مسح العناوين: $e');
      return false;
    }
  }

  /// التحقق من وجود عناوين
  static Future<bool> hasAddresses() async {
    final addresses = await loadAddresses();
    return addresses.isNotEmpty;
  }

  /// عدد العناوين المحفوظة
  static Future<int> getAddressCount() async {
    final addresses = await loadAddresses();
    return addresses.length;
  }

  /// البحث في العناوين
  static Future<List<Address>> searchAddresses(String query) async {
    try {
      final addresses = await loadAddresses();
      final lowerQuery = query.toLowerCase();
      
      return addresses.where((addr) {
        return addr.title.toLowerCase().contains(lowerQuery) ||
               addr.fullAddress.toLowerCase().contains(lowerQuery) ||
               addr.city.toLowerCase().contains(lowerQuery) ||
               addr.district.toLowerCase().contains(lowerQuery);
      }).toList();
    } catch (e) {
      print('خطأ في البحث: $e');
      return [];
    }
  }
}
