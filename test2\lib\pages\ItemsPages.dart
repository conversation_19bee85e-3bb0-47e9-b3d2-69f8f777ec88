import 'package:clippy_flutter/clippy_flutter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:test2/widgets/ItemAppBar.dart';

import '../widgets/ItemBottomNavBar.dart';

class ItemsPages extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)!.settings.arguments as Map;
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: ItemAppBar(),
      body: ListView(children: [
        Padding(
          padding: EdgeInsets.all(16),
          child: Image.asset(
            "images/${args['id']}.png",
            height: 300,
          ),
        ),
        Arc(
          edge: Edge.TOP,
          arcType: ArcType.CONVEY,
          height: 30,
          child: Container(
            width: double.infinity,
            color: Colors.white,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                textDirection: TextDirection.rtl,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 50, bottom: 20),
                    child: Row(
                      textDirection: TextDirection.rtl,
                      children: [
                        Text(
                          "عنوان المنتج",
                          style: TextStyle(
                            fontSize: 28,
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 5, bottom: 10),
                    child: Row(
                      textDirection: TextDirection.rtl,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        RatingBar.builder(
                          initialRating: 4,
                          minRating: 1,
                          direction: Axis.horizontal,
                          itemCount: 5,
                          itemSize: 20,
                          itemPadding: EdgeInsets.symmetric(horizontal: 4),
                          itemBuilder: (context, _) => Icon(
                            Icons.favorite,
                            color: Color(0xFF4C53A5),
                          ),
                          onRatingUpdate: (index) {},
                        ),
                        Row(textDirection: TextDirection.rtl, children: [
                          Container(
                            padding: EdgeInsets.all(5),
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.5),
                                    spreadRadius: 3,
                                    blurRadius: 10,
                                    offset: Offset(0, 3),
                                  )
                                ]),
                            child: Icon(CupertinoIcons.minus,
                                size: 18, color: Color(0xFF4C53A5)),
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 10),
                            child: Text(
                              "01",
                              style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF4C53A5)),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.all(5),
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.5),
                                    spreadRadius: 3,
                                    blurRadius: 10,
                                    offset: Offset(0, 3),
                                  )
                                ]),
                            child: Icon(CupertinoIcons.plus,
                                size: 18, color: Color(0xFF4C53A5)),
                          )
                        ])
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 12),
                    child: Text(
                      "السلتة هي أكلة يمنية تقليدية تُقدّم ساخنة جدًا، وتُطهى في مقلاة حجرية تُسمى المقلى أو المدرة، وتحافظ على حرارة الأكل لمدة طويلة.",
                      textAlign: TextAlign.justify,
                      style: TextStyle(fontSize: 17, color: Color(0xFF4C53A5)),
                    ),
                  ),
                  // Padding(
                  //     padding: EdgeInsets.symmetric(vertical: 8),
                  //     child: Row(
                  //       textDirection: TextDirection.rtl,
                  //       children: [
                  //         Text(
                  //           "الحجم",
                  //           style: TextStyle(
                  //               fontSize: 18,
                  //               color: Color(0xFF4C53A5),
                  //               fontWeight: FontWeight.bold),
                  //         ),
                  //         SizedBox(
                  //           width: 10,
                  //         ),
                  //         Row(
                  //           textDirection: TextDirection.rtl,
                  //           children: [
                  //             for (int i = 5; i < 10; i++)
                  //               Container(
                  //                 height: 30,
                  //                 width: 30,
                  //                 alignment: Alignment.center,
                  //                 margin: EdgeInsets.symmetric(horizontal: 5),
                  //                 decoration: BoxDecoration(
                  //                     color: Colors.white,
                  //                     borderRadius: BorderRadius.circular(30),
                  //                     boxShadow: [
                  //                       BoxShadow(
                  //                           color: Colors.grey.withOpacity(0.5),
                  //                           spreadRadius: 2,
                  //                           blurRadius: 8)
                  //                     ]),
                  //                 child: Text(
                  //                   i.toString(),
                  //                   style: TextStyle(
                  //                       fontSize: 18,
                  //                       fontWeight: FontWeight.bold,
                  //                       color: Color(0xFF4C53A5)),
                  //                 ),
                  //               )
                  //           ],
                  //         )
                  //       ],
                  //     )
                  //   )
                ],
              ),
            ),
          ),
        )
      ]),
      bottomNavigationBar: ItemBottomNavBar(),
    );
  }
}
