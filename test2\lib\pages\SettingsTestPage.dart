import 'package:flutter/material.dart';
import 'package:test2/services/SettingsService.dart'; // استيراد خدمة الإعدادات
import 'package:test2/services/NotificationService.dart'; // استيراد خدمة الإشعارات
import 'package:test2/services/AutoNotificationManager.dart'; // استيراد مدير الإشعارات التلقائية
import 'package:test2/utils/ResponsiveHelper.dart'; // استيراد مساعد التجاوب
import 'package:test2/widgets/ResponsiveText.dart'; // استيراد النص المتجاوب
import 'package:test2/widgets/ResponsiveButton.dart'; // استيراد الزر المتجاوب

/// صفحة اختبار الإعدادات - لتوضيح كيفية عمل نظام الإعدادات
class SettingsTestPage extends StatefulWidget {
  @override
  _SettingsTestPageState createState() => _SettingsTestPageState();
}

class _SettingsTestPageState extends State<SettingsTestPage> {
  // إنشاء مثيلات من الخدمات المختلفة
  final SettingsService _settingsService = SettingsService(); // خدمة الإعدادات
  final NotificationService _notificationService = NotificationService(); // خدمة الإشعارات
  final AutoNotificationManager _autoNotificationManager = AutoNotificationManager(); // مدير الإشعارات التلقائية

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق العلوي
      appBar: AppBar(
        title: ResponsiveText(
          'اختبار الإعدادات', // عنوان الصفحة
          mobileFontSize: 18,
          tabletFontSize: 20,
          desktopFontSize: 22,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        backgroundColor: Color(0xFF4C53A5), // لون الخلفية
        elevation: 0, // إزالة الظل
      ),
      
      // محتوى الصفحة
      body: SingleChildScrollView(
        padding: ResponsiveHelper.responsiveEdgeInsets(
          context,
          mobile: 16,
          tablet: 20,
          desktop: 24,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // قسم معلومات الإعدادات الحالية
            _buildCurrentSettingsSection(),
            
            SizedBox(height: ResponsiveHelper.responsiveSpacing(context, mobile: 20)),
            
            // قسم اختبار الإشعارات
            _buildNotificationTestSection(),
            
            SizedBox(height: ResponsiveHelper.responsiveSpacing(context, mobile: 20)),
            
            // قسم اختبار الإعدادات المختلفة
            _buildSettingsTestSection(),
          ],
        ),
      ),
    );
  }

  /// بناء قسم معلومات الإعدادات الحالية
  Widget _buildCurrentSettingsSection() {
    return Container(
      width: double.infinity,
      padding: ResponsiveHelper.responsiveEdgeInsets(
        context,
        mobile: 16,
        tablet: 20,
        desktop: 24,
      ),
      decoration: BoxDecoration(
        color: Colors.blue.shade50, // لون الخلفية
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.responsiveBorderRadius(context, mobile: 12),
        ),
        border: Border.all(color: Colors.blue.shade200), // حدود ملونة
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          ResponsiveText(
            'الإعدادات الحالية', // عنوان القسم
            mobileFontSize: 16,
            tabletFontSize: 18,
            desktopFontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.blue.shade800,
          ),
          
          SizedBox(height: ResponsiveHelper.responsiveSpacing(context, mobile: 12)),
          
          // عرض الإعدادات الحالية
          _buildSettingInfo('الإشعارات', _settingsService.notificationsEnabled),
          _buildSettingInfo('الموقع', _settingsService.locationEnabled),
          _buildSettingInfo('الوضع الليلي', _settingsService.darkModeEnabled),
          _buildSettingInfo('الأصوات', _settingsService.soundEnabled),
          _buildSettingInfo('الاهتزاز', _settingsService.vibrationEnabled),
          
          SizedBox(height: ResponsiveHelper.responsiveSpacing(context, mobile: 12)),
          
          // زر تحديث الإعدادات
          ResponsiveButton(
            text: 'تحديث الإعدادات', // نص الزر
            onPressed: () {
              setState(() {}); // إعادة بناء الواجهة لتحديث الإعدادات
              _settingsService.printCurrentSettings(); // طباعة الإعدادات الحالية
            },
            backgroundColor: Colors.blue,
            textColor: Colors.white,
            borderRadius: BorderRadius.circular(ResponsiveHelper.responsiveBorderRadius(context, mobile: 8 )),
          ),
        ],
      ),
    );
  }

  /// بناء معلومة إعداد واحد
  Widget _buildSettingInfo(String title, bool value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ResponsiveText(
            title, // عنوان الإعداد
            mobileFontSize: 14,
            tabletFontSize: 16,
            desktopFontSize: 18,
            color: Colors.grey.shade700,
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: value ? Colors.green : Colors.red, // لون حسب الحالة
              borderRadius: BorderRadius.circular(12),
            ),
            child: ResponsiveText(
              value ? 'مفعل' : 'معطل', // نص حسب الحالة
              mobileFontSize: 12,
              tabletFontSize: 14,
              desktopFontSize: 16,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم اختبار الإشعارات
  Widget _buildNotificationTestSection() {
    return Container(
      width: double.infinity,
      padding: ResponsiveHelper.responsiveEdgeInsets(
        context,
        mobile: 16,
        tablet: 20,
        desktop: 24,
      ),
      decoration: BoxDecoration(
        color: Colors.orange.shade50, // لون الخلفية
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.responsiveBorderRadius(context, mobile: 12),
        ),
        border: Border.all(color: Colors.orange.shade200), // حدود ملونة
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          ResponsiveText(
            'اختبار الإشعارات', // عنوان القسم
            mobileFontSize: 16,
            tabletFontSize: 18,
            desktopFontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.orange.shade800,
          ),
          
          SizedBox(height: ResponsiveHelper.responsiveSpacing(context, mobile: 12)),
          
          ResponsiveText(
            'اختبر كيفية تأثير إعدادات الإشعارات على التطبيق:', // وصف القسم
            mobileFontSize: 14,
            tabletFontSize: 16,
            desktopFontSize: 18,
            color: Colors.grey.shade700,
          ),
          
          SizedBox(height: ResponsiveHelper.responsiveSpacing(context, mobile: 16)),
          
          // أزرار اختبار الإشعارات
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              // زر اختبار إشعار عرض
              ResponsiveButton(
                text: 'إشعار عرض', // نص الزر
                onPressed: () async {
                  await _notificationService.showNewOfferNotification(
                    offerTitle: 'خصم خاص', // عنوان العرض
                    storeName: 'متجر الاختبار', // اسم المتجر
                    discountPercentage: '50', // نسبة الخصم
                  );
                },
                backgroundColor: Colors.green,
                textColor: Colors.white,
                borderRadius: BorderRadius.circular(ResponsiveHelper.responsiveBorderRadius(context, mobile: 8 )),
              ),
              
              // زر اختبار إشعار متجر
              ResponsiveButton(
                text: 'إشعار متجر', // نص الزر
                onPressed: () async {
                  await _notificationService.showNewStoreNotification(
                    storeName: 'متجر جديد', // اسم المتجر
                    storeCategory: 'مطاعم', // فئة المتجر
                  );
                },
                backgroundColor: Colors.blue,
                textColor: Colors.white,
                borderRadius: BorderRadius.circular(ResponsiveHelper.responsiveBorderRadius(context, mobile: 8 )),
              ),
              
              // زر اختبار إشعار منتج
              ResponsiveButton(
                text: 'إشعار منتج', // نص الزر
                onPressed: () async {
                  await _notificationService.showNewProductNotification(
                    productName: 'منتج جديد', // اسم المنتج
                    storeName: 'متجر الاختبار', // اسم المتجر
                    price: '100', // سعر المنتج
                  );
                },
                backgroundColor: Colors.purple,
                textColor: Colors.white,
                borderRadius: BorderRadius.circular(ResponsiveHelper.responsiveBorderRadius(context, mobile: 8 )),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قسم اختبار الإعدادات المختلفة
  Widget _buildSettingsTestSection() {
    return Container(
      width: double.infinity,
      padding: ResponsiveHelper.responsiveEdgeInsets(
        context,
        mobile: 16,
        tablet: 20,
        desktop: 24,
      ),
      decoration: BoxDecoration(
        color: Colors.red.shade50, // لون الخلفية
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.responsiveBorderRadius(context, mobile: 12),
        ),
        border: Border.all(color: Colors.red.shade200), // حدود ملونة
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          ResponsiveText(
            'اختبار تطبيق الإعدادات', // عنوان القسم
            mobileFontSize: 16,
            tabletFontSize: 18,
            desktopFontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.red.shade800,
          ),
          
          SizedBox(height: ResponsiveHelper.responsiveSpacing(context, mobile: 12)),
          
          ResponsiveText(
            'اختبر كيفية تأثير تغيير الإعدادات على سلوك التطبيق:', // وصف القسم
            mobileFontSize: 14,
            tabletFontSize: 16,
            desktopFontSize: 18,
            color: Colors.grey.shade700,
          ),
          
          SizedBox(height: ResponsiveHelper.responsiveSpacing(context, mobile: 16)),
          
          // أزرار اختبار الإعدادات
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              // زر تفعيل الإشعارات
              ResponsiveButton(
                text: 'تفعيل الإشعارات', // نص الزر
                onPressed: () async {
                  bool success = await _settingsService.setNotificationsEnabled(true);
                  if (success) {
                    setState(() {}); // تحديث الواجهة
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('تم تفعيل الإشعارات')),
                    );
                  }
                },
                backgroundColor: Colors.green,
                textColor: Colors.white,
                borderRadius: BorderRadius.circular(ResponsiveHelper.responsiveBorderRadius(context, mobile: 8 )),
              ),
              
              // زر إلغاء تفعيل الإشعارات
              ResponsiveButton(
                text: 'إلغاء الإشعارات', // نص الزر
                onPressed: () async {
                  bool success = await _settingsService.setNotificationsEnabled(false);
                  if (success) {
                    setState(() {}); // تحديث الواجهة
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('تم إلغاء تفعيل الإشعارات')),
                    );
                  }
                },
                backgroundColor: Colors.red,
                textColor: Colors.white,
                borderRadius: BorderRadius.circular(ResponsiveHelper.responsiveBorderRadius(context, mobile: 8 )),
              ),
              
              // زر بدء الإشعارات التلقائية
              ResponsiveButton(
                text: 'بدء الإشعارات التلقائية', // نص الزر
                onPressed: () {
                  _autoNotificationManager.startAutoNotifications();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('تم بدء الإشعارات التلقائية')),
                  );
                },
                backgroundColor: Colors.blue,
                textColor: Colors.white,
                borderRadius: BorderRadius.circular(ResponsiveHelper.responsiveBorderRadius(context, mobile: 8 )),
              ),
              
              // زر إيقاف الإشعارات التلقائية
              ResponsiveButton(
                text: 'إيقاف الإشعارات التلقائية', // نص الزر
                onPressed: () {
                  _autoNotificationManager.stopAutoNotifications();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('تم إيقاف الإشعارات التلقائية')),
                  );
                },
                backgroundColor: Colors.orange,
                textColor: Colors.white,
                borderRadius: BorderRadius.circular(ResponsiveHelper.responsiveBorderRadius(context, mobile: 8 )),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
