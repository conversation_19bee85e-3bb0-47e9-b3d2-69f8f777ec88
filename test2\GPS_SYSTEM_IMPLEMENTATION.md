# نظام GPS المحسن - تفعيل وإلغاء تفعيل GPS من الإعدادات

## نظرة عامة

تم تطوير نظام GPS متكامل يربط بين إعدادات التطبيق وخدمة الموقع، بحيث يتم تفعيل أو إلغاء تفعيل GPS تلقائياً عند تغيير الإعدادات.

## المكونات الرئيسية

### 1. SettingsService
- يدير جميع إعدادات التطبيق بما في ذلك إعداد الموقع
- يحفظ الإعدادات في التخزين المحلي (SharedPreferences)
- يوفر دوال لتفعيل/إلغاء تفعيل إعداد الموقع

### 2. LocationService
- يدير خدمة GPS والموقع
- يطبق إعدادات الموقع تلقائياً
- يتعامل مع أذونات الموقع
- يوفر دوال للحصول على الموقع الحالي

### 3. SettingsPage
- واجهة المستخدم لإدارة الإعدادات
- مفتاح تفعيل/إلغاء تفعيل الموقع
- أزرار اختبار GPS وعرض الحالة

## كيفية العمل

### التهيئة الأولية
```dart
// في main.dart
final locationService = LocationService();
await locationService.initialize(); // تطبيق إعدادات GPS حسب الإعدادات المحفوظة
```

### تغيير الإعدادات
```dart
// عند تغيير إعداد الموقع في SettingsPage
bool success = await _settingsService.setLocationEnabled(value);
if (success) {
  await _locationService.updateLocationSettings(value);
}
```

### تطبيق الإعدادات
- **عند التفعيل**: يطلب أذونات الموقع ويفعل GPS
- **عند الإلغاء**: يوقف استخدام GPS ويمسح البيانات المحفوظة

## الميزات الجديدة

### 1. مؤشر التحميل
- يظهر أثناء تطبيق إعدادات GPS
- يوضح للمستخدم أن العملية قيد التنفيذ

### 2. رسائل تفصيلية
- رسائل تأكيد عند نجاح العملية
- رسائل خطأ واضحة عند الفشل
- معلومات عن حالة GPS والأذونات

### 3. اختبار GPS
- زر "اختبار GPS" للتحقق من عمل الخدمة
- عرض الإحداثيات عند نجاح الاختبار

### 4. حالة مفصلة
- زر "حالة خدمة الموقع" لعرض تفاصيل شاملة
- فحص خدمة الموقع في النظام
- فحص أذونات التطبيق
- فحص إعدادات التطبيق

### 5. إصلاح المشاكل
- اكتشاف تلقائي للمشاكل
- اقتراح خطوات الإصلاح
- أزرار لفتح الإعدادات المناسبة

## حالات الاستخدام

### 1. تفعيل GPS لأول مرة
1. المستخدم يفعل مفتاح "تحديد الموقع"
2. النظام يطلب أذونات الموقع
3. يتم تفعيل GPS تلقائياً
4. رسالة تأكيد مع خيار اختبار الموقع

### 2. إلغاء تفعيل GPS
1. المستخدم يلغي تفعيل مفتاح "تحديد الموقع"
2. النظام يوقف استخدام GPS
3. يتم مسح البيانات المحفوظة
4. رسالة تأكيد الإلغاء

### 3. اختبار GPS
1. المستخدم ينقر "اختبار GPS"
2. النظام يحاول الحصول على الموقع
3. عرض الإحداثيات أو رسالة خطأ

### 4. فحص الحالة
1. المستخدم ينقر "حالة خدمة الموقع"
2. فحص شامل لجميع المتطلبات
3. عرض النتائج مع خيارات الإصلاح

## الأذونات المطلوبة

### Android
```xml
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
```

### iOS
```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>يحتاج التطبيق للوصول إلى موقعك لتحديد المتاجر القريبة</string>
```

## المكتبات المستخدمة

- `geolocator: ^11.0.0` - للحصول على الموقع
- `permission_handler: ^11.3.1` - لإدارة الأذونات
- `shared_preferences: ^2.2.2` - لحفظ الإعدادات

## الاختبار

### اختبار التفعيل
1. افتح صفحة الإعدادات
2. فعل مفتاح "تحديد الموقع"
3. اقبل أذونات الموقع
4. انقر "اختبار GPS"
5. تأكد من ظهور الإحداثيات

### اختبار الإلغاء
1. ألغ تفعيل مفتاح "تحديد الموقع"
2. انقر "اختبار GPS"
3. تأكد من ظهور رسالة "الموقع معطل"

### اختبار الحالة
1. انقر "حالة خدمة الموقع"
2. تأكد من دقة المعلومات المعروضة
3. جرب خيارات الإصلاح إذا لزم الأمر

## ملاحظات مهمة

1. **الأمان**: لا يتم حفظ الإحداثيات في التخزين المحلي
2. **الخصوصية**: يمكن إلغاء تفعيل GPS في أي وقت
3. **الأداء**: GPS يعمل فقط عند الحاجة
4. **التوافق**: يعمل على Android و iOS
5. **إعادة التشغيل**: الإعدادات تطبق تلقائياً عند بدء التطبيق

## استكشاف الأخطاء

### مشكلة: لا يعمل GPS
- تأكد من تفعيل خدمة الموقع في الجهاز
- تأكد من منح أذونات الموقع للتطبيق
- تأكد من تفعيل الإعداد في التطبيق

### مشكلة: بطء في الحصول على الموقع
- تأكد من قوة إشارة GPS
- جرب في مكان مفتوح
- أعد تشغيل التطبيق

### مشكلة: رسائل خطأ
- اقرأ الرسالة بعناية
- اتبع الخطوات المقترحة
- استخدم "حالة خدمة الموقع" للتشخيص
