# دليل الاستخدام السريع - نظام الواجهة الجميلة

## 🎯 كيفية استخدام النظام الجديد

### 1. **حوارات التحميل الجميلة**

```dart
import 'package:test2/widgets/LoadingDialogs.dart';

// عرض حوار فحص أذونات الموقع
LoadingDialogs.showLocationPermissionLoading(context);

// عرض حوار تهيئة التتبع
LoadingDialogs.showTrackingInitLoading(context);

// عرض حوار تحديد الموقع
LoadingDialogs.showLocationLoading(context);

// عرض حوار تطبيق الإعدادات
LoadingDialogs.showSettingsLoading(context, true); // true = تفعيل

// عرض حوار اختبار GPS
LoadingDialogs.showGPSTestLoading(context);

// إغلاق أي حوار تحميل
LoadingDialogs.hideLoadingDialog(context);
```

### 2. **رسائل الإشعارات الجميلة**

```dart
import 'package:test2/widgets/CustomSnackBars.dart';

// رسالة نجاح
CustomSnackBars.showSuccess(
  context,
  message: "تم الحفظ بنجاح!",
  subtitle: "تم تطبيق جميع التغييرات",
  actionLabel: "عرض",
  onAction: () => showDetails(),
);

// رسالة خطأ
CustomSnackBars.showError(
  context,
  message: "حدث خطأ",
  subtitle: "يرجى المحاولة مرة أخرى",
  actionLabel: "إعادة المحاولة",
  onAction: () => retry(),
);

// رسالة تحذير
CustomSnackBars.showWarning(
  context,
  message: "تنبيه مهم",
  subtitle: "يرجى الانتباه لهذا الأمر",
);

// رسالة معلومات
CustomSnackBars.showInfo(
  context,
  message: "معلومة مفيدة",
  subtitle: "تفاصيل إضافية هنا",
);

// رسائل GPS جاهزة
CustomSnackBars.showGPSSuccess(context);
CustomSnackBars.showGPSError(context, "رسالة الخطأ");
CustomSnackBars.showGPSWarning(context, "رسالة التحذير");
```

### 3. **رسائل Toast خفيفة**

```dart
// رسائل سريعة وخفيفة
CustomToasts.showSuccess(context, "تم بنجاح!");
CustomToasts.showError(context, "حدث خطأ!");
CustomToasts.showInfo(context, "معلومة سريعة");
```

### 4. **الحوارات التفاعلية**

```dart
import 'package:test2/widgets/CustomDialogs.dart';

// حوار تأكيد
bool? result = await CustomDialogs.showConfirmDialog(
  context,
  title: "تأكيد الحذف",
  message: "هل أنت متأكد من حذف هذا العنصر؟",
  confirmText: "حذف",
  cancelText: "إلغاء",
  icon: Icons.delete,
  iconColor: Colors.red,
  isDangerous: true,
);

if (result == true) {
  // المستخدم أكد
  deleteItem();
}

// حوار معلومات
await CustomDialogs.showInfoDialog(
  context,
  title: "معلومات مهمة",
  message: "هذه معلومات مفيدة للمستخدم",
  buttonText: "فهمت",
  icon: Icons.info,
  iconColor: Colors.blue,
);

// حوار خطأ مع إعادة محاولة
await CustomDialogs.showErrorDialog(
  context,
  title: "حدث خطأ",
  message: "فشل في تنفيذ العملية",
  buttonText: "إغلاق",
  onRetry: () => retryOperation(),
);

// حوار أذونات GPS
bool? shouldOpenSettings = await CustomDialogs.showGPSPermissionDialog(
  context,
  title: "تفعيل خدمة الموقع",
  message: "يرجى تفعيل خدمة الموقع من إعدادات الجهاز",
  actionText: "فتح الإعدادات",
  needsSystemSettings: true,
);

if (shouldOpenSettings == true) {
  // فتح إعدادات النظام
  await Geolocator.openLocationSettings();
}
```

## 🎨 أمثلة عملية من التطبيق

### مثال 1: فتح تتبع الطلب

```dart
Future<void> _openOrderTracking() async {
  try {
    // عرض حوار التحميل الجميل
    LoadingDialogs.showLocationPermissionLoading(context);

    // طلب أذونات الموقع
    LocationPermissionResult result = await _locationService.requestLocationPermissionInteractive();

    // إغلاق حوار التحميل
    LoadingDialogs.hideLoadingDialog(context);

    if (result.success) {
      // فتح صفحة التتبع
      Navigator.push(context, MaterialPageRoute(builder: (context) => OrderTrackingPage()));
      
      // عرض رسالة نجاح
      CustomSnackBars.showGPSSuccess(context);
    } else {
      // عرض حوار أذونات
      _showPermissionDialog(result);
    }
  } catch (e) {
    LoadingDialogs.hideLoadingDialog(context);
    CustomSnackBars.showGPSError(context, e.toString());
  }
}
```

### مثال 2: تفعيل GPS في الإعدادات

```dart
onChanged: (value) async {
  // عرض حوار التحميل
  LoadingDialogs.showSettingsLoading(context, value);

  try {
    bool success = await _settingsService.setLocationEnabled(value);
    
    LoadingDialogs.hideLoadingDialog(context);
    
    if (success) {
      setState(() => _locationEnabled = value);
      
      if (value) {
        CustomSnackBars.showSuccess(
          context,
          message: 'تم تفعيل خدمة الموقع بنجاح!',
          subtitle: 'يمكنك الآن تتبع طلباتك على الخريطة',
          actionLabel: 'اختبار',
          onAction: () => _testLocationService(),
        );
      } else {
        CustomSnackBars.showInfo(
          context,
          message: 'تم إلغاء تفعيل خدمة الموقع',
          subtitle: 'لن تتمكن من تتبع الطلبات على الخريطة',
        );
      }
    } else {
      CustomSnackBars.showError(
        context,
        message: 'فشل في حفظ إعدادات الموقع',
        subtitle: 'يرجى المحاولة مرة أخرى',
      );
    }
  } catch (e) {
    LoadingDialogs.hideLoadingDialog(context);
    CustomSnackBars.showError(context, message: 'خطأ في الإعدادات', subtitle: e.toString());
  }
},
```

### مثال 3: اختبار GPS

```dart
Future<void> _testLocationService() async {
  try {
    LoadingDialogs.showGPSTestLoading(context);
    
    Position position = await Geolocator.getCurrentPosition();
    
    LoadingDialogs.hideLoadingDialog(context);
    
    CustomSnackBars.showSuccess(
      context,
      message: 'اختبار GPS ناجح!',
      subtitle: 'تم الحصول على موقعك: ${position.latitude.toStringAsFixed(4)}, ${position.longitude.toStringAsFixed(4)}',
      actionLabel: 'تفاصيل',
      onAction: () => _showLocationDetails(position),
    );
  } catch (e) {
    LoadingDialogs.hideLoadingDialog(context);
    CustomSnackBars.showError(
      context,
      message: 'فشل اختبار GPS',
      subtitle: e.toString(),
      actionLabel: 'إعادة المحاولة',
      onAction: () => _testLocationService(),
    );
  }
}
```

## 🔧 نصائح للاستخدام

### 1. **استخدم الحوار المناسب للعملية:**
- `showLocationPermissionLoading` → لفحص أذونات الموقع
- `showTrackingInitLoading` → لتهيئة نظام التتبع
- `showGPSTestLoading` → لاختبار GPS
- `showSettingsLoading` → لتطبيق الإعدادات

### 2. **لا تنس إغلاق حوارات التحميل:**
```dart
try {
  LoadingDialogs.showLocationLoading(context);
  // العملية هنا
  LoadingDialogs.hideLoadingDialog(context);
} catch (e) {
  LoadingDialogs.hideLoadingDialog(context); // مهم في catch أيضاً
}
```

### 3. **استخدم الألوان المناسبة:**
- 🟢 أخضر → النجاح والتفعيل
- 🔴 أحمر → الأخطاء والمشاكل
- 🟠 برتقالي → التحذيرات
- 🔵 أزرق → المعلومات

### 4. **أضف أزرار إجراءات مفيدة:**
```dart
CustomSnackBars.showSuccess(
  context,
  message: "تم الحفظ",
  actionLabel: "عرض",           // زر مفيد
  onAction: () => showResult(), // إجراء واضح
);
```

## 🎯 النتيجة

بهذا النظام الجديد، جميع رسائل التطبيق أصبحت:
- ✅ **جميلة ومتناسقة** في التصميم
- ✅ **واضحة ومفيدة** للمستخدم
- ✅ **تفاعلية** مع أزرار إجراءات
- ✅ **متحركة** بتأثيرات سلسة
- ✅ **سهلة الاستخدام** للمطورين

**استمتع بالواجهة الجديدة!** 🎉✨
