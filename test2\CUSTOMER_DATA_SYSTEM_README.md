# نظام إدارة بيانات العميل الموحد

## 📋 نظرة عامة

هذا النظام يوفر إدارة موحدة لجميع بيانات العميل في التطبيق باستخدام SharedPreferences كمصدر وحيد للبيانات.

## 🏗️ هيكل النظام

### 1. الطبقات الأساسية

```
CustomerDataService (خدمة البيانات)
    ↓
CustomerDataProvider (مزود الحالة)
    ↓
UI Components (مكونات الواجهة)
```

### 2. الملفات الأساسية

- `lib/services/CustomerDataService.dart` - خدمة إدارة البيانات
- `lib/providers/CustomerDataProvider.dart` - مزود حالة البيانات
- `lib/models/UserProfile.dart` - نموذج بيانات المستخدم
- `lib/models/Address.dart` - نموذج بيانات العنوان

## 🔧 كيفية الاستخدام

### 1. في الصفحات (Pages)

```dart
// استيراد المزود
import '../providers/CustomerDataProvider.dart';

// استخدام Consumer للوصول للبيانات
Consumer<CustomerDataProvider>(
  builder: (context, customerProvider, child) {
    return Text(customerProvider.fullName);
  },
)

// أو الوصول المباشر
final customerProvider = Provider.of<CustomerDataProvider>(context, listen: false);
await customerProvider.ensureDataLoaded();
String name = customerProvider.fullName;
```

### 2. في الخدمات (Services)

```dart
// استيراد الخدمة
import '../services/CustomerDataService.dart';

// الوصول للبيانات
final customerService = CustomerDataService();
await customerService.ensureDataLoaded();
UserProfile? profile = customerService.customerProfile;
```

### 3. تحديث البيانات

```dart
// تحديث البيانات الأساسية
await customerProvider.updateBasicProfile(
  firstName: 'أحمد',
  lastName: 'محمد',
  phone: '+966501234567',
  email: '<EMAIL>',
);

// إضافة عنوان جديد
final newAddress = Address(
  id: 'addr_123',
  title: 'المنزل',
  street: 'شارع الملك فهد',
  district: 'حي العليا',
  city: 'الرياض',
  buildingNumber: '123',
  fullAddress: 'شارع الملك فهد، حي العليا، الرياض',
  isDefault: true,
);

await customerProvider.addAddress(newAddress);
```

## 📊 البيانات المتاحة

### معلومات العميل الأساسية
- `fullName` - الاسم الكامل
- `email` - البريد الإلكتروني
- `phone` - رقم الهاتف
- `customerId` - معرف العميل
- `isLoggedIn` - حالة تسجيل الدخول

### العناوين
- `addresses` - جميع العناوين
- `defaultAddress` - العنوان الافتراضي
- `addAddress()` - إضافة عنوان جديد
- `updateAddress()` - تحديث عنوان موجود
- `deleteAddress()` - حذف عنوان
- `setDefaultAddress()` - تعيين عنوان افتراضي

### التفضيلات
- `getPreference()` - الحصول على تفضيل
- `setPreference()` - تعيين تفضيل
- `updatePreferences()` - تحديث جميع التفضيلات

## 🔄 دورة حياة البيانات

### 1. التحميل الأولي
```dart
// في main.dart
WidgetsBinding.instance.addPostFrameCallback((_) {
  Provider.of<CustomerDataProvider>(context, listen: false).loadCustomerData();
});
```

### 2. التحديث التلقائي
```dart
// عند تغيير البيانات
await customerProvider.updateCustomerData(firstName: 'اسم جديد');
// ← يتم حفظ البيانات في SharedPreferences تلقائياً
// ← يتم تحديث جميع الواجهات المرتبطة تلقائياً
```

### 3. تسجيل الخروج
```dart
await customerProvider.logout();
// ← يتم مسح جميع البيانات من SharedPreferences
// ← يتم إنشاء بيانات تجريبية جديدة
```

## 🛡️ الأمان والموثوقية

### 1. التحقق من صحة البيانات
```dart
// التحقق من رقم الهاتف
bool isValid = customerProvider.isValidPhone('+966501234567');

// التحقق من البريد الإلكتروني
bool isValid = customerProvider.isValidEmail('<EMAIL>');
```

### 2. معالجة الأخطاء
```dart
// التحقق من حالة التحميل
if (customerProvider.isLoading) {
  return CircularProgressIndicator();
}

// التحقق من الأخطاء
if (customerProvider.error != null) {
  return Text('خطأ: ${customerProvider.error}');
}
```

### 3. ضمان تحميل البيانات
```dart
// التأكد من تحميل البيانات قبل الاستخدام
await customerProvider.ensureDataLoaded();
```

## 🧪 الاختبار

### 1. اختبار التحميل
```dart
void testDataLoading() async {
  final provider = CustomerDataProvider();
  await provider.loadCustomerData();
  assert(provider.isLoggedIn);
  assert(provider.fullName.isNotEmpty);
}
```

### 2. اختبار التحديث
```dart
void testDataUpdate() async {
  final provider = CustomerDataProvider();
  await provider.loadCustomerData();
  
  final oldName = provider.fullName;
  await provider.updateBasicProfile(firstName: 'اسم جديد');
  
  assert(provider.fullName != oldName);
  assert(provider.fullName.contains('اسم جديد'));
}
```

### 3. اختبار تسجيل الخروج
```dart
void testLogout() async {
  final provider = CustomerDataProvider();
  await provider.loadCustomerData();
  
  assert(provider.isLoggedIn);
  await provider.logout();
  assert(!provider.isLoggedIn);
}
```

## 🔧 استكشاف الأخطاء

### 1. البيانات لا تظهر
```dart
// تأكد من تحميل البيانات
await customerProvider.ensureDataLoaded();

// تحقق من حالة تسجيل الدخول
if (!customerProvider.isLoggedIn) {
  // إنشاء بيانات جديدة أو تسجيل دخول
}
```

### 2. البيانات لا تتحدث
```dart
// تأكد من استخدام Consumer أو Provider.of مع listen: true
Consumer<CustomerDataProvider>(
  builder: (context, provider, child) {
    return Text(provider.fullName);
  },
)
```

### 3. فقدان البيانات
```dart
// تحقق من حفظ البيانات
bool success = await customerProvider.updateCustomerData(/*...*/);
if (!success) {
  // معالجة فشل الحفظ
}
```

## 📈 الأداء

### 1. التخزين المؤقت
- البيانات محملة في الذاكرة لسرعة الوصول
- تحديث SharedPreferences فقط عند التغيير

### 2. التحديث الذكي
- تحديث الواجهة فقط عند تغيير البيانات
- استخدام notifyListeners() بذكاء

### 3. إدارة الذاكرة
- Singleton pattern لتوفير الذاكرة
- تنظيف البيانات عند تسجيل الخروج

## 🔮 التطوير المستقبلي

### 1. ميزات مقترحة
- تشفير البيانات الحساسة
- مزامنة مع الخادم
- نسخ احتياطية سحابية
- تسجيل دخول متعدد الحسابات

### 2. تحسينات الأداء
- ضغط البيانات
- تحميل تدريجي
- ذاكرة تخزين مؤقت ذكية

### 3. الأمان المتقدم
- مصادقة ثنائية العامل
- انتهاء صلاحية الجلسة
- تشفير end-to-end

## 📞 الدعم

للمساعدة أو الإبلاغ عن مشاكل:
1. تحقق من هذا الدليل أولاً
2. راجع ملف CUSTOMER_DATA_UNIFIED_SYSTEM_SUMMARY.md
3. تحقق من التعليقات في الكود
4. اختبر على بيانات تجريبية أولاً

---

**النظام مصمم ليكون بسيط الاستخدام وموثوق وقابل للتطوير** 🚀
