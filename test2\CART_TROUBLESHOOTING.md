# دليل حل مشاكل نظام السلة

## 🔧 المشاكل الشائعة وحلولها

### 1. **خطأ: "Product class not found"**

#### المشكلة:
```
Error: The name 'Product' isn't defined.
```

#### الحل: ✅ تم إصلاحه
```dart
// تم إصلاح الكود في ItemsWidgets.dart
// الآن يحول البيانات من Map إلى Product بشكل صحيح

final productData = {
  'id': item['id']?.toString() ?? '',
  'name': item['name']?.toString() ?? '',
  'description': item['description']?.toString() ?? '',
  'price': _parsePrice(item['price']),
  'imageUrl': 'images/${item['id']}.png',
  'category': item['category']?.toString() ?? '',
  // باقي الحقول...
};

final product = Product.fromMap(productData);
```

### 2. **خطأ: "Provider not found"**

#### المشكلة:
```
Error: Could not find the correct Provider<CartProvider> above this widget
```

#### الحل:
```dart
// تأكد من وجود MultiProvider في main.dart
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => CartProvider()),
  ],
  child: MyApp(),
)
```

### 3. **خطأ: "SharedPreferences not working"**

#### المشكلة:
```
السلة لا تحفظ البيانات عند إغلاق التطبيق
```

#### الحل:
```bash
# تأكد من إضافة المكتبة
flutter pub add shared_preferences

# ثم
flutter pub get
flutter clean
flutter run
```

### 4. **خطأ: "CustomSnackBars not found"**

#### المشكلة:
```
Error: The name 'CustomSnackBars' isn't defined.
```

#### الحل:
```dart
// أضف الاستيراد في أعلى الملف
import 'package:test2/widgets/CustomSnackBars.dart';
```

### 5. **خطأ: "Consumer not updating UI"**

#### المشكلة:
```
واجهة المستخدم لا تتحدث عند تغيير السلة
```

#### الحل:
```dart
// استخدم Consumer بدلاً من Provider.of
Consumer<CartProvider>(
  builder: (context, cartProvider, child) {
    return Text('${cartProvider.itemCount}');
  },
)

// بدلاً من
Provider.of<CartProvider>(context, listen: false)
```

## 🚀 خطوات الإصلاح السريع

### 1. **تشغيل الأوامر الأساسية:**
```bash
flutter clean
flutter pub get
flutter run
```

### 2. **إعادة تشغيل التطبيق:**
```bash
# أوقف التطبيق (Ctrl+C)
# ثم شغله مرة أخرى
flutter run
```

### 3. **تحقق من الاستيرادات:**
```dart
// في أي ملف يستخدم السلة، تأكد من وجود:
import 'package:provider/provider.dart';
import 'package:test2/providers/CartProvider.dart';
import 'package:test2/models/Product.dart';
import 'package:test2/widgets/CustomSnackBars.dart';
```

### 4. **تحقق من pubspec.yaml:**
```yaml
dependencies:
  provider: ^6.1.1
  shared_preferences: ^2.2.2
  # باقي المكتبات...
```

## 🧪 اختبار النظام

### 1. **اختبار إضافة المنتجات:**
```
1. افتح التطبيق
2. اذهب للصفحة الرئيسية
3. انقر على أيقونة السلة في أي منتج
4. يجب أن تتغير الأيقونة إلى ✅
5. يجب أن يظهر عداد في شريط التنقل
```

### 2. **اختبار صفحة السلة:**
```
1. انقر على أيقونة السلة في شريط التنقل
2. يجب أن تفتح صفحة السلة
3. يجب أن تظهر المنتجات المضافة
4. جرب تغيير الكميات
5. جرب حذف منتج
```

### 3. **اختبار الحفظ:**
```
1. أضف منتجات للسلة
2. أغلق التطبيق تماماً
3. افتح التطبيق مرة أخرى
4. يجب أن تظهر المنتجات كما هي
```

## 🔍 تشخيص المشاكل

### 1. **تحقق من Console:**
```bash
# شغل التطبيق مع verbose logging
flutter run -v

# ابحث عن أخطاء مثل:
# - Provider errors
# - SharedPreferences errors
# - Import errors
```

### 2. **تحقق من الملفات:**
```bash
# تأكد من وجود جميع الملفات
ls lib/models/Product.dart
ls lib/models/CartItem.dart
ls lib/services/CartService.dart
ls lib/providers/CartProvider.dart
ls lib/widgets/CartIcon.dart
```

### 3. **تحقق من البيانات:**
```dart
// أضف هذا في أي مكان للتشخيص
print('Cart items: ${cartProvider.cartItems.length}');
print('Total quantity: ${cartProvider.totalQuantity}');
print('Total price: ${cartProvider.totalPrice}');
```

## 🛠️ إصلاحات متقدمة

### 1. **إعادة إنشاء الملفات:**
```bash
# إذا كانت هناك مشاكل في الملفات
rm -rf lib/models/Product.dart
rm -rf lib/providers/CartProvider.dart

# ثم أعد إنشاؤها من الكود المرفق
```

### 2. **تنظيف البيانات:**
```dart
// لمسح بيانات السلة المحفوظة
SharedPreferences prefs = await SharedPreferences.getInstance();
await prefs.remove('shopping_cart');
await prefs.remove('cart_count');
```

### 3. **إعادة تعيين التطبيق:**
```bash
# مسح جميع البيانات
flutter clean
rm -rf build/
rm pubspec.lock

# إعادة التثبيت
flutter pub get
flutter run
```

## 📞 الحصول على المساعدة

### إذا استمرت المشاكل:

1. **تحقق من الأخطاء في Console**
2. **تأكد من إصدار Flutter:**
   ```bash
   flutter --version
   # يجب أن يكون 3.0+ 
   ```

3. **تحقق من إصدار Dart:**
   ```bash
   dart --version
   # يجب أن يكون 2.17+
   ```

4. **أعد تشغيل IDE:**
   - أغلق VS Code أو Android Studio
   - افتحه مرة أخرى
   - شغل التطبيق

## ✅ قائمة التحقق النهائية

- [ ] تم تشغيل `flutter pub get`
- [ ] تم تشغيل `flutter clean`
- [ ] جميع الاستيرادات صحيحة
- [ ] MultiProvider موجود في main.dart
- [ ] لا توجد أخطاء في Console
- [ ] التطبيق يعمل بدون crash
- [ ] أيقونة السلة تظهر في المنتجات
- [ ] عداد السلة يظهر في شريط التنقل
- [ ] صفحة السلة تفتح وتعرض المنتجات
- [ ] يمكن إضافة وحذف المنتجات
- [ ] البيانات تحفظ عند إغلاق التطبيق

## 🎉 النظام جاهز!

إذا تم تحقيق جميع النقاط أعلاه، فإن نظام السلة يعمل بشكل صحيح!

**استمتع بالتسوق!** 🛒✨
