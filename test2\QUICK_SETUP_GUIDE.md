# دليل الإعداد السريع - أذونات GPS و Google Maps

## ✅ تم إصلاح أذونات الموقع

### 1. **Android** - تم إضافة الأذونات في `android/app/src/main/AndroidManifest.xml`:
```xml
<!-- أذونات الموقع المطلوبة لتتبع الطلبات -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### 2. **iOS** - تم إضافة الأذونات في `ios/Runner/Info.plist`:
```xml
<!-- أذونات الموقع المطلوبة لتتبع الطلبات -->
<key>NSLocationWhenInUseUsageDescription</key>
<string>يحتاج التطبيق للوصول إلى موقعك لتتبع طلبك وعرضه على الخريطة مع موقع سائق التوصيل</string>

<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>يحتاج التطبيق للوصول إلى موقعك لتتبع طلبك وعرضه على الخريطة مع موقع سائق التوصيل</string>
```

## 🗺️ إعداد Google Maps (مطلوب للخرائط)

### الخطوة 1: الحصول على API Key
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعل **Maps SDK for Android** و **Maps SDK for iOS**
4. أنشئ **API Key** جديد

### الخطوة 2: إضافة API Key في Android
في ملف `android/app/src/main/AndroidManifest.xml`، استبدل:
```xml
<meta-data android:name="com.google.android.geo.API_KEY"
           android:value="YOUR_GOOGLE_MAPS_API_KEY_HERE"/>
```

بـ:
```xml
<meta-data android:name="com.google.android.geo.API_KEY"
           android:value="AIza...YOUR_ACTUAL_API_KEY"/>
```

### الخطوة 3: إضافة API Key في iOS
في ملف `ios/Runner/AppDelegate.swift`، أضف:
```swift
import UIKit
import Flutter
import GoogleMaps

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GMSServices.provideAPIKey("YOUR_IOS_API_KEY_HERE")
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
```

## 🚀 تشغيل التطبيق

### الخطوة 1: تحديث المكتبات
```bash
flutter pub get
```

### الخطوة 2: تنظيف المشروع
```bash
flutter clean
```

### الخطوة 3: تشغيل التطبيق
```bash
flutter run
```

## 🧪 اختبار النظام

### 1. اختبار أذونات GPS:
1. افتح التطبيق
2. اذهب لصفحة تفاصيل طلب
3. انقر "تتبع الطلب"
4. يجب أن يظهر طلب إذن الموقع
5. وافق على الإذن
6. يجب أن تفتح صفحة التتبع

### 2. اختبار الخريطة:
1. بعد فتح صفحة التتبع
2. يجب أن تظهر خريطة Google Maps
3. يجب أن تظهر علامة زرقاء لموقعك
4. يجب أن تظهر علامة حمراء للسائق
5. يجب أن تتحرك العلامة الحمراء كل 3 ثواني

## ⚠️ مشاكل شائعة وحلولها

### 1. "No location permissions are defined"
**الحل:** ✅ تم إصلاحه - الأذونات مضافة الآن

### 2. "API key not found"
**الحل:** أضف Google Maps API Key في الملفات المذكورة أعلاه

### 3. "This app is not authorized"
**الحل:** 
- تأكد من إضافة package name في Google Cloud Console
- تأكد من تفعيل Maps SDK

### 4. الخريطة لا تظهر
**الحل:**
- تأكد من الاتصال بالإنترنت
- تأكد من صحة API Key
- تحقق من console للأخطاء

### 5. أذونات الموقع لا تعمل
**الحل:**
- أعد تشغيل التطبيق بعد إضافة الأذونات
- اختبر على جهاز حقيقي وليس محاكي
- تأكد من تفعيل الموقع في الجهاز

## 📱 اختبار على الأجهزة

### Android:
```bash
flutter run -d android
```

### iOS:
```bash
flutter run -d ios
```

### الويب (للاختبار فقط):
```bash
flutter run -d chrome
```

## 🎯 النتيجة المتوقعة

بعد إكمال هذه الخطوات، يجب أن يعمل النظام كالتالي:

1. **✅ أذونات GPS** - يطلب الإذن تلقائياً
2. **✅ خريطة تفاعلية** - تظهر مع العلامات
3. **✅ تتبع مباشر** - تحديث كل 3 ثواني
4. **✅ واجهة جميلة** - متسقة مع التطبيق

## 📞 المساعدة

إذا واجهت أي مشاكل:
1. تأكد من إضافة Google Maps API Key
2. تأكد من تفعيل Maps SDK في Google Cloud
3. أعد تشغيل التطبيق بعد التغييرات
4. اختبر على جهاز حقيقي

## 🎉 مبروك!

النظام جاهز للاستخدام! يمكن للمستخدمين الآن:
- 🗺️ تتبع طلباتهم على خريطة حقيقية
- 📍 رؤية موقعهم وموقع السائق
- ⏰ معرفة الوقت المتوقع والمسافة
- 🔐 منح أذونات GPS بسهولة

كل شيء يعمل بسلاسة! 🚀
