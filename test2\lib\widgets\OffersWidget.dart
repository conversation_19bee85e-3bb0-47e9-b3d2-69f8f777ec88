import 'package:flutter/material.dart';
import '../utils/OffersManager.dart';
import '../widgets/OptimizedImage.dart';
import '../pages/StoreDetailsPage.dart';
import '../utils/DataManager.dart';

class OffersWidget extends StatelessWidget {
  final String? searchQuery;

  const OffersWidget({
    Key? key,
    this.searchQuery,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // الحصول على العروض (مع البحث إذا وجد)
    List<Map<String, dynamic>> offers;

    if (searchQuery != null && searchQuery!.isNotEmpty) {
      offers = OffersManager.searchOffers(searchQuery!);
    } else {
      offers = OffersManager.getValidOffers();
    }

    // ترتيب العروض حسب الأولوية
    offers = OffersManager.sortOffersByPriority(offers);
    print("عدد العروض: ${offers.length}");

    if (offers.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        Container(
          height: 150,
          margin: const EdgeInsets.symmetric(vertical: 10),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            reverse: true, // عكس الاتجاه ليبدأ من اليسار إلى اليمين
            padding: const EdgeInsets.symmetric(horizontal: 15),
            itemCount:
                offers.length > 6 ? 6 : offers.length, // عرض 6 عروض كحد أقصى
            itemBuilder: (context, index) {
              final offer = offers[index];
              return OfferCard(
                key: ValueKey('offer_${offer["id"]}'),
                offer: offer,
              );
            },
          ),
        ),

        // زر عرض المزيد
        if (offers.length > 6)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 7),
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () {
                Navigator.pushNamed(context, '/offers');
              },
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: Color(0xFF4C53A5)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "عرض جميع العروض",
                    style: TextStyle(
                      color: Color(0xFF4C53A5),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(width: 5),
                  Icon(
                    Icons.arrow_forward,
                    color: Color(0xFF4C53A5),
                    size: 13,
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}

class OfferCard extends StatelessWidget {
  final Map<String, dynamic> offer;

  const OfferCard({
    Key? key,
    required this.offer,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final offerType = offer['type'] ?? '';
    final backgroundColor =
        offer['backgroundColor'] ?? OffersManager.getOfferColor(offerType);
    final textColor = offer['textColor'] ?? Colors.white;
    final daysRemaining = OffersManager.getDaysRemaining(offer);

    return Container(
      width: 200,
      margin:
          const EdgeInsets.only(left: 15), // تغيير الاتجاه من اليسار إلى اليمين
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withOpacity(0.4),
            spreadRadius: 2,
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          children: [
            // صورة الخلفية
            if (offer['backgroundImage'] != null)
              Positioned.fill(
                child: OptimizedImage(
                  imagePath: offer['backgroundImage'],
                  fit: BoxFit.cover,
                ),
              ),

            // طبقة التدرج فوق الصورة (شفافية أقل لإظهار الصورة)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                    colors: [
                      backgroundColor.withOpacity(0.4), // شفافية أقل
                      backgroundColor.withOpacity(0.6), // شفافية أقل
                    ],
                  ),
                ),
              ),
            ),

            // المحتوى
            _buildCardContent(context, textColor, offerType, daysRemaining),
          ],
        ),
      ),
    );
  }

  Widget _buildCardContent(BuildContext context, Color textColor,
      String offerType, int daysRemaining) {
    return Container(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _onOfferTap(context),
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الصف العلوي - نوع العرض والأيام المتبقية
                Expanded(
                    child: SingleChildScrollView(
                        child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // نوع العرض
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 5, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            offer['icon'] ??
                                OffersManager.getOfferIcon(offerType),
                            color: textColor,
                            size: 13,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            OffersManager.getOfferTypeText(offerType),
                            style: TextStyle(
                              color: textColor,
                              fontSize: 9,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // الأيام المتبقية
                    if (daysRemaining >= 0)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          daysRemaining == 0
                              ? "ينتهي اليوم"
                              : "$daysRemaining أيام",
                          style: TextStyle(
                            color:
                                daysRemaining <= 2 ? Colors.red : Colors.black,
                            fontSize: 11,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ))),

                const SizedBox(height: 3),

                // عنوان العرض الرئيسي
                Text(
                  offer['title'] ?? '',
                  style: TextStyle(
                    color: textColor,
                    fontSize: 12, // خط أكبر
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 2,
                        color: Colors.black.withOpacity(0.5),
                      ),
                    ],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.right,
                ),

                const SizedBox(height: 1),

                // العنوان الفرعي
                Text(
                  offer['subtitle'] ?? '',
                  style: TextStyle(
                    color: textColor.withOpacity(0.95),
                    fontSize: 11, // خط أكبر
                    fontWeight: FontWeight.w600,
                    shadows: [
                      Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 2,
                        color: Colors.black.withOpacity(0.3),
                      ),
                    ],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.right,
                ),

                const SizedBox(height: 1),

                // وصف مختصر
                Text(
                  offer['description'] ?? '',
                  style: TextStyle(
                    color: textColor.withOpacity(0.85),
                    fontSize: 11,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.right,
                ),

                const Spacer(),

                // الصف السفلي - اسم المتجر والصورة

                Expanded(
                    child: SingleChildScrollView(
                        child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  textDirection: TextDirection.rtl,
                  children: [
                    // صورة العرض/المتجر
                    Container(
                      width: 10, // حجم أكبر
                      height: 10, // حجم أكبر
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(18),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.5),
                          width: 2,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: OptimizedImage(
                          imagePath: offer['image'] ?? 'images/1.png',
                          width: 10,
                          height: 10,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // اسم المتجر
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        textDirection: TextDirection.rtl,
                        children: [
                          // Text(
                          //   "من",
                          //   style: TextStyle(
                          //     color: textColor.withOpacity(0.7),
                          //     fontSize: 10, // خط أكبر
                          //     shadows: [
                          //       Shadow(
                          //         offset: Offset(1, 1),
                          //         blurRadius: 1,
                          //         color: Colors.black.withOpacity(0.3),
                          //       ),
                          //     ],
                          //   ),
                          //   textAlign: TextAlign.right,
                          // ),
                          Text(
                            offer['storeName'] ?? '',
                            style: TextStyle(
                              color: textColor,
                              fontSize: 12, // خط أكبر
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  offset: Offset(1, 1),
                                  blurRadius: 2,
                                  color: Colors.black.withOpacity(0.5),
                                ),
                              ],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.right,
                          ),
                        ],
                      ),
                    ),
                  ],
                ))),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onOfferTap(BuildContext context) {
    // عرض تفاصيل العرض
    showDialog(
      context: context,
      builder: (context) => OfferDetailsDialog(offer: offer),
    );
  }
}

class OfferDetailsDialog extends StatelessWidget {
  final Map<String, dynamic> offer;

  const OfferDetailsDialog({
    Key? key,
    required this.offer,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final offerType = offer['type'] ?? '';
    final backgroundColor =
        offer['backgroundColor'] ?? OffersManager.getOfferColor(offerType);
    final daysRemaining = OffersManager.getDaysRemaining(offer);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              backgroundColor.withOpacity(0.1),
              backgroundColor.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          textDirection: TextDirection.rtl,
          children: [
            // رأس الحوار
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              textDirection: TextDirection.rtl,
              children: [
                Row(
                  textDirection: TextDirection.rtl,
                  children: [
                    Icon(
                      offer['icon'] ?? OffersManager.getOfferIcon(offerType),
                      color: backgroundColor,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      OffersManager.getOfferTypeText(offerType),
                      style: TextStyle(
                        color: backgroundColor,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const SizedBox(height: 15),

            // عنوان العرض
            Text(
              offer['title'] ?? '',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
              textAlign: TextAlign.right,
            ),

            const SizedBox(height: 10),

            // وصف العرض
            Text(
              offer['description'] ?? '',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.right,
            ),

            const SizedBox(height: 15),

            // معلومات إضافية
            if (offer['discountPercentage'] != null)
              _buildInfoRow(
                Icons.local_offer,
                "نسبة الخصم",
                "${offer['discountPercentage']}%",
                backgroundColor,
              ),

            if (offer['minOrderAmount'] != null)
              _buildInfoRow(
                Icons.shopping_cart,
                "الحد الأدنى للطلب",
                "${offer['minOrderAmount']} ريال",
                backgroundColor,
              ),

            _buildInfoRow(
              Icons.store,
              "المتجر",
              offer['storeName'] ?? '',
              backgroundColor,
            ),

            if (daysRemaining >= 0)
              _buildInfoRow(
                Icons.access_time,
                "ينتهي خلال",
                daysRemaining == 0 ? "اليوم" : "$daysRemaining أيام",
                daysRemaining <= 2 ? Colors.red : backgroundColor,
              ),

            const SizedBox(height: 20),

            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // الانتقال إلى المتجر
                      _navigateToStore(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: backgroundColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      "زيارة المتجر",
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // مشاركة العرض
                      _shareOffer(context);
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: backgroundColor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      "مشاركة",
                      style: TextStyle(color: backgroundColor),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Row(
        textDirection: TextDirection.rtl,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 10),
          Text(
            "$label: ",
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToStore(BuildContext context) {
    // البحث عن المتجر في DataManager باستخدام storeId
    final storeId = offer['storeId'];
    if (storeId != null) {
      // البحث عن المتجر في قائمة المتاجر
      final stores = DataManager.allStores;
      final store = stores.firstWhere(
        (s) => s['id'] == storeId || s['name'] == offer['storeName'],
        orElse: () => {
          'id': storeId,
          'name': offer['storeName'] ?? 'متجر غير معروف',
          'category': 'عام',
          'image': offer['image'] ?? 'images/1.png',
          'rating': '4.5',
          'deliveryTime': '30 دقيقة',
          'description': 'متجر يقدم عروض رائعة',
        },
      );

      // الانتقال إلى صفحة تفاصيل المتجر
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StoreDetailsPage(store: store),
        ),
      );
    } else {
      // في حالة عدم وجود معرف المتجر، عرض رسالة
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("عذراً، لا يمكن الوصول إلى ${offer['storeName']}"),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _shareOffer(BuildContext context) {
    // مشاركة العرض
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("تم نسخ رابط العرض"),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
