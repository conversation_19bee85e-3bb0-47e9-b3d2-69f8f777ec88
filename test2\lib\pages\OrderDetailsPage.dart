import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/OrderProvider.dart';
import '../providers/CustomerDataProvider.dart';
import '../models/Order.dart';
import '../models/CartItem.dart';
import '../models/Address.dart';
import '../widgets/CustomSnackBars.dart';
import 'OrderTrackingPage.dart';

class OrderDetailsPage extends StatefulWidget {
  final String orderId;

  const OrderDetailsPage({Key? key, required this.orderId}) : super(key: key);

  @override
  _OrderDetailsPageState createState() => _OrderDetailsPageState();
}

class _OrderDetailsPageState extends State<OrderDetailsPage> {
  Order? _order;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrder();
  }

  /// تحميل بيانات الطلب
  Future<void> _loadOrder() async {
    try {
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final order = orderProvider.getOrderById(widget.orderId);

      if (order != null) {
        setState(() {
          _order = order;
          _isLoading = false;
        });
      } else {
        // محاولة تحديث الطلب من الخدمة
        await orderProvider.refreshOrder(widget.orderId);
        final updatedOrder = orderProvider.getOrderById(widget.orderId);

        setState(() {
          _order = updatedOrder;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      CustomSnackBars.showError(
        context,
        message: 'فشل في تحميل تفاصيل الطلب',
        subtitle: e.toString(),
      );
    }
  }

  /// فتح صفحة تتبع الطلب
  void _openOrderTracking() {
    if (_order != null && _order!.canTrack) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => OrderTrackingPage(orderId: widget.orderId),
        ),
      );
    } else {
      CustomSnackBars.showInfo(
        context,
        message: 'لا يمكن تتبع هذا الطلب',
        subtitle: 'الطلب غير قابل للتتبع في الوقت الحالي',
      );
    }
  }

  /// إلغاء الطلب
  void _cancelOrder() {
    if (_order != null && _order!.canCancel) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('إلغاء الطلب'),
          content: Text('هل أنت متأكد من إلغاء الطلب #${_order!.id}؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('لا'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                final orderProvider =
                    Provider.of<OrderProvider>(context, listen: false);
                orderProvider
                    .cancelOrder(_order!.id, 'إلغاء من العميل')
                    .then((success) {
                  if (success) {
                    CustomSnackBars.showSuccess(
                      context,
                      message: 'تم إلغاء الطلب بنجاح',
                      subtitle: 'سيتم استرداد المبلغ خلال 3-5 أيام عمل',
                    );
                    _loadOrder(); // إعادة تحميل الطلب
                  } else {
                    CustomSnackBars.showError(
                      context,
                      message: 'فشل في إلغاء الطلب',
                      subtitle: 'يرجى المحاولة مرة أخرى',
                    );
                  }
                });
              },
              child: Text('نعم', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: AppBar(
        backgroundColor: Color(0xFF4C53A5),
        elevation: 0,
        centerTitle: true,
        title: Text(
          'تفاصيل الطلب',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: Color(0xFF4C53A5),
              ),
            )
          : _order == null
              ? _buildErrorState()
              : RefreshIndicator(
                  onRefresh: _loadOrder,
                  child: ListView(
                    padding: EdgeInsets.all(16),
                    children: [
                      _buildOrderHeader(),
                      SizedBox(height: 16),
                      _buildOrderStatusCard(),
                      SizedBox(height: 16),
                      _buildCustomerInfoCard(),
                      SizedBox(height: 16),
                      _buildOrderItemsList(),
                      SizedBox(height: 16),
                      _buildDeliveryAddressCard(),
                      SizedBox(height: 16),
                      _buildPaymentDetailsCard(),
                      SizedBox(height: 16),
                      if (_order!.notes != null && _order!.notes!.isNotEmpty)
                        _buildNotesCard(),
                      SizedBox(height: 16),
                      _buildStatusHistoryCard(),
                      SizedBox(height: 100), // مساحة للأزرار السفلية
                    ],
                  ),
                ),
      bottomNavigationBar: _order != null ? _buildBottomActions() : null,
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'لم يتم العثور على الطلب',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'تحقق من رقم الطلب وحاول مرة أخرى',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          SizedBox(height: 20),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFF4C53A5),
            ),
            child: Text(
              'العودة',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الطلب
  Widget _buildOrderHeader() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'طلب #${_order!.id}',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF4C53A5),
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    _order!.storeName ?? 'متجر غير معروف',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: OrderStatusHelper.getStatusColor(_order!.status)
                      .withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: OrderStatusHelper.getStatusColor(_order!.status)
                        .withOpacity(0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      OrderStatusHelper.getStatusIcon(_order!.status),
                      style: TextStyle(fontSize: 16),
                    ),
                    SizedBox(width: 4),
                    Text(
                      OrderStatusHelper.getStatusName(_order!.status),
                      style: TextStyle(
                        color: OrderStatusHelper.getStatusColor(_order!.status),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
              SizedBox(width: 4),
              Text(
                'تاريخ الطلب: ${_formatDateTime(_order!.createdAt)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          if (_order!.estimatedTimeRemaining != null) ...[
            SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.timer, size: 16, color: Colors.orange),
                SizedBox(width: 4),
                Text(
                  'الوقت المتبقي المقدر: ${_formatDuration(_order!.estimatedTimeRemaining!)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.orange[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOrderStatusCard() {
    return Container(
      padding: EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        textDirection: TextDirection.rtl,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "حالة الطلب",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 15),
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: OrderStatusHelper.getStatusColor(_order!.status)
                  .withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                  color: OrderStatusHelper.getStatusColor(_order!.status)
                      .withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: OrderStatusHelper.getStatusColor(_order!.status)
                        .withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    OrderStatusHelper.getStatusIcon(_order!.status),
                    style: TextStyle(fontSize: 24),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        OrderStatusHelper.getStatusName(_order!.status),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color:
                              OrderStatusHelper.getStatusColor(_order!.status),
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        OrderStatusHelper.getStatusMessage(_order!.status),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                          height: 1.3,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItemsList() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المنتجات (${_order!.items.length})',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 16),
          ...(_order!.items.map((item) => _buildOrderItem(item)).toList()),
        ],
      ),
    );
  }

  /// بناء عنصر المنتج
  Widget _buildOrderItem(CartItem item) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // صورة المنتج
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Color(0xFFEDECF2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                item.imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.grey[400],
                      size: 30,
                    ),
                  );
                },
              ),
            ),
          ),
          SizedBox(width: 12),

          // تفاصيل المنتج
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C53A5),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4),
                Text(
                  item.category,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      'الكمية: ${item.quantity}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[700],
                      ),
                    ),
                    Text(
                      ' × ${item.price.toStringAsFixed(2)} ريال',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // السعر الإجمالي
          Text(
            '${item.totalPrice.toStringAsFixed(2)} ريال',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryAddressCard() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'عنوان التوصيل',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 16),
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      AddressType.getIcon(_order!.deliveryAddress.title),
                      style: TextStyle(fontSize: 18),
                    ),
                    SizedBox(width: 8),
                    Text(
                      _order!.deliveryAddress.title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF4C53A5),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.location_on,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                    SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        _order!.deliveryAddress.formattedAddress,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
                if (_order!.deliveryAddress.landmark != null &&
                    _order!.deliveryAddress.landmark!.isNotEmpty) ...[
                  SizedBox(height: 8),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.place,
                        color: Colors.grey[600],
                        size: 16,
                      ),
                      SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          'علامة مميزة: ${_order!.deliveryAddress.landmark}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentDetailsCard() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل الدفع',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 16),

          // المجموع الفرعي
          _buildPaymentRow('المجموع الفرعي', _order!.subtotal),
          SizedBox(height: 8),

          // رسوم التوصيل
          _buildPaymentRow('رسوم التوصيل', _order!.deliveryFee),
          SizedBox(height: 8),

          // الضريبة
          _buildPaymentRow('ضريبة القيمة المضافة (15%)', _order!.tax),
          SizedBox(height: 8),

          // الخصم (إن وجد)
          if (_order!.discount > 0) ...[
            _buildPaymentRow('الخصم', -_order!.discount, isDiscount: true),
            SizedBox(height: 8),
          ],

          Divider(thickness: 1),
          SizedBox(height: 8),

          // الإجمالي
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الإجمالي',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
              Text(
                '${_order!.total.toStringAsFixed(2)} ريال',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),

          SizedBox(height: 16),

          // طريقة الدفع
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Row(
              children: [
                Icon(
                  _getPaymentIcon(_order!.paymentMethod),
                  color: Color(0xFF4C53A5),
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  'طريقة الدفع: ${_getPaymentMethodName(_order!.paymentMethod)}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صف الدفع
  Widget _buildPaymentRow(String label, double amount,
      {bool isDiscount = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[700],
          ),
        ),
        Text(
          '${amount.toStringAsFixed(2)} ريال',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDiscount ? Colors.green : Colors.black87,
          ),
        ),
      ],
    );
  }

  /// الحصول على أيقونة طريقة الدفع
  IconData _getPaymentIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.card:
        return Icons.credit_card;
      case PaymentMethod.applePay:
        return Icons.apple;
      case PaymentMethod.stcPay:
        return Icons.phone_android;
      case PaymentMethod.mada:
        return Icons.credit_card;
      default:
        return Icons.payment;
    }
  }

  /// الحصول على اسم طريقة الدفع
  String _getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'نقدي عند التوصيل';
      case PaymentMethod.card:
        return 'بطاقة ائتمان';
      case PaymentMethod.applePay:
        return 'آبل باي';
      case PaymentMethod.stcPay:
        return 'STC Pay';
      case PaymentMethod.mada:
        return 'مدى';
      default:
        return 'غير محدد';
    }
  }

  /// بناء بطاقة معلومات العميل
  Widget _buildCustomerInfoCard() {
    return Consumer<CustomerDataProvider>(
      builder: (context, customerProvider, child) {
        return Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 5,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'معلومات العميل',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
              SizedBox(height: 12),
              _buildInfoRow(
                  Icons.person,
                  'الاسم',
                  customerProvider.fullName.isNotEmpty
                      ? customerProvider.fullName
                      : _order!.userName),
              SizedBox(height: 8),
              _buildInfoRow(
                  Icons.phone,
                  'الهاتف',
                  customerProvider.phone.isNotEmpty
                      ? customerProvider.phone
                      : _order!.userPhone),
              SizedBox(height: 8),
              _buildInfoRow(
                  Icons.email,
                  'البريد الإلكتروني',
                  customerProvider.email.isNotEmpty
                      ? customerProvider.email
                      : _order!.userEmail),
              if (customerProvider.customerId.isNotEmpty) ...[
                SizedBox(height: 8),
                _buildInfoRow(
                    Icons.badge, 'معرف العميل', customerProvider.customerId),
              ],
            ],
          ),
        );
      },
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة الملاحظات
  Widget _buildNotesCard() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملاحظات الطلب',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 12),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Text(
              _order!.notes!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة تاريخ الحالات
  Widget _buildStatusHistoryCard() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تاريخ الطلب',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 16),
          ...(_order!.statusHistory.reversed
              .map((update) => _buildStatusHistoryItem(update))
              .toList()),
        ],
      ),
    );
  }

  /// بناء عنصر تاريخ الحالة
  Widget _buildStatusHistoryItem(OrderStatusUpdate update) {
    final statusColor = OrderStatusHelper.getStatusColor(update.status);
    final statusIcon = OrderStatusHelper.getStatusIcon(update.status);
    final statusName = OrderStatusHelper.getStatusName(update.status);

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: statusColor.withOpacity(0.3)),
            ),
            child: Center(
              child: Text(
                statusIcon,
                style: TextStyle(fontSize: 16),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  statusName,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  update.message ??
                      OrderStatusHelper.getStatusMessage(update.status),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[700],
                  ),
                ),
                SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      _formatDateTime(update.timestamp),
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[500],
                      ),
                    ),
                    if (update.updatedBy != null) ...[
                      Text(
                        ' • ',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[500],
                        ),
                      ),
                      Text(
                        update.updatedBy!,
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الأزرار السفلية
  Widget _buildBottomActions() {
    return SafeArea(
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Text(
                  'الإجمالي: ',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  '${_order!.total.toStringAsFixed(2)} ريال',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C53A5),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            Row(
              children: [
                if (_order!.canCancel) ...[
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _cancelOrder,
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.red),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: Text(
                        'إلغاء الطلب',
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                ],
                Expanded(
                  child: ElevatedButton(
                    onPressed: _order!.canTrack ? _openOrderTracking : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFF4C53A5),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(
                      _order!.canTrack ? 'تتبع الطلب' : 'مكتمل',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return 'اليوم ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'أمس ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }

  /// تنسيق المدة الزمنية
  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}س ${duration.inMinutes % 60}د';
    } else {
      return '${duration.inMinutes}د';
    }
  }
}
