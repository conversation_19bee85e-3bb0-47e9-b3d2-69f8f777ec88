import 'package:flutter/material.dart';
import 'package:test2/widgets/OrderDetailsAppBar.dart';
import 'package:test2/pages/OrderTrackingPage.dart';
import 'package:test2/services/LocationService.dart';
import 'package:test2/models/LocationPermissionResult.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

class OrderDetailsPage extends StatefulWidget {
  final String orderId;

  OrderDetailsPage({required this.orderId});

  @override
  _OrderDetailsPageState createState() => _OrderDetailsPageState();
}

class _OrderDetailsPageState extends State<OrderDetailsPage> {
  final LocationService _locationService = LocationService();

  /// فتح صفحة تتبع الطلب مع طلب أذونات GPS
  Future<void> _openOrderTracking() async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: Container(
            padding: EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(color: Color(0xFF4C53A5)),
                SizedBox(height: 16),
                Text('جاري فحص أذونات الموقع...'),
              ],
            ),
          ),
        ),
      );

      // طلب أذونات الموقع
      LocationPermissionResult result =
          await _locationService.requestLocationPermissionInteractive();

      // إغلاق مؤشر التحميل
      Navigator.of(context).pop();

      if (result.success) {
        // تم الحصول على الأذونات - فتح صفحة التتبع
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => OrderTrackingPage(orderId: widget.orderId),
          ),
        );

        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.message),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      } else {
        // فشل في الحصول على الأذونات - عرض حوار مناسب
        _showPermissionDialog(result);
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      Navigator.of(context).pop();

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في طلب أذونات الموقع: $e'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  /// عرض حوار أذونات الموقع حسب نوع المشكلة
  void _showPermissionDialog(LocationPermissionResult result) {
    String title;
    IconData icon;
    Color iconColor;
    List<Widget> actions;

    if (result.needsSystemSettings) {
      // يحتاج لإعدادات النظام
      title = 'تفعيل خدمة الموقع';
      icon = Icons.location_off;
      iconColor = Colors.orange;
      actions = [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.of(context).pop();
            try {
              await Geolocator.openLocationSettings();
            } catch (e) {
              print('خطأ في فتح إعدادات الموقع: $e');
            }
          },
          child: Text('فتح إعدادات الجهاز'),
        ),
      ];
    } else if (result.needsAppSettings) {
      // يحتاج لإعدادات التطبيق
      title = 'أذونات التطبيق';
      icon = Icons.settings_applications;
      iconColor = Colors.red;
      actions = [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.of(context).pop();
            try {
              await openAppSettings();
            } catch (e) {
              print('خطأ في فتح إعدادات التطبيق: $e');
            }
          },
          child: Text('فتح إعدادات التطبيق'),
        ),
      ];
    } else {
      // خطأ عام
      title = 'خطأ في الأذونات';
      icon = Icons.error_outline;
      iconColor = Colors.red;
      actions = [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('إغلاق'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            _openOrderTracking(); // إعادة المحاولة
          },
          child: Text('إعادة المحاولة'),
        ),
      ];
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(icon, color: iconColor),
            SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(
          result.message,
          style: TextStyle(height: 1.5),
        ),
        actions: actions,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: OrderDetailsAppBar(),
      body: ListView(
        children: [
          Container(
            padding: EdgeInsets.all(20),
            decoration: BoxDecoration(
                color: Color(0xFFEDECF2),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(35),
                  topRight: Radius.circular(35),
                )),
            child: Column(
              textDirection: TextDirection.rtl,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "تفاصيل الطلب #${widget.orderId}",
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C53A5),
                  ),
                ),
                SizedBox(height: 20),
                _buildOrderStatusCard(),
                SizedBox(height: 20),
                _buildOrderItemsList(),
                SizedBox(height: 20),
                _buildDeliveryAddressCard(),
                SizedBox(height: 20),
                _buildPaymentDetailsCard(),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        height: 70,
        padding: EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.5),
              spreadRadius: 3,
              blurRadius: 10,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          textDirection: TextDirection.rtl,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "الإجمالي: \$120",
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4C53A5),
              ),
            ),
            ElevatedButton(
              onPressed: () => _openOrderTracking(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF4C53A5),
                padding: EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: Text(
                "تتبع الطلب",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderStatusCard() {
    return Container(
      padding: EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        textDirection: TextDirection.rtl,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "حالة الطلب",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 15),
          Row(
            textDirection: TextDirection.rtl,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatusItem(Icons.check_circle, "تم الطلب", true),
              _buildStatusDivider(true),
              _buildStatusItem(Icons.inventory, "قيد التحضير", true),
              _buildStatusDivider(false),
              _buildStatusItem(Icons.delivery_dining, "قيد التوصيل", false),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem(IconData icon, String label, bool isCompleted) {
    return Column(
      textDirection: TextDirection.rtl,
      children: [
        Icon(
          icon,
          color: isCompleted ? Colors.green : Colors.grey,
          size: 30,
        ),
        SizedBox(height: 5),
        Text(
          label,
          style: TextStyle(
            color: isCompleted ? Colors.green : Colors.grey,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusDivider(bool isCompleted) {
    return Container(
      width: 40,
      height: 2,
      color: isCompleted ? Colors.green : Colors.grey,
    );
  }

  Widget _buildOrderItemsList() {
    return Container(
      padding: EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        textDirection: TextDirection.rtl,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "المنتجات",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 15),
          for (int i = 1; i < 4; i++)
            Container(
              margin: EdgeInsets.only(bottom: 10),
              child: Row(
                textDirection: TextDirection.rtl,
                children: [
                  Container(
                    height: 60,
                    width: 60,
                    decoration: BoxDecoration(
                      color: Color(0xFFEDECF2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Image.asset("images/product-$i.png"),
                  ),
                  SizedBox(width: 10),
                  Expanded(
                    child: Column(
                      textDirection: TextDirection.rtl,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "منتج $i",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF4C53A5),
                          ),
                        ),
                        Text(
                          "العدد: ${i}",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    "\$${i * 30}",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF4C53A5),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDeliveryAddressCard() {
    return Container(
      padding: EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        textDirection: TextDirection.rtl,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "عنوان التوصيل",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 15),
          Row(
            textDirection: TextDirection.rtl,
            children: [
              Icon(Icons.location_on, color: Color(0xFF4C53A5)),
              SizedBox(width: 10),
              Expanded(
                child: Text(
                  "شارع الملك فهد، حي الورود، الرياض، المملكة العربية السعودية",
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentDetailsCard() {
    return Container(
      padding: EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        textDirection: TextDirection.rtl,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "تفاصيل الدفع",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4C53A5),
            ),
          ),
          SizedBox(height: 15),
          Row(
            textDirection: TextDirection.rtl,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "المجموع الفرعي",
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
              Text(
                "\$100",
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
            ],
          ),
          SizedBox(height: 10),
          Row(
            textDirection: TextDirection.rtl,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "رسوم التوصيل",
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
              Text(
                "\$20",
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
            ],
          ),
          Divider(height: 20),
          Row(
            textDirection: TextDirection.rtl,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "الإجمالي",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
              Text(
                "\$120",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C53A5),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
