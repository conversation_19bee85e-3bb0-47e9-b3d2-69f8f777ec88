import 'package:flutter/material.dart';
import 'package:test2/utils/ResponsiveHelper.dart';

class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? mobileWidth;
  final double? tabletWidth;
  final double? desktopWidth;
  final double? mobileHeight;
  final double? tabletHeight;
  final double? desktopHeight;
  final double? mobilePadding;
  final double? tabletPadding;
  final double? desktopPadding;
  final double? mobileMargin;
  final double? tabletMargin;
  final double? desktopMargin;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final BoxShadow? boxShadow;
  final Border? border;
  final AlignmentGeometry? alignment;

  const ResponsiveContainer({
    Key? key,
    required this.child,
    this.mobileWidth,
    this.tabletWidth,
    this.desktopWidth,
    this.mobileHeight,
    this.tabletHeight,
    this.desktopHeight,
    this.mobilePadding,
    this.tabletPadding,
    this.desktopPadding,
    this.mobileMargin,
    this.tabletMargin,
    this.desktopMargin,
    this.backgroundColor,
    this.borderRadius,
    this.boxShadow,
    this.border,
    this.alignment,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final width = mobileWidth != null
        ? ResponsiveHelper.responsiveSize(
            context,
            mobile: mobileWidth!,
            tablet: tabletWidth,
            desktop: desktopWidth,
          )
        : null;

    final height = mobileHeight != null
        ? ResponsiveHelper.responsiveSize(
            context,
            mobile: mobileHeight!,
            tablet: tabletHeight,
            desktop: desktopHeight,
          )
        : null;

    final padding = mobilePadding != null
        ? ResponsiveHelper.responsiveEdgeInsets(
            context,
            mobile: mobilePadding!,
            tablet: tabletPadding,
            desktop: desktopPadding,
          )
        : null;

    final margin = mobileMargin != null
        ? ResponsiveHelper.responsiveEdgeInsets(
            context,
            mobile: mobileMargin!,
            tablet: tabletMargin,
            desktop: desktopMargin,
          )
        : null;

    return Container(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      alignment: alignment,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius,
        border: border,
        boxShadow: boxShadow != null ? [boxShadow!] : null,
      ),
      child: child,
    );
  }
}

class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final double? mobilePadding;
  final double? tabletPadding;
  final double? desktopPadding;
  final double? mobileMargin;
  final double? tabletMargin;
  final double? desktopMargin;
  final Color? backgroundColor;
  final double? elevation;
  final VoidCallback? onTap;

  const ResponsiveCard({
    Key? key,
    required this.child,
    this.mobilePadding = 16,
    this.tabletPadding,
    this.desktopPadding,
    this.mobileMargin = 8,
    this.tabletMargin,
    this.desktopMargin,
    this.backgroundColor,
    this.elevation = 2,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final padding = ResponsiveHelper.responsiveEdgeInsets(
      context,
      mobile: mobilePadding ?? 16,
      tablet: tabletPadding,
      desktop: desktopPadding,
    );

    final margin = ResponsiveHelper.responsiveEdgeInsets(
      context,
      mobile: mobileMargin ?? 8,
      tablet: tabletMargin,
      desktop: desktopMargin,
    );

    final borderRadius = ResponsiveHelper.responsiveBorderRadius(
      context,
      mobile: 12,
      tablet: 16,
      desktop: 20,
    );

    Widget cardWidget = Card(
      margin: margin,
      elevation: elevation,
      color: backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Padding(
        padding: padding,
        child: child,
      ),
    );

    if (onTap != null) {
      cardWidget = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(borderRadius),
        child: cardWidget,
      );
    }

    return cardWidget;
  }
}

class ResponsiveRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final bool wrapOnMobile;
  final double? spacing;

  const ResponsiveRow({
    Key? key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.wrapOnMobile = false,
    this.spacing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (wrapOnMobile && ResponsiveHelper.isMobile(context)) {
      return Wrap(
        spacing: spacing ?? ResponsiveHelper.responsiveSpacing(context),
        runSpacing: spacing ?? ResponsiveHelper.responsiveSpacing(context),
        children: children,
      );
    }

    List<Widget> spacedChildren = [];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1 && spacing != null) {
        spacedChildren.add(SizedBox(width: spacing));
      }
    }

    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: spacedChildren,
    );
  }
}

class ResponsiveColumn extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final double? spacing;

  const ResponsiveColumn({
    Key? key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.spacing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    List<Widget> spacedChildren = [];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1 && spacing != null) {
        spacedChildren.add(SizedBox(height: spacing));
      }
    }

    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: spacedChildren,
    );
  }
}

class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final int mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final double? childAspectRatio;
  final double? spacing;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const ResponsiveGridView({
    Key? key,
    required this.children,
    this.mobileColumns = 2,
    this.tabletColumns,
    this.desktopColumns,
    this.childAspectRatio = 1.0,
    this.spacing,
    this.shrinkWrap = false,
    this.physics,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveHelper.responsiveGridColumns(
      context,
      mobile: mobileColumns,
      tablet: tabletColumns,
      desktop: desktopColumns,
    );

    final gridSpacing = spacing ?? ResponsiveHelper.responsiveSpacing(context);

    return GridView.builder(
      shrinkWrap: shrinkWrap,
      physics: physics,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        childAspectRatio: childAspectRatio ?? 1.0,
        crossAxisSpacing: gridSpacing,
        mainAxisSpacing: gridSpacing,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
}

class ResponsiveSizedBox extends StatelessWidget {
  final double? mobileWidth;
  final double? tabletWidth;
  final double? desktopWidth;
  final double? mobileHeight;
  final double? tabletHeight;
  final double? desktopHeight;
  final Widget? child;

  const ResponsiveSizedBox({
    Key? key,
    this.mobileWidth,
    this.tabletWidth,
    this.desktopWidth,
    this.mobileHeight,
    this.tabletHeight,
    this.desktopHeight,
    this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final width = mobileWidth != null
        ? ResponsiveHelper.responsiveSize(
            context,
            mobile: mobileWidth!,
            tablet: tabletWidth,
            desktop: desktopWidth,
          )
        : null;

    final height = mobileHeight != null
        ? ResponsiveHelper.responsiveSize(
            context,
            mobile: mobileHeight!,
            tablet: tabletHeight,
            desktop: desktopHeight,
          )
        : null;

    return SizedBox(
      width: width,
      height: height,
      child: child,
    );
  }
}
