import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:test2/models/User.dart';

class UserService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  static const String _userKey = 'current_user';
  static const String _isFirstTimeKey = 'is_first_time';

  User? _currentUser;

  // الحصول على المستخدم الحالي
  User? get currentUser => _currentUser;

  // التحقق من وجود مستخدم مسجل
  bool get isLoggedIn => _currentUser != null && !_currentUser!.isGuest;

  // التحقق من أن المستخدم ضيف
  bool get isGuest => _currentUser != null && _currentUser!.isGuest;

  // التحقق من أول مرة فتح التطبيق
  Future<bool> isFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isFirstTimeKey) ?? true;
  }

  // تحديد أنه ليس أول مرة
  Future<void> setNotFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isFirstTimeKey, false);
  }

  // تحميل بيانات المستخدم من التخزين المحلي
  Future<void> loadUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);

      if (userJson != null) {
        final userMap = jsonDecode(userJson);
        _currentUser = User.fromMap(userMap);
      }
    } catch (e) {
      print('خطأ في تحميل بيانات المستخدم: $e');
    }
  }

  // حفظ بيانات المستخدم
  Future<bool> saveUser(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = jsonEncode(user.toMap());
      await prefs.setString(_userKey, userJson);
      _currentUser = user;
      return true;
    } catch (e) {
      print('خطأ في حفظ بيانات المستخدم: $e');
      return false;
    }
  }

  // تسجيل مستخدم جديد
  Future<UserRegistrationResult> registerUser({
    required String name,
    required String phone,
    String? email,
    String? address,
    String? city,
    double? latitude,
    double? longitude,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (name.trim().isEmpty) {
        return UserRegistrationResult(
          success: false,
          message: 'يرجى إدخال الاسم',
        );
      }

      if (phone.trim().isEmpty) {
        return UserRegistrationResult(
          success: false,
          message: 'يرجى إدخال رقم الهاتف',
        );
      }

      // التحقق من صحة رقم الهاتف اليمني
      if (!_isValidYemeniPhone(phone)) {
        return UserRegistrationResult(
          success: false,
          message: 'رقم الهاتف غير صحيح. يجب أن يبدأ بـ 77، 73، 70، أو 71',
        );
      }

      // إنشاء مستخدم جديد
      final user = User(
        id: 'user_${DateTime.now().millisecondsSinceEpoch}',
        name: name.trim(),
        phone: phone.trim(),
        email: email?.trim(),
        address: address?.trim(),
        city: city?.trim(),
        latitude: latitude,
        longitude: longitude,
        createdAt: DateTime.now(),
      );

      // حفظ المستخدم
      final saved = await saveUser(user);

      if (saved) {
        await setNotFirstTime();
        return UserRegistrationResult(
          success: true,
          message: 'تم التسجيل بنجاح',
          user: user,
        );
      } else {
        return UserRegistrationResult(
          success: false,
          message: 'فشل في حفظ بيانات المستخدم',
        );
      }
    } catch (e) {
      return UserRegistrationResult(
        success: false,
        message: 'حدث خطأ أثناء التسجيل: ${e.toString()}',
      );
    }
  }

  // تسجيل دخول كضيف
  Future<bool> loginAsGuest() async {
    try {
      final guestUser = User.guest();
      final saved = await saveUser(guestUser);

      if (saved) {
        await setNotFirstTime();
        return true;
      }
      return false;
    } catch (e) {
      print('خطأ في تسجيل الدخول كضيف: $e');
      return false;
    }
  }

  // تحديث بيانات المستخدم
  Future<bool> updateUser({
    String? name,
    String? phone,
    String? email,
    String? address,
    String? city,
    double? latitude,
    double? longitude,
  }) async {
    if (_currentUser == null) return false;

    try {
      final updatedUser = _currentUser!.copyWith(
        name: name ?? _currentUser!.name,
        phone: phone ?? _currentUser!.phone,
        email: email ?? _currentUser!.email,
        address: address ?? _currentUser!.address,
        city: city ?? _currentUser!.city,
        latitude: latitude ?? _currentUser!.latitude,
        longitude: longitude ?? _currentUser!.longitude,
        updatedAt: DateTime.now(),
      );

      return await saveUser(updatedUser);
    } catch (e) {
      print('خطأ في تحديث بيانات المستخدم: $e');
      return false;
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
      _currentUser = null;
    } catch (e) {
      print('خطأ في تسجيل الخروج: $e');
    }
  }

  // مسح بيانات الضيف والعودة لحالة البداية
  Future<void> clearGuestData() async {
    try {
      if (_currentUser != null && _currentUser!.isGuest) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(_userKey);
        _currentUser = null;
      }
    } catch (e) {
      print('خطأ في مسح بيانات الضيف: $e');
    }
  }

  // التحقق من أن المستخدم يحتاج للتسجيل
  bool needsRegistration() {
    return _currentUser == null ||
        _currentUser!.isGuest ||
        !_currentUser!.isComplete;
  }

  // حذف الحساب
  Future<void> deleteAccount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
      await prefs.remove(_isFirstTimeKey);
      _currentUser = null;
    } catch (e) {
      print('خطأ في حذف الحساب: $e');
    }
  }

  // التحقق من صحة رقم الهاتف اليمني
  bool _isValidYemeniPhone(String phone) {
    // إزالة المسافات والرموز
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

    // التحقق من الطول (9 أرقام)
    if (cleanPhone.length != 9) return false;

    // التحقق من بداية الرقم
    final validPrefixes = ['77', '73', '70', '71'];
    return validPrefixes.any((prefix) => cleanPhone.startsWith(prefix));
  }

  // الحصول على معلومات المستخدم للعرض
  Map<String, String> getUserDisplayInfo() {
    if (_currentUser == null) {
      return {
        'name': 'غير مسجل',
        'phone': '',
        'address': 'لم يتم تحديد العنوان',
        'status': 'غير مسجل',
      };
    }

    return {
      'name': _currentUser!.displayName,
      'phone': _currentUser!.phone,
      'address': _currentUser!.shortAddress,
      'status': _currentUser!.isGuest ? 'ضيف' : 'مسجل',
    };
  }

  // التحقق من إمكانية الطلب
  bool canPlaceOrder() {
    if (_currentUser == null) return false;
    if (_currentUser!.isGuest) return true; // الضيف يمكنه الطلب
    return _currentUser!.isComplete; // المستخدم المسجل يحتاج بيانات كاملة
  }

  // الحصول على رسالة تنبيه للطلب
  String? getOrderWarningMessage() {
    if (_currentUser == null) {
      return 'يرجى تسجيل الدخول أو التصفح كضيف للمتابعة';
    }

    if (_currentUser!.isGuest) {
      return 'أنت تتصفح كضيف. ستحتاج لإدخال معلومات التوصيل عند الطلب';
    }

    if (!_currentUser!.isComplete) {
      return 'يرجى إكمال بيانات الملف الشخصي للمتابعة';
    }

    return null;
  }
}

// فئة نتيجة التسجيل
class UserRegistrationResult {
  final bool success;
  final String message;
  final User? user;

  UserRegistrationResult({
    required this.success,
    required this.message,
    this.user,
  });
}
