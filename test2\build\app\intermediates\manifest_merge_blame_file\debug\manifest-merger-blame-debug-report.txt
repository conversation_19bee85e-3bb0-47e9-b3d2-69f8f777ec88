1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.test2"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:6:5-67
15-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:6:22-64
16    <!-- أذونات الموقع المطلوبة لتتبع الطلبات -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:4:5-79
17-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:4:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- إذن للوصول لحالة الشبكة -->
18-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:5:5-81
18-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:5:22-78
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:9:5-79
19-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:9:22-76
20    <!--
21 Required to query activities that can process text, see:
22         https://developer.android.com/training/package-visibility and
23         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
24
25         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
26    -->
27    <queries>
27-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:52:5-57:15
28        <intent>
28-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:53:9-56:18
29            <action android:name="android.intent.action.PROCESS_TEXT" />
29-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:54:13-72
29-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:54:21-70
30
31            <data android:mimeType="text/plain" />
31-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:55:13-50
31-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:55:19-48
32        </intent>
33        <!-- Needs to be explicitly declared on Android R+ -->
34        <package android:name="com.google.android.apps.maps" />
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
35    </queries>
36
37    <uses-permission android:name="android.permission.VIBRATE" />
37-->[:flutter_local_notifications] F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-66
37-->[:flutter_local_notifications] F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-63
38    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
38-->[:flutter_local_notifications] F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-77
38-->[:flutter_local_notifications] F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-74
39
40    <uses-feature
40-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
41        android:glEsVersion="0x00020000"
41-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
42        android:required="true" />
42-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
43
44    <permission
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
45        android:name="com.example.test2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.example.test2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
49
50    <application
51        android:name="android.app.Application"
52        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
53        android:debuggable="true"
54        android:extractNativeLibs="true"
55        android:icon="@mipmap/ic_launcher"
56        android:label="test2" >
57        <activity
58            android:name="com.example.test2.MainActivity"
59            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
60            android:exported="true"
61            android:hardwareAccelerated="true"
62            android:launchMode="singleTop"
63            android:taskAffinity=""
64            android:theme="@style/LaunchTheme"
65            android:windowSoftInputMode="adjustResize" >
66
67            <!--
68                 Specifies an Android theme to apply to this Activity as soon as
69                 the Android process has started. This theme is visible to the user
70                 while the Flutter UI initializes. After that, this theme continues
71                 to determine the Window background behind the Flutter UI.
72            -->
73            <meta-data
74                android:name="io.flutter.embedding.android.NormalTheme"
75                android:resource="@style/NormalTheme" />
76
77            <intent-filter>
78                <action android:name="android.intent.action.MAIN" />
79
80                <category android:name="android.intent.category.LAUNCHER" />
81            </intent-filter>
82        </activity>
83        <!-- Google Maps API Key (يجب إضافة المفتاح الحقيقي هنا) -->
84        <meta-data
85            android:name="com.google.android.geo.API_KEY"
86            android:value="YOUR_GOOGLE_MAPS_API_KEY_HERE" />
87
88        <!--
89             Don't delete the meta-data below.
90             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
91        -->
92        <meta-data
93            android:name="flutterEmbedding"
94            android:value="2" />
95
96        <service
96-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:56
97            android:name="com.baseflow.geolocator.GeolocatorLocationService"
97-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-77
98            android:enabled="true"
98-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-35
99            android:exported="false"
99-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
100            android:foregroundServiceType="location" />
100-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-53
101
102        <uses-library
102-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
103            android:name="androidx.window.extensions"
103-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
104            android:required="false" />
104-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
105        <uses-library
105-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
106            android:name="androidx.window.sidecar"
106-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
107            android:required="false" /> <!-- Needs to be explicitly declared on P+ -->
107-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
108        <uses-library
108-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
109            android:name="org.apache.http.legacy"
109-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
110            android:required="false" />
110-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
111
112        <activity
112-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\07c8d82ce007794779a5c5ed16d7cd9e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
113            android:name="com.google.android.gms.common.api.GoogleApiActivity"
113-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\07c8d82ce007794779a5c5ed16d7cd9e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
114            android:exported="false"
114-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\07c8d82ce007794779a5c5ed16d7cd9e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
115            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
115-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\07c8d82ce007794779a5c5ed16d7cd9e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
116
117        <meta-data
117-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88e3d0d424506a1c1cef1c33bd4edbb3\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
118            android:name="com.google.android.gms.version"
118-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88e3d0d424506a1c1cef1c33bd4edbb3\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
119            android:value="@integer/google_play_services_version" />
119-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88e3d0d424506a1c1cef1c33bd4edbb3\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
120
121        <provider
121-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
122            android:name="androidx.startup.InitializationProvider"
122-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
123            android:authorities="com.example.test2.androidx-startup"
123-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
124            android:exported="false" >
124-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
125            <meta-data
125-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
126                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
126-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
127                android:value="androidx.startup" />
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
128            <meta-data
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
129                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
130                android:value="androidx.startup" />
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
131        </provider>
132
133        <receiver
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
134            android:name="androidx.profileinstaller.ProfileInstallReceiver"
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
135            android:directBootAware="false"
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
136            android:enabled="true"
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
137            android:exported="true"
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
138            android:permission="android.permission.DUMP" >
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
139            <intent-filter>
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
140                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
141            </intent-filter>
142            <intent-filter>
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
143                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
144            </intent-filter>
145            <intent-filter>
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
146                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
147            </intent-filter>
148            <intent-filter>
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
149                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
150            </intent-filter>
151        </receiver>
152    </application>
153
154</manifest>
