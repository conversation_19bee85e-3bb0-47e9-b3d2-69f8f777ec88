1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.test2"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:6:5-67
15-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:6:22-64
16    <!-- أذونات الموقع المطلوبة لتتبع الطلبات -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:4:5-79
17-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:4:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- إذن للوصول لحالة الشبكة -->
18-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:5:5-81
18-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:5:22-78
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:9:5-79
19-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:9:22-76
20    <!--
21 Required to query activities that can process text, see:
22         https://developer.android.com/training/package-visibility and
23         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
24
25         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
26    -->
27    <queries>
27-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:52:5-57:15
28        <intent>
28-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:53:9-56:18
29            <action android:name="android.intent.action.PROCESS_TEXT" />
29-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:54:13-72
29-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:54:21-70
30
31            <data android:mimeType="text/plain" />
31-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:55:13-50
31-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:55:19-48
32        </intent>
33        <!-- Needs to be explicitly declared on Android R+ -->
34        <package android:name="com.google.android.apps.maps" />
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
35    </queries>
36
37    <uses-permission android:name="android.permission.VIBRATE" />
37-->[:flutter_local_notifications] F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-66
37-->[:flutter_local_notifications] F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-63
38    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
38-->[:flutter_local_notifications] F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-77
38-->[:flutter_local_notifications] F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-74
39    <uses-permission android:name="android.permission.WAKE_LOCK" />
39-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
39-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:22-65
40    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
40-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
40-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
41    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
41-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
41-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
42
43    <uses-feature
43-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
44        android:glEsVersion="0x00020000"
44-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
45        android:required="true" />
45-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
46
47    <permission
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
48        android:name="com.example.test2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.example.test2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
52
53    <application
54        android:name="android.app.Application"
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
56        android:debuggable="true"
57        android:extractNativeLibs="true"
58        android:icon="@mipmap/ic_launcher"
59        android:label="test2" >
60        <activity
61            android:name="com.example.test2.MainActivity"
62            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
63            android:exported="true"
64            android:hardwareAccelerated="true"
65            android:launchMode="singleTop"
66            android:taskAffinity=""
67            android:theme="@style/LaunchTheme"
68            android:windowSoftInputMode="adjustResize" >
69
70            <!--
71                 Specifies an Android theme to apply to this Activity as soon as
72                 the Android process has started. This theme is visible to the user
73                 while the Flutter UI initializes. After that, this theme continues
74                 to determine the Window background behind the Flutter UI.
75            -->
76            <meta-data
77                android:name="io.flutter.embedding.android.NormalTheme"
78                android:resource="@style/NormalTheme" />
79
80            <intent-filter>
81                <action android:name="android.intent.action.MAIN" />
82
83                <category android:name="android.intent.category.LAUNCHER" />
84            </intent-filter>
85        </activity>
86        <!-- Google Maps API Key (يجب إضافة المفتاح الحقيقي هنا) -->
87        <meta-data
88            android:name="com.google.android.geo.API_KEY"
89            android:value="YOUR_GOOGLE_MAPS_API_KEY_HERE" />
90
91        <!--
92             Don't delete the meta-data below.
93             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
94        -->
95        <meta-data
96            android:name="flutterEmbedding"
97            android:value="2" />
98
99        <service
99-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:56
100            android:name="com.baseflow.geolocator.GeolocatorLocationService"
100-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-77
101            android:enabled="true"
101-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-35
102            android:exported="false"
102-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
103            android:foregroundServiceType="location" />
103-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-53
104
105        <provider
105-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
106            android:name="androidx.startup.InitializationProvider"
106-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
107            android:authorities="com.example.test2.androidx-startup"
107-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
108            android:exported="false" >
108-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
109            <meta-data
109-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
110                android:name="androidx.work.WorkManagerInitializer"
110-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
111                android:value="androidx.startup" />
111-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
112            <meta-data
112-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
113                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
113-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
114                android:value="androidx.startup" />
114-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
115            <meta-data
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
116                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
117                android:value="androidx.startup" />
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
118        </provider>
119
120        <service
120-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
121            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
121-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
122            android:directBootAware="false"
122-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
123            android:enabled="@bool/enable_system_alarm_service_default"
123-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
124            android:exported="false" />
124-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
125        <service
125-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
126            android:name="androidx.work.impl.background.systemjob.SystemJobService"
126-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
127            android:directBootAware="false"
127-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
128            android:enabled="@bool/enable_system_job_service_default"
128-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
129            android:exported="true"
129-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
130            android:permission="android.permission.BIND_JOB_SERVICE" />
130-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
131        <service
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
132            android:name="androidx.work.impl.foreground.SystemForegroundService"
132-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
133            android:directBootAware="false"
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
134            android:enabled="@bool/enable_system_foreground_service_default"
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
135            android:exported="false" />
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
136
137        <receiver
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
138            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
139            android:directBootAware="false"
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
140            android:enabled="true"
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
141            android:exported="false" />
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
142        <receiver
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
143            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
144            android:directBootAware="false"
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
145            android:enabled="false"
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
146            android:exported="false" >
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
147            <intent-filter>
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
148                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
149                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
150            </intent-filter>
151        </receiver>
152        <receiver
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
153            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
154            android:directBootAware="false"
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
155            android:enabled="false"
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
156            android:exported="false" >
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
157            <intent-filter>
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
158                <action android:name="android.intent.action.BATTERY_OKAY" />
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
159                <action android:name="android.intent.action.BATTERY_LOW" />
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
160            </intent-filter>
161        </receiver>
162        <receiver
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
163            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
164            android:directBootAware="false"
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
165            android:enabled="false"
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
166            android:exported="false" >
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
167            <intent-filter>
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
168                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
169                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
170            </intent-filter>
171        </receiver>
172        <receiver
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
173            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
174            android:directBootAware="false"
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
175            android:enabled="false"
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
176            android:exported="false" >
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
177            <intent-filter>
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
178                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
179            </intent-filter>
180        </receiver>
181        <receiver
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
182            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
183            android:directBootAware="false"
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
184            android:enabled="false"
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
185            android:exported="false" >
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
186            <intent-filter>
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
187                <action android:name="android.intent.action.BOOT_COMPLETED" />
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
188                <action android:name="android.intent.action.TIME_SET" />
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
189                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
190            </intent-filter>
191        </receiver>
192        <receiver
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
193            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
194            android:directBootAware="false"
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
195            android:enabled="@bool/enable_system_alarm_service_default"
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
196            android:exported="false" >
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
197            <intent-filter>
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
198                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
199            </intent-filter>
200        </receiver>
201        <receiver
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
202            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
203            android:directBootAware="false"
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
204            android:enabled="true"
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
205            android:exported="true"
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
206            android:permission="android.permission.DUMP" >
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
207            <intent-filter>
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
208                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\883da05e951611f8406b16df7b2c9aa9\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
209            </intent-filter>
210        </receiver>
211
212        <uses-library
212-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
213            android:name="androidx.window.extensions"
213-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
214            android:required="false" />
214-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
215        <uses-library
215-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
216            android:name="androidx.window.sidecar"
216-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
217            android:required="false" /> <!-- Needs to be explicitly declared on P+ -->
217-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
218        <uses-library
218-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
219            android:name="org.apache.http.legacy"
219-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
220            android:required="false" />
220-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
221
222        <activity
222-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\07c8d82ce007794779a5c5ed16d7cd9e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
223            android:name="com.google.android.gms.common.api.GoogleApiActivity"
223-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\07c8d82ce007794779a5c5ed16d7cd9e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
224            android:exported="false"
224-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\07c8d82ce007794779a5c5ed16d7cd9e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
225            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
225-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\07c8d82ce007794779a5c5ed16d7cd9e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
226
227        <meta-data
227-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88e3d0d424506a1c1cef1c33bd4edbb3\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
228            android:name="com.google.android.gms.version"
228-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88e3d0d424506a1c1cef1c33bd4edbb3\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
229            android:value="@integer/google_play_services_version" />
229-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88e3d0d424506a1c1cef1c33bd4edbb3\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
230
231        <receiver
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
232            android:name="androidx.profileinstaller.ProfileInstallReceiver"
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
233            android:directBootAware="false"
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
234            android:enabled="true"
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
235            android:exported="true"
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
236            android:permission="android.permission.DUMP" >
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
237            <intent-filter>
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
238                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
239            </intent-filter>
240            <intent-filter>
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
241                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
242            </intent-filter>
243            <intent-filter>
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
244                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
245            </intent-filter>
246            <intent-filter>
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
247                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
248            </intent-filter>
249        </receiver>
250
251        <service
251-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1130aac3039e2a60f0933b8749e9c5d\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
252            android:name="androidx.room.MultiInstanceInvalidationService"
252-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1130aac3039e2a60f0933b8749e9c5d\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
253            android:directBootAware="true"
253-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1130aac3039e2a60f0933b8749e9c5d\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
254            android:exported="false" />
254-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1130aac3039e2a60f0933b8749e9c5d\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
255    </application>
256
257</manifest>
