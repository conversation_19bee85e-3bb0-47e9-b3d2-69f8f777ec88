1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.test2"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:41:13-72
25-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:42:13-50
27-->F:\FlutterApps\augmentsAPP\appss\test2\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29        <!-- Needs to be explicitly declared on Android R+ -->
30        <package android:name="com.google.android.apps.maps" />
30-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
30-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
31    </queries>
32
33    <uses-permission android:name="android.permission.VIBRATE" />
33-->[:flutter_local_notifications] F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-66
33-->[:flutter_local_notifications] F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-63
34    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Include required permissions for Google Maps API to run. -->
34-->[:flutter_local_notifications] F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-77
34-->[:flutter_local_notifications] F:\FlutterApps\augmentsAPP\appss\test2\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-74
35    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
35-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
35-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:22-76
36
37    <uses-feature
37-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
38        android:glEsVersion="0x00020000"
38-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
39        android:required="true" />
39-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
40
41    <permission
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
42        android:name="com.example.test2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
43        android:protectionLevel="signature" />
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
44
45    <uses-permission android:name="com.example.test2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
46
47    <application
48        android:name="android.app.Application"
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f9d4625548a64e0918456efe245c9bb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
50        android:debuggable="true"
51        android:extractNativeLibs="true"
52        android:icon="@mipmap/ic_launcher"
53        android:label="test2" >
54        <activity
55            android:name="com.example.test2.MainActivity"
56            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
57            android:exported="true"
58            android:hardwareAccelerated="true"
59            android:launchMode="singleTop"
60            android:taskAffinity=""
61            android:theme="@style/LaunchTheme"
62            android:windowSoftInputMode="adjustResize" >
63
64            <!--
65                 Specifies an Android theme to apply to this Activity as soon as
66                 the Android process has started. This theme is visible to the user
67                 while the Flutter UI initializes. After that, this theme continues
68                 to determine the Window background behind the Flutter UI.
69            -->
70            <meta-data
71                android:name="io.flutter.embedding.android.NormalTheme"
72                android:resource="@style/NormalTheme" />
73
74            <intent-filter>
75                <action android:name="android.intent.action.MAIN" />
76
77                <category android:name="android.intent.category.LAUNCHER" />
78            </intent-filter>
79        </activity>
80        <!--
81             Don't delete the meta-data below.
82             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
83        -->
84        <meta-data
85            android:name="flutterEmbedding"
86            android:value="2" />
87
88        <service
88-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:56
89            android:name="com.baseflow.geolocator.GeolocatorLocationService"
89-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-77
90            android:enabled="true"
90-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-35
91            android:exported="false"
91-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
92            android:foregroundServiceType="location" />
92-->[:geolocator_android] F:\FlutterApps\augmentsAPP\appss\test2\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-53
93
94        <uses-library
94-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
95            android:name="androidx.window.extensions"
95-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
96            android:required="false" />
96-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
97        <uses-library
97-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
98            android:name="androidx.window.sidecar"
98-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
99            android:required="false" /> <!-- Needs to be explicitly declared on P+ -->
99-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4a5afbadec1452e88719b386e4569f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
100        <uses-library
100-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
101            android:name="org.apache.http.legacy"
101-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
102            android:required="false" />
102-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\28517815d8c8a576bd2413afe5492a2c\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
103
104        <activity
104-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\07c8d82ce007794779a5c5ed16d7cd9e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
105            android:name="com.google.android.gms.common.api.GoogleApiActivity"
105-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\07c8d82ce007794779a5c5ed16d7cd9e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
106            android:exported="false"
106-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\07c8d82ce007794779a5c5ed16d7cd9e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
107            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
107-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\07c8d82ce007794779a5c5ed16d7cd9e\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
108
109        <meta-data
109-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88e3d0d424506a1c1cef1c33bd4edbb3\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
110            android:name="com.google.android.gms.version"
110-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88e3d0d424506a1c1cef1c33bd4edbb3\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
111            android:value="@integer/google_play_services_version" />
111-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88e3d0d424506a1c1cef1c33bd4edbb3\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
112
113        <provider
113-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
114            android:name="androidx.startup.InitializationProvider"
114-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
115            android:authorities="com.example.test2.androidx-startup"
115-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
116            android:exported="false" >
116-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
117            <meta-data
117-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
118                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
118-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
119                android:value="androidx.startup" />
119-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2a9e196ff1b4228c9a31a1eab1c2c9f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
120            <meta-data
120-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
121                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
122                android:value="androidx.startup" />
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
123        </provider>
124
125        <receiver
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
126            android:name="androidx.profileinstaller.ProfileInstallReceiver"
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
127            android:directBootAware="false"
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
128            android:enabled="true"
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
129            android:exported="true"
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
130            android:permission="android.permission.DUMP" >
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
131            <intent-filter>
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
132                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
133            </intent-filter>
134            <intent-filter>
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
135                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
136            </intent-filter>
137            <intent-filter>
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
138                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
139            </intent-filter>
140            <intent-filter>
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
141                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\7da8bec0279daa7eed224c7f717a9034\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
142            </intent-filter>
143        </receiver>
144    </application>
145
146</manifest>
