import 'package:flutter/material.dart';
import '../services/AuthenticationService.dart';
import '../models/Address.dart';

/// مزود حالة المصادقة الذكية
class AuthenticationProvider extends ChangeNotifier {
  final AuthenticationService _authService = AuthenticationService();

  bool _isLoading = false;
  bool _isAuthenticated = false;
  bool _needsAuthentication = false;
  String? _error;
  String? _authToken;
  DateTime? _lastLogin;

  // Getters
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;
  bool get needsAuthentication => _needsAuthentication;
  String? get error => _error;
  String? get authToken => _authToken;
  DateTime? get lastLogin => _lastLogin;

  /// تحميل حالة المصادقة عند بدء التطبيق
  Future<void> initializeAuth() async {
    _setLoading(true);
    _setError(null);

    try {
      // تحميل حالة المصادقة
      await _authService.loadAuthState();
      
      // التحقق من الحاجة للمصادقة
      _needsAuthentication = await _authService.needsAuthentication();
      _isAuthenticated = _authService.isAuthenticated;
      _authToken = _authService.authToken;
      _lastLogin = _authService.lastLogin;

      print('حالة المصادقة: مصادق=$_isAuthenticated, يحتاج مصادقة=$_needsAuthentication');
      
    } catch (e) {
      _setError('فشل في تحميل حالة المصادقة: ${e.toString()}');
      _needsAuthentication = true;
      _isAuthenticated = false;
    } finally {
      _setLoading(false);
    }
  }

  /// التحقق من كون المستخدم جديد
  Future<bool> isFirstTimeUser() async {
    try {
      return await _authService.isFirstTimeUser();
    } catch (e) {
      return true;
    }
  }

  /// تسجيل دخول أولي (مرة واحدة فقط)
  Future<bool> performInitialLogin({
    required String firstName,
    required String lastName,
    required String phone,
    required String email,
    String? city,
    String? address,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await _authService.performInitialLogin(
        firstName: firstName,
        lastName: lastName,
        phone: phone,
        email: email,
        city: city,
        address: address,
      );

      if (result.success) {
        _isAuthenticated = true;
        _needsAuthentication = false;
        _authToken = result.token;
        _lastLogin = DateTime.now();
        
        print('تم تسجيل الدخول بنجاح: ${result.message}');
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ غير متوقع: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث آخر نشاط
  Future<void> updateLastActivity() async {
    try {
      await _authService.updateLastActivity();
      _lastLogin = _authService.lastLogin;
      notifyListeners();
    } catch (e) {
      print('خطأ في تحديث آخر نشاط: $e');
    }
  }

  /// التحقق من صحة رقم الهاتف
  bool isValidPhone(String phone) {
    return phone.length == 9 && phone.startsWith('7');
  }

  /// التحقق من صحة البريد الإلكتروني
  bool isValidEmail(String email) {
    if (email.isEmpty) return true; // البريد اختياري
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email);
  }

  /// التحقق من صحة الاسم
  bool isValidName(String name) {
    return name.trim().length >= 2;
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    _setLoading(true);
    
    try {
      await _authService.logout();
      
      _isAuthenticated = false;
      _needsAuthentication = true;
      _authToken = null;
      _lastLogin = null;
      
      print('تم تسجيل الخروج بنجاح');
    } catch (e) {
      _setError('فشل في تسجيل الخروج: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// إعادة تعيين التطبيق (للاختبار والتطوير)
  Future<void> resetApp() async {
    _setLoading(true);
    
    try {
      await _authService.resetApp();
      
      _isAuthenticated = false;
      _needsAuthentication = true;
      _authToken = null;
      _lastLogin = null;
      _setError(null);
      
      print('تم إعادة تعيين التطبيق');
    } catch (e) {
      _setError('فشل في إعادة تعيين التطبيق: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// التحقق من انتهاء صلاحية الجلسة
  bool isSessionExpired() {
    if (_lastLogin == null) return true;
    
    final daysSinceLogin = DateTime.now().difference(_lastLogin!).inDays;
    return daysSinceLogin > 30;
  }

  /// الحصول على معلومات الجلسة
  Map<String, dynamic> getSessionInfo() {
    return {
      'isAuthenticated': _isAuthenticated,
      'needsAuthentication': _needsAuthentication,
      'lastLogin': _lastLogin?.toIso8601String(),
      'sessionAge': _lastLogin != null 
          ? DateTime.now().difference(_lastLogin!).inDays 
          : null,
      'isExpired': isSessionExpired(),
    };
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// تعيين رسالة الخطأ
  void _setError(String? error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _setError(null);
  }

  /// إعادة تحميل حالة المصادقة
  Future<void> refreshAuthState() async {
    await initializeAuth();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
