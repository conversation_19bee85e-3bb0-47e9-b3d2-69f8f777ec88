import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:test2/providers/CustomerDataProvider.dart';
import 'package:test2/models/Address.dart';
import 'package:test2/pages/HomePages.dart';
import 'package:test2/pages/WelcomePage.dart';
import 'package:test2/utils/ResponsiveHelper.dart';
import 'package:test2/widgets/ResponsiveText.dart';
import 'package:test2/widgets/ResponsiveButton.dart';
import 'package:test2/widgets/ResponsiveContainer.dart';
import 'package:test2/widgets/ResponsiveTextField.dart';

class RegistrationPage extends StatefulWidget {
  @override
  _RegistrationPageState createState() => _RegistrationPageState();
}

class _RegistrationPageState extends State<RegistrationPage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _cityController = TextEditingController();

  bool _isLoading = false;
  int _currentStep = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: AppBar(
        title: Text(
          'إنشاء حساب جديد',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Color(0xFF4C53A5),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => WelcomePage()),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: _skipRegistration,
            child: Text(
              'تخطي',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // مؤشر التقدم
            _buildProgressIndicator(),

            Expanded(
              child: SingleChildScrollView(
                padding: ResponsiveHelper.responsiveEdgeInsets(
                  context,
                  mobile: 16,
                  tablet: 24,
                  desktop: 32,
                ),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: ResponsiveHelper.responsiveWidth(
                      context,
                      percentage: 100,
                      maxWidth: 600,
                    ),
                  ),
                  child: Column(
                    children: [
                      // معلومات الخطوة الحالية
                      _buildStepInfo(),

                      ResponsiveSizedBox(
                        mobileHeight: 20,
                        tabletHeight: 24,
                        desktopHeight: 28,
                      ),

                      // محتوى الخطوة
                      _buildStepContent(),

                      ResponsiveSizedBox(
                        mobileHeight: 28,
                        tabletHeight: 32,
                        desktopHeight: 36,
                      ),

                      // أزرار التنقل
                      _buildNavigationButtons(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return ResponsiveContainer(
      mobilePadding: 16,
      tabletPadding: 20,
      desktopPadding: 24,
      backgroundColor: Colors.white,
      boxShadow: BoxShadow(
        color: Colors.grey.withOpacity(0.1),
        spreadRadius: 1,
        blurRadius: 4,
        offset: Offset(0, 2),
      ),
      child: Row(
        children: [
          for (int i = 0; i < 3; i++) ...[
            Expanded(
              child: Container(
                height: ResponsiveHelper.responsiveSize(
                  context,
                  mobile: 4,
                  tablet: 5,
                  desktop: 6,
                ),
                decoration: BoxDecoration(
                  color: i <= _currentStep
                      ? Color(0xFF4C53A5)
                      : Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(
                    ResponsiveHelper.responsiveBorderRadius(
                      context,
                      mobile: 2,
                      tablet: 3,
                      desktop: 4,
                    ),
                  ),
                ),
              ),
            ),
            if (i < 2)
              ResponsiveSizedBox(
                mobileWidth: 6,
                tabletWidth: 8,
                desktopWidth: 10,
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildStepInfo() {
    final stepTitles = [
      'المعلومات الأساسية',
      'معلومات الاتصال',
      'العنوان والموقع',
    ];

    final stepDescriptions = [
      'أدخل اسمك ورقم هاتفك',
      'أضف بريدك الإلكتروني (اختياري)',
      'حدد عنوانك لتسهيل التوصيل',
    ];

    return ResponsiveCard(
      mobilePadding: 16,
      tabletPadding: 20,
      desktopPadding: 24,
      child: Row(
        children: [
          ResponsiveContainer(
            mobileWidth: 36,
            tabletWidth: 40,
            desktopWidth: 44,
            mobileHeight: 36,
            tabletHeight: 40,
            desktopHeight: 44,
            backgroundColor: Color(0xFF4C53A5),
            borderRadius: BorderRadius.circular(
              ResponsiveHelper.responsiveBorderRadius(
                context,
                mobile: 18,
                tablet: 20,
                desktop: 22,
              ),
            ),
            child: Center(
              child: ResponsiveText(
                '${_currentStep + 1}',
                mobileFontSize: 16,
                tabletFontSize: 18,
                desktopFontSize: 20,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ResponsiveSizedBox(
            mobileWidth: 12,
            tabletWidth: 16,
            desktopWidth: 20,
          ),
          Expanded(
            child: ResponsiveColumn(
              spacing: ResponsiveHelper.responsiveSpacing(
                context,
                mobile: 4,
                tablet: 6,
                desktop: 8,
              ),
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveSubtitle(
                  stepTitles[_currentStep],
                  color: Color(0xFF4C53A5),
                ),
                ResponsiveBody(
                  stepDescriptions[_currentStep],
                  color: Colors.grey.shade600,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent() {
    switch (_currentStep) {
      case 0:
        return _buildBasicInfoStep();
      case 1:
        return _buildContactInfoStep();
      case 2:
        return _buildAddressStep();
      default:
        return Container();
    }
  }

  Widget _buildBasicInfoStep() {
    return ResponsiveCard(
      mobilePadding: 16,
      tabletPadding: 20,
      desktopPadding: 24,
      child: ResponsiveColumn(
        spacing: ResponsiveHelper.responsiveSpacing(
          context,
          mobile: 16,
          tablet: 20,
          desktop: 24,
        ),
        children: [
          // حقل الاسم
          ResponsiveTextField(
            controller: _nameController,
            labelText: 'الاسم الكامل *',
            hintText: 'أدخل اسمك الكامل',
            prefixIcon: Icons.person,
            textCapitalization: TextCapitalization.words,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال الاسم';
              }
              if (value.trim().length < 2) {
                return 'الاسم يجب أن يكون أكثر من حرفين';
              }
              return null;
            },
          ),

          ResponsiveSizedBox(
            mobileHeight: 16,
            tabletHeight: 20,
            desktopHeight: 24,
          ),

          // حقل رقم الهاتف
          ResponsiveTextField(
            controller: _phoneController,
            labelText: 'رقم الهاتف *',
            hintText: '77xxxxxxx',
            prefixIcon: Icons.phone,
            keyboardType: TextInputType.phone,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(9),
            ],
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال رقم الهاتف';
              }
              if (value.length != 9) {
                return 'رقم الهاتف يجب أن يكون 9 أرقام';
              }
              if (!['77', '73', '70', '71']
                  .any((prefix) => value.startsWith(prefix))) {
                return 'رقم الهاتف يجب أن يبدأ بـ 77، 73، 70، أو 71';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfoStep() {
    return ResponsiveCard(
      mobilePadding: 16,
      tabletPadding: 20,
      desktopPadding: 24,
      child: ResponsiveColumn(
        spacing: ResponsiveHelper.responsiveSpacing(
          context,
          mobile: 16,
          tablet: 20,
          desktop: 24,
        ),
        children: [
          // معلومة
          ResponsiveContainer(
            mobilePadding: 10,
            tabletPadding: 12,
            desktopPadding: 14,
            backgroundColor: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(
              ResponsiveHelper.responsiveBorderRadius(
                context,
                mobile: 6,
                tablet: 8,
                desktop: 10,
              ),
            ),
            border: Border.all(
              color: Colors.blue.shade200,
              width: ResponsiveHelper.responsiveBorderWidth(context),
            ),
            child: ResponsiveRow(
              spacing: ResponsiveHelper.responsiveSpacing(
                context,
                mobile: 6,
                tablet: 8,
                desktop: 10,
              ),
              children: [
                Icon(
                  Icons.info,
                  color: Colors.blue,
                  size: ResponsiveHelper.responsiveIconSize(
                    context,
                    mobile: 18,
                    tablet: 20,
                    desktop: 22,
                  ),
                ),
                Expanded(
                  child: ResponsiveCaption(
                    'البريد الإلكتروني اختياري ويمكن إضافته لاحقاً',
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
          ),

          ResponsiveSizedBox(
            mobileHeight: 16,
            tabletHeight: 20,
            desktopHeight: 24,
          ),

          // حقل البريد الإلكتروني
          ResponsiveTextField(
            controller: _emailController,
            labelText: 'البريد الإلكتروني (اختياري)',
            hintText: '<EMAIL>',
            prefixIcon: Icons.email,
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                    .hasMatch(value)) {
                  return 'البريد الإلكتروني غير صحيح';
                }
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAddressStep() {
    return ResponsiveCard(
      mobilePadding: 16,
      tabletPadding: 20,
      desktopPadding: 24,
      child: ResponsiveColumn(
        spacing: ResponsiveHelper.responsiveSpacing(
          context,
          mobile: 16,
          tablet: 20,
          desktop: 24,
        ),
        children: [
          // حقل المدينة
          ResponsiveTextField(
            controller: _cityController,
            labelText: 'المدينة *',
            hintText: 'صنعاء، عدن، تعز...',
            prefixIcon: Icons.location_city,
            textCapitalization: TextCapitalization.words,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال المدينة';
              }
              return null;
            },
          ),

          ResponsiveSizedBox(
            mobileHeight: 16,
            tabletHeight: 20,
            desktopHeight: 24,
          ),

          // حقل العنوان
          ResponsiveTextField(
            controller: _addressController,
            labelText: 'العنوان التفصيلي *',
            hintText: 'الحي، الشارع، رقم المنزل...',
            prefixIcon: Icons.location_on,
            maxLines: 3,
            minLines: 2,
            textCapitalization: TextCapitalization.sentences,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال العنوان';
              }
              if (value.trim().length < 10) {
                return 'يرجى إدخال عنوان أكثر تفصيلاً';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return ResponsiveRow(
      spacing: ResponsiveHelper.responsiveSpacing(
        context,
        mobile: 12,
        tablet: 16,
        desktop: 20,
      ),
      children: [
        if (_currentStep > 0)
          Expanded(
            child: ResponsiveOutlinedButton(
              text: 'السابق',
              onPressed: _isLoading ? null : _previousStep,
              borderColor: Color(0xFF4C53A5),
              textColor: Color(0xFF4C53A5),
              mobileHeight: 48,
              tabletHeight: 52,
              desktopHeight: 56,
              mobileFontSize: 14,
              tabletFontSize: 16,
              desktopFontSize: 18,
            ),
          ),
        Expanded(
          flex: _currentStep > 0 ? 1 : 2,
          child: ResponsiveButton(
            text: _currentStep < 2 ? 'التالي' : 'إنشاء الحساب',
            onPressed: _isLoading ? null : _nextStep,
            backgroundColor: Color(0xFF4C53A5),
            textColor: Colors.white,
            mobileHeight: 48,
            tabletHeight: 52,
            desktopHeight: 56,
            mobileFontSize: 14,
            tabletFontSize: 16,
            desktopFontSize: 18,
            isLoading: _isLoading,
          ),
        ),
      ],
    );
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
    }
  }

  void _nextStep() {
    if (_currentStep < 2) {
      if (_validateCurrentStep()) {
        setState(() {
          _currentStep++;
        });
      }
    } else {
      _submitRegistration();
    }
  }

  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0:
        return _nameController.text.trim().isNotEmpty &&
            _phoneController.text.length == 9 &&
            ['77', '73', '70', '71']
                .any((prefix) => _phoneController.text.startsWith(prefix));
      case 1:
        if (_emailController.text.isNotEmpty) {
          return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
              .hasMatch(_emailController.text);
        }
        return true;
      case 2:
        return _cityController.text.trim().isNotEmpty &&
            _addressController.text.trim().length >= 10;
      default:
        return false;
    }
  }

  void _submitRegistration() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final customerProvider =
          Provider.of<CustomerDataProvider>(context, listen: false);

      // تقسيم الاسم إلى اسم أول وأخير
      final nameParts = _nameController.text.trim().split(' ');
      final firstName = nameParts.first;
      final lastName =
          nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

      // إنشاء عنوان من البيانات المدخلة
      final address = Address(
        id: 'address_${DateTime.now().millisecondsSinceEpoch}',
        title: 'المنزل',
        street: _addressController.text.trim(),
        district: 'حي رئيسي',
        city: _cityController.text.trim(),
        buildingNumber: '1',
        fullAddress:
            '${_addressController.text.trim()}, ${_cityController.text.trim()}',
        isDefault: true,
      );

      // تسجيل العميل الجديد
      final success = await customerProvider.registerNewCustomer(
        firstName: firstName,
        lastName: lastName,
        email: _emailController.text.trim().isEmpty
            ? '<EMAIL>'
            : _emailController.text.trim(),
        phone: '+967${_phoneController.text.trim()}',
        addresses: [address],
      );

      if (success) {
        _showSuccessDialog();
      } else {
        _showErrorDialog(customerProvider.error ?? 'فشل في إنشاء الحساب');
      }
    } catch (e) {
      _showErrorDialog('حدث خطأ غير متوقع: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Column(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 64),
            SizedBox(height: 8),
            Text(
              'تم إنشاء الحساب بنجاح!',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.green),
            ),
          ],
        ),
        content: Text(
          'مرحباً ${_nameController.text}! تم إنشاء حسابك بنجاح ويمكنك الآن الاستمتاع بجميع ميزات التطبيق.',
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => HomePage()),
              );
            },
            child: Text('ابدأ التصفح'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('خطأ'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _skipRegistration() async {
    try {
      final customerProvider =
          Provider.of<CustomerDataProvider>(context, listen: false);

      // إنشاء مستخدم ضيف مؤقت
      final success = await customerProvider.registerNewCustomer(
        firstName: 'ضيف',
        lastName: 'مؤقت',
        email: '<EMAIL>',
        phone: '+967700000000',
      );

      if (success) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => HomePage()),
        );
      }
    } catch (e) {
      // في حالة الفشل، انتقل للصفحة الرئيسية مباشرة
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => HomePage()),
      );
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    super.dispose();
  }
}
