import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/NotificationProvider.dart';
import '../models/Notification.dart';
import '../widgets/CustomSnackBars.dart';

/// صفحة إعدادات الإشعارات
class NotificationSettingsPage extends StatefulWidget {
  @override
  _NotificationSettingsPageState createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: Consumer<NotificationProvider>(
        builder: (context, notificationProvider, child) {
          return SingleChildScrollView(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الإعدادات العامة
                _buildGeneralSettings(notificationProvider),
                
                SizedBox(height: 20),
                
                // إعدادات الأنواع
                _buildTypeSettings(notificationProvider),
                
                SizedBox(height: 20),
                
                // إعدادات الصوت والاهتزاز
                _buildSoundSettings(notificationProvider),
                
                SizedBox(height: 20),
                
                // إعدادات متقدمة
                _buildAdvancedSettings(notificationProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Color(0xFF4C53A5),
      elevation: 0,
      title: Text(
        'إعدادات الإشعارات',
        style: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildGeneralSettings(NotificationProvider notificationProvider) {
    return _buildSettingsCard(
      title: 'الإعدادات العامة',
      icon: Icons.settings,
      children: [
        _buildSwitchTile(
          title: 'تفعيل الإشعارات',
          subtitle: 'تفعيل أو إلغاء جميع الإشعارات',
          value: notificationProvider.settings.isEnabled,
          onChanged: (value) {
            notificationProvider.toggleNotifications(value);
            _showSettingUpdated();
          },
        ),
        
        _buildSwitchTile(
          title: 'شارة التطبيق',
          subtitle: 'إظهار عدد الإشعارات غير المقروءة على أيقونة التطبيق',
          value: notificationProvider.settings.badge,
          onChanged: (value) {
            final newSettings = notificationProvider.settings.copyWith(badge: value);
            notificationProvider.updateSettings(newSettings);
            _showSettingUpdated();
          },
        ),
      ],
    );
  }

  Widget _buildTypeSettings(NotificationProvider notificationProvider) {
    return _buildSettingsCard(
      title: 'أنواع الإشعارات',
      icon: Icons.category,
      children: [
        _buildSwitchTile(
          title: 'إشعارات الطلبات',
          subtitle: 'تحديثات حالة الطلبات والتوصيل',
          value: notificationProvider.settings.orderUpdates,
          onChanged: (value) {
            notificationProvider.toggleOrderNotifications(value);
            _showSettingUpdated();
          },
        ),
        
        _buildSwitchTile(
          title: 'العروض والخصومات',
          subtitle: 'إشعارات العروض الخاصة والخصومات',
          value: notificationProvider.settings.offers,
          onChanged: (value) {
            notificationProvider.toggleOfferNotifications(value);
            _showSettingUpdated();
          },
        ),
        
        _buildSwitchTile(
          title: 'المنتجات الجديدة',
          subtitle: 'إشعارات عند إضافة منتجات جديدة',
          value: notificationProvider.settings.newProducts,
          onChanged: (value) {
            final newSettings = notificationProvider.settings.copyWith(newProducts: value);
            notificationProvider.updateSettings(newSettings);
            _showSettingUpdated();
          },
        ),
        
        _buildSwitchTile(
          title: 'الإشعارات العامة',
          subtitle: 'إشعارات عامة ونصائح',
          value: notificationProvider.settings.general,
          onChanged: (value) {
            final newSettings = notificationProvider.settings.copyWith(general: value);
            notificationProvider.updateSettings(newSettings);
            _showSettingUpdated();
          },
        ),
      ],
    );
  }

  Widget _buildSoundSettings(NotificationProvider notificationProvider) {
    return _buildSettingsCard(
      title: 'الصوت والاهتزاز',
      icon: Icons.volume_up,
      children: [
        _buildSwitchTile(
          title: 'الصوت',
          subtitle: 'تشغيل صوت عند وصول الإشعارات',
          value: notificationProvider.settings.sound,
          onChanged: (value) {
            notificationProvider.toggleSound(value);
            _showSettingUpdated();
          },
        ),
        
        _buildSwitchTile(
          title: 'الاهتزاز',
          subtitle: 'اهتزاز الجهاز عند وصول الإشعارات',
          value: notificationProvider.settings.vibration,
          onChanged: (value) {
            notificationProvider.toggleVibration(value);
            _showSettingUpdated();
          },
        ),
      ],
    );
  }

  Widget _buildAdvancedSettings(NotificationProvider notificationProvider) {
    return _buildSettingsCard(
      title: 'إعدادات متقدمة',
      icon: Icons.tune,
      children: [
        _buildActionTile(
          title: 'اختبار الإشعارات',
          subtitle: 'إرسال إشعار تجريبي',
          icon: Icons.send,
          onTap: () => _sendTestNotification(notificationProvider),
        ),
        
        _buildActionTile(
          title: 'مسح جميع الإشعارات',
          subtitle: 'حذف جميع الإشعارات المحفوظة',
          icon: Icons.clear_all,
          onTap: () => _showClearAllDialog(notificationProvider),
          isDestructive: true,
        ),
        
        _buildActionTile(
          title: 'إعادة تعيين الإعدادات',
          subtitle: 'استعادة الإعدادات الافتراضية',
          icon: Icons.restore,
          onTap: () => _showResetDialog(notificationProvider),
          isDestructive: true,
        ),
      ],
    );
  }

  Widget _buildSettingsCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Color(0xFF4C53A5).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(icon, color: Color(0xFF4C53A5), size: 20),
                ),
                SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C53A5),
                  ),
                ),
              ],
            ),
          ),
          
          // المحتوى
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Color(0xFF4C53A5),
      ),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : Color(0xFF4C53A5),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: isDestructive ? Colors.red : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: Colors.grey[400],
        size: 16,
      ),
      onTap: onTap,
    );
  }

  void _sendTestNotification(NotificationProvider notificationProvider) {
    notificationProvider.addNotification(
      title: 'إشعار تجريبي',
      message: 'هذا إشعار تجريبي للتأكد من عمل النظام بشكل صحيح',
      type: NotificationType.general,
    );
    
    CustomSnackBars.showSuccess(context, message: 'تم إرسال إشعار تجريبي');
  }

  void _showClearAllDialog(NotificationProvider notificationProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('مسح جميع الإشعارات'),
        content: Text('هل أنت متأكد من مسح جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              notificationProvider.clearAllNotifications();
              Navigator.pop(context);
              CustomSnackBars.showSuccess(context, message: 'تم مسح جميع الإشعارات');
            },
            child: Text('مسح الكل'),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
          ),
        ],
      ),
    );
  }

  void _showResetDialog(NotificationProvider notificationProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إعادة تعيين الإعدادات'),
        content: Text('هل أنت متأكد من إعادة تعيين جميع إعدادات الإشعارات للقيم الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final defaultSettings = NotificationSettings();
              notificationProvider.updateSettings(defaultSettings);
              Navigator.pop(context);
              CustomSnackBars.showSuccess(context, message: 'تم إعادة تعيين الإعدادات');
            },
            child: Text('إعادة تعيين'),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
          ),
        ],
      ),
    );
  }

  void _showSettingUpdated() {
    CustomSnackBars.showSuccess(context, message: 'تم تحديث الإعدادات');
  }
}
