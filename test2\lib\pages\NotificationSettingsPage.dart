import 'package:flutter/material.dart';
import 'package:test2/services/AutoNotificationManager.dart';
import 'package:test2/services/NotificationService.dart';

class NotificationSettingsPage extends StatefulWidget {
  @override
  _NotificationSettingsPageState createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  bool _offersNotifications = true;
  bool _storesNotifications = true;
  bool _productsNotifications = true;
  bool _dailyNotifications = true;
  bool _autoNotifications = true;

  final AutoNotificationManager _autoNotificationManager = AutoNotificationManager();
  final NotificationService _notificationService = NotificationService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: AppBar(
        title: Text(
          'إعدادات الإشعارات',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Color(0xFF4C53A5),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: ListView(
        padding: EdgeInsets.all(16),
        children: [
          // بطاقة الإعدادات العامة
          _buildSettingsCard(
            title: 'الإعدادات العامة',
            icon: Icons.settings,
            children: [
              _buildSwitchTile(
                title: 'تفعيل الإشعارات التلقائية',
                subtitle: 'تلقي إشعارات دورية عن العروض والمتاجر الجديدة',
                value: _autoNotifications,
                onChanged: (value) {
                  setState(() {
                    _autoNotifications = value;
                  });
                  if (value) {
                    _autoNotificationManager.startAutoNotifications();
                  } else {
                    _autoNotificationManager.stopAutoNotifications();
                  }
                },
                icon: Icons.notifications_active,
              ),
              Divider(),
              _buildSwitchTile(
                title: 'الإشعارات اليومية',
                subtitle: 'تذكير يومي بالعروض المتاحة',
                value: _dailyNotifications,
                onChanged: (value) {
                  setState(() {
                    _dailyNotifications = value;
                  });
                  if (value) {
                    _notificationService.scheduleDailyOffersNotification();
                  }
                },
                icon: Icons.schedule,
              ),
            ],
          ),

          SizedBox(height: 16),

          // بطاقة أنواع الإشعارات
          _buildSettingsCard(
            title: 'أنواع الإشعارات',
            icon: Icons.category,
            children: [
              _buildSwitchTile(
                title: 'إشعارات العروض',
                subtitle: 'تلقي إشعارات عند توفر عروض جديدة',
                value: _offersNotifications,
                onChanged: (value) {
                  setState(() {
                    _offersNotifications = value;
                  });
                },
                icon: Icons.local_offer,
              ),
              Divider(),
              _buildSwitchTile(
                title: 'إشعارات المتاجر الجديدة',
                subtitle: 'تلقي إشعارات عند انضمام متاجر جديدة',
                value: _storesNotifications,
                onChanged: (value) {
                  setState(() {
                    _storesNotifications = value;
                  });
                },
                icon: Icons.store,
              ),
              Divider(),
              _buildSwitchTile(
                title: 'إشعارات المنتجات الجديدة',
                subtitle: 'تلقي إشعارات عند إضافة منتجات جديدة',
                value: _productsNotifications,
                onChanged: (value) {
                  setState(() {
                    _productsNotifications = value;
                  });
                },
                icon: Icons.shopping_bag,
              ),
            ],
          ),

          SizedBox(height: 16),

          // بطاقة الإجراءات
          _buildSettingsCard(
            title: 'الإجراءات',
            icon: Icons.build,
            children: [
              _buildActionTile(
                title: 'اختبار الإشعارات',
                subtitle: 'إرسال إشعار تجريبي',
                icon: Icons.notifications,
                onTap: () {
                  _sendTestNotification();
                },
              ),
              Divider(),
              _buildActionTile(
                title: 'مسح جميع الإشعارات',
                subtitle: 'إزالة جميع الإشعارات المعلقة',
                icon: Icons.clear_all,
                onTap: () {
                  _clearAllNotifications();
                },
              ),
            ],
          ),

          SizedBox(height: 24),

          // معلومات إضافية
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue),
                    SizedBox(width: 8),
                    Text(
                      'معلومات مهمة',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                Text(
                  '• يمكنك تخصيص أنواع الإشعارات التي تريد تلقيها\n'
                  '• الإشعارات التلقائية تعمل في الخلفية\n'
                  '• يمكنك إيقاف الإشعارات في أي وقت\n'
                  '• الإشعارات اليومية تصل في الساعة 10 صباحاً',
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Color(0xFF4C53A5).withOpacity(0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: Color(0xFF4C53A5)),
                SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C53A5),
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(icon, color: Color(0xFF4C53A5)),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: Colors.grey.shade600,
          fontSize: 14,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Color(0xFF4C53A5),
      ),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Color(0xFF4C53A5)),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: Colors.grey.shade600,
          fontSize: 14,
        ),
      ),
      trailing: Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  void _sendTestNotification() {
    _autoNotificationManager.sendOfferNotification(
      offerTitle: 'خصم 50%',
      storeName: 'مطعم الاختبار',
      discountPercentage: '50',
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إرسال إشعار تجريبي'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _clearAllNotifications() {
    _autoNotificationManager.cancelAllNotifications();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم مسح جميع الإشعارات'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
