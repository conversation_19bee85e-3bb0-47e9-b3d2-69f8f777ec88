class AppConfig {
  // معلومات التطبيق
  static const String appName = "زاد";
  static const String appVersion = "1.0.0";
  static const String appDescription = "طعم الأصالة في كل وجبة";

  // إعدادات التطبيق
  static const int splashDuration = 4; // بالثواني
  static const double animationDuration = 0.3; // بالثواني

  // إعدادات الشبكة
  static const String baseUrl = "https://api.zad.com";
  static const int connectionTimeout = 30; // بالثواني
  static const int receiveTimeout = 30; // بالثواني

  // إعدادات التخزين المحلي
  static const String userTokenKey = "user_token";
  static const String userDataKey = "user_data";
  static const String settingsKey = "app_settings";
  static const String cartKey = "cart_items";

  // إعدادات الموقع
  static const double defaultLatitude = 15.3694; // صنعاء
  static const double defaultLongitude = 44.1910; // صنعاء
  static const double locationRadius = 50.0; // كيلومتر

  // إعدادات الدفع
  static const List<String> supportedPaymentMethods = [
    "cash",
    "card",
    "wallet"
  ];

  // إعدادات التوصيل
  static const double deliveryFee = 5.0;
  static const double freeDeliveryMinimum = 50.0;
  static const int estimatedDeliveryTime = 30; // بالدقائق

  // إعدادات الإشعارات
  static const bool defaultNotificationsEnabled = true;
  static const bool defaultLocationEnabled = true;
  static const bool defaultDarkModeEnabled = false;

  // أرقام الاتصال
  static const String supportPhone = "+967 777 123 456";
  static const String supportEmail = "<EMAIL>";
  static const String supportWhatsApp = "+967 777 123 456";

  // روابط التواصل الاجتماعي
  static const String facebookUrl = "https://facebook.com/zad";
  static const String instagramUrl = "https://instagram.com/zad";
  static const String twitterUrl = "https://twitter.com/zad";

  // إعدادات التقييم
  static const double minRating = 1.0;
  static const double maxRating = 5.0;
  static const double defaultRating = 4.0;

  // إعدادات البحث
  static const int searchHistoryLimit = 10;
  static const int searchResultsLimit = 20;

  // إعدادات الصور
  static const int maxImageSize = 5; // ميجابايت
  static const List<String> supportedImageFormats = ["jpg", "jpeg", "png"];

  // إعدادات اللغة
  static const String defaultLanguage = "ar";
  static const List<String> supportedLanguages = ["ar", "en"];

  // إعدادات العملة
  static const String defaultCurrency = "YER";
  static const String currencySymbol = "ر.ي";

  // رسائل النظام
  static const String noInternetMessage = "لا يوجد اتصال بالإنترنت";
  static const String serverErrorMessage =
      "خطأ في الخادم، يرجى المحاولة لاحقاً";
  static const String unknownErrorMessage = "حدث خطأ غير متوقع";

  // إعدادات التخزين المؤقت
  static const int cacheExpiryDays = 7;
  static const int maxCacheSize = 100; // ميجابايت

  // إعدادات الأمان
  static const int maxLoginAttempts = 5;
  static const int lockoutDuration = 15; // بالدقائق
  static const int sessionTimeout = 24; // بالساعات

  // إعدادات التحديث
  static const bool autoUpdateEnabled = true;
  static const int updateCheckInterval = 24; // بالساعات

  // إعدادات التحليلات
  static const bool analyticsEnabled = true;
  static const bool crashReportingEnabled = true;

  // إعدادات التطوير
  static const bool debugMode = true;
  static const bool showPerformanceOverlay = false;
  static const bool enableLogging = true;
}
