import 'Product.dart';

/// نموذج عنصر السلة
class CartItem {
  final String id;
  final String productId;
  final String name;
  final String description;
  final double price;
  final String imageUrl;
  final String category;
  int quantity;
  final DateTime addedAt;

  CartItem({
    required this.id,
    required this.productId,
    required this.name,
    required this.description,
    required this.price,
    required this.imageUrl,
    required this.category,
    this.quantity = 1,
    DateTime? addedAt,
  }) : addedAt = addedAt ?? DateTime.now();

  /// إنشاء CartItem من Product
  factory CartItem.fromProduct(Product product, {int quantity = 1}) {
    return CartItem(
      id: '${product.id}_${DateTime.now().millisecondsSinceEpoch}',
      productId: product.id,
      name: product.name,
      description: product.description,
      price: product.price,
      imageUrl: product.imageUrl,
      category: product.category,
      quantity: quantity,
    );
  }

  /// حساب السعر الإجمالي للعنصر
  double get totalPrice => price * quantity;

  /// زيادة الكمية
  void increaseQuantity() {
    quantity++;
  }

  /// تقليل الكمية
  void decreaseQuantity() {
    if (quantity > 1) {
      quantity--;
    }
  }

  /// تحديث الكمية
  void updateQuantity(int newQuantity) {
    if (newQuantity > 0) {
      quantity = newQuantity;
    }
  }

  /// تحويل إلى Map للحفظ
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'name': name,
      'description': description,
      'price': price,
      'imageUrl': imageUrl,
      'category': category,
      'quantity': quantity,
      'addedAt': addedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory CartItem.fromMap(Map<String, dynamic> map) {
    return CartItem(
      id: map['id'] ?? '',
      productId: map['productId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      imageUrl: map['imageUrl'] ?? '',
      category: map['category'] ?? '',
      quantity: map['quantity'] ?? 1,
      addedAt:
          DateTime.parse(map['addedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// نسخ مع تعديل
  CartItem copyWith({
    String? id,
    String? productId,
    String? name,
    String? description,
    double? price,
    String? imageUrl,
    String? category,
    int? quantity,
    DateTime? addedAt,
  }) {
    return CartItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      quantity: quantity ?? this.quantity,
      addedAt: addedAt ?? this.addedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem && other.productId == productId;
  }

  @override
  int get hashCode => productId.hashCode;

  @override
  String toString() {
    return 'CartItem(id: $id, productId: $productId, name: $name, quantity: $quantity, price: $price)';
  }
}
