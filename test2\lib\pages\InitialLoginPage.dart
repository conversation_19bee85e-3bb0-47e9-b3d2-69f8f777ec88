import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/AuthenticationProvider.dart';
import '../widgets/CustomSnackBars.dart';
import 'HomePages.dart';

/// صفحة تسجيل الدخول الأولي (مرة واحدة فقط)
class InitialLoginPage extends StatefulWidget {
  @override
  _InitialLoginPageState createState() => _InitialLoginPageState();
}

class _InitialLoginPageState extends State<InitialLoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _cityController = TextEditingController();
  final _addressController = TextEditingController();

  bool _includeAddress = false;

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _cityController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Consumer<AuthenticationProvider>(
          builder: (context, authProvider, child) {
            return SingleChildScrollView(
              padding: EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 40),
                  
                  // العنوان والترحيب
                  _buildHeader(),
                  
                  SizedBox(height: 40),
                  
                  // نموذج البيانات
                  _buildForm(authProvider),
                  
                  SizedBox(height: 30),
                  
                  // زر تسجيل الدخول
                  _buildLoginButton(authProvider),
                  
                  SizedBox(height: 20),
                  
                  // رسالة توضيحية
                  _buildInfoMessage(),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Color(0xFF4C53A5),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Icon(
                Icons.restaurant_menu,
                color: Colors.white,
                size: 30,
              ),
            ),
            SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مرحباً بك في زاد',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C53A5),
                  ),
                ),
                Text(
                  'أفضل تطبيق لطلب الطعام',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 24),
        Text(
          'لنبدأ بإعداد حسابك',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 8),
        Text(
          'سنحتاج بعض المعلومات الأساسية لتخصيص تجربتك',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildForm(AuthenticationProvider authProvider) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الاسم الأول
          _buildTextField(
            controller: _firstNameController,
            label: 'الاسم الأول',
            hint: 'أدخل اسمك الأول',
            icon: Icons.person,
            validator: (value) {
              if (!authProvider.isValidName(value ?? '')) {
                return 'يرجى إدخال اسم صحيح (حرفين على الأقل)';
              }
              return null;
            },
          ),
          
          SizedBox(height: 16),
          
          // الاسم الأخير
          _buildTextField(
            controller: _lastNameController,
            label: 'الاسم الأخير (اختياري)',
            hint: 'أدخل اسم العائلة',
            icon: Icons.person_outline,
            required: false,
          ),
          
          SizedBox(height: 16),
          
          // رقم الهاتف
          _buildTextField(
            controller: _phoneController,
            label: 'رقم الهاتف',
            hint: '7xxxxxxxx',
            icon: Icons.phone,
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (!authProvider.isValidPhone(value ?? '')) {
                return 'رقم الهاتف يجب أن يكون 9 أرقام ويبدأ بالرقم 7';
              }
              return null;
            },
          ),
          
          SizedBox(height: 16),
          
          // البريد الإلكتروني
          _buildTextField(
            controller: _emailController,
            label: 'البريد الإلكتروني (اختياري)',
            hint: '<EMAIL>',
            icon: Icons.email,
            keyboardType: TextInputType.emailAddress,
            required: false,
            validator: (value) {
              if (value != null && value.isNotEmpty && !authProvider.isValidEmail(value)) {
                return 'يرجى إدخال بريد إلكتروني صحيح';
              }
              return null;
            },
          ),
          
          SizedBox(height: 20),
          
          // خيار إضافة العنوان
          _buildAddressOption(),
          
          if (_includeAddress) ...[
            SizedBox(height: 16),
            _buildAddressFields(),
          ],
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    bool required = true,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Color(0xFF4C53A5)),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Color(0xFF4C53A5), width: 2),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: required 
          ? (validator ?? (value) {
              if (value == null || value.trim().isEmpty) {
                return 'هذا الحقل مطلوب';
              }
              return null;
            })
          : validator,
    );
  }

  Widget _buildAddressOption() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.location_on, color: Color(0xFF4C53A5)),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إضافة عنوان التوصيل',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF4C53A5),
                  ),
                ),
                Text(
                  'يمكنك إضافة عنوانك الآن أو لاحقاً',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _includeAddress,
            onChanged: (value) {
              setState(() {
                _includeAddress = value;
              });
            },
            activeColor: Color(0xFF4C53A5),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressFields() {
    return Column(
      children: [
        _buildTextField(
          controller: _cityController,
          label: 'المدينة',
          hint: 'صنعاء، عدن، تعز...',
          icon: Icons.location_city,
        ),
        SizedBox(height: 16),
        _buildTextField(
          controller: _addressController,
          label: 'العنوان التفصيلي',
          hint: 'الحي، الشارع، رقم المنزل...',
          icon: Icons.home,
        ),
      ],
    );
  }

  Widget _buildLoginButton(AuthenticationProvider authProvider) {
    return Container(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: authProvider.isLoading ? null : () => _handleLogin(authProvider),
        style: ElevatedButton.styleFrom(
          backgroundColor: Color(0xFF4C53A5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: authProvider.isLoading
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  ),
                  SizedBox(width: 12),
                  Text(
                    'جاري الحفظ...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              )
            : Text(
                'ابدأ الاستخدام',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  Widget _buildInfoMessage() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: Colors.green[700]),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              'ستحتاج لإدخال هذه المعلومات مرة واحدة فقط. يمكنك تعديلها لاحقاً من الملف الشخصي.',
              style: TextStyle(
                fontSize: 13,
                color: Colors.green[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleLogin(AuthenticationProvider authProvider) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    authProvider.clearError();

    final success = await authProvider.performInitialLogin(
      firstName: _firstNameController.text.trim(),
      lastName: _lastNameController.text.trim(),
      phone: _phoneController.text.trim(),
      email: _emailController.text.trim(),
      city: _includeAddress ? _cityController.text.trim() : null,
      address: _includeAddress ? _addressController.text.trim() : null,
    );

    if (success) {
      CustomSnackBars.showSuccess(
        context,
        message: 'مرحباً بك!',
        subtitle: 'تم إعداد حسابك بنجاح',
      );

      // الانتقال للصفحة الرئيسية
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => HomePage()),
      );
    } else {
      CustomSnackBars.showError(
        context,
        message: 'فشل في إعداد الحساب',
        subtitle: authProvider.error ?? 'حدث خطأ غير متوقع',
      );
    }
  }
}
