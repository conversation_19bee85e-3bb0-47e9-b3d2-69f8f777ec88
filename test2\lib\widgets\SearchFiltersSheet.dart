import 'package:flutter/material.dart';
import '../services/SearchService.dart';

/// ورقة فلاتر البحث
class SearchFiltersSheet extends StatefulWidget {
  final FilterOptions currentFilters;
  final Function(FilterOptions) onApplyFilters;

  const SearchFiltersSheet({
    Key? key,
    required this.currentFilters,
    required this.onApplyFilters,
  }) : super(key: key);

  @override
  _SearchFiltersSheetState createState() => _SearchFiltersSheetState();
}

class _SearchFiltersSheetState extends State<SearchFiltersSheet> {
  late FilterOptions _filters;
  RangeValues _priceRange = RangeValues(0, 200);
  double _minRating = 0.0;
  bool _availableOnly = false;
  List<String> _selectedCategories = [];

  final List<String> _categories = [
    'برجر',
    'بيتزا',
    'شاورما',
    'مشروبات',
    'حلويات',
    'سلطات',
    'مأكولات بحرية',
    'دجاج',
    'لحوم',
    'مقبلات',
  ];

  @override
  void initState() {
    super.initState();
    _initializeFilters();
  }

  void _initializeFilters() {
    _filters = widget.currentFilters;

    // تهيئة نطاق السعر
    _priceRange = RangeValues(
      _filters.minPrice ?? 0,
      _filters.maxPrice ?? 200,
    );

    // تهيئة التقييم
    _minRating = _filters.minRating ?? 0.0;

    // تهيئة التوفر
    _availableOnly = _filters.availableOnly;

    // تهيئة الفئات
    _selectedCategories = List.from(_filters.categories);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // العنوان
          _buildHeader(),

          // المحتوى
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // فلتر السعر
                  _buildPriceFilter(),

                  SizedBox(height: 24),

                  // فلتر الفئات
                  _buildCategoriesFilter(),

                  SizedBox(height: 24),

                  // فلتر التقييم
                  _buildRatingFilter(),

                  SizedBox(height: 24),

                  // فلتر التوفر
                  _buildAvailabilityFilter(),
                ],
              ),
            ),
          ),

          // الأزرار
          _buildButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Color(0xFF4C53A5),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.filter_list, color: Colors.white, size: 24),
          SizedBox(width: 12),
          Text(
            'فلترة النتائج',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Spacer(),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.attach_money, color: Color(0xFF4C53A5), size: 20),
            SizedBox(width: 8),
            Text(
              'نطاق السعر',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4C53A5),
              ),
            ),
          ],
        ),
        SizedBox(height: 12),

        // عرض النطاق الحالي
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'من: ${_priceRange.start.toInt()} ريال',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              Text(
                'إلى: ${_priceRange.end.toInt()} ريال',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ],
          ),
        ),

        SizedBox(height: 8),

        // شريط النطاق
        RangeSlider(
          values: _priceRange,
          min: 0,
          max: 200,
          divisions: 20,
          activeColor: Color(0xFF4C53A5),
          inactiveColor: Colors.grey[300],
          labels: RangeLabels(
            '${_priceRange.start.toInt()}',
            '${_priceRange.end.toInt()}',
          ),
          onChanged: (values) {
            setState(() {
              _priceRange = values;
            });
          },
        ),
      ],
    );
  }

  Widget _buildCategoriesFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.category, color: Color(0xFF4C53A5), size: 20),
            SizedBox(width: 8),
            Text(
              'الفئات',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4C53A5),
              ),
            ),
          ],
        ),
        SizedBox(height: 12),

        // شبكة الفئات
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _categories.map((category) {
            final isSelected = _selectedCategories.contains(category);
            return FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedCategories.add(category);
                  } else {
                    _selectedCategories.remove(category);
                  }
                });
              },
              selectedColor: Color(0xFF4C53A5).withOpacity(0.2),
              checkmarkColor: Color(0xFF4C53A5),
              labelStyle: TextStyle(
                color: isSelected ? Color(0xFF4C53A5) : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildRatingFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.star, color: Color(0xFF4C53A5), size: 20),
            SizedBox(width: 8),
            Text(
              'التقييم الأدنى',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4C53A5),
              ),
            ),
          ],
        ),
        SizedBox(height: 12),

        // عرض التقييم الحالي
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              ...List.generate(5, (index) {
                return Icon(
                  index < _minRating ? Icons.star : Icons.star_border,
                  color: Colors.amber,
                  size: 20,
                );
              }),
              SizedBox(width: 8),
              Text(
                '${_minRating.toStringAsFixed(1)} فأكثر',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ],
          ),
        ),

        SizedBox(height: 8),

        // شريط التقييم
        Slider(
          value: _minRating,
          min: 0,
          max: 5,
          divisions: 10,
          activeColor: Color(0xFF4C53A5),
          inactiveColor: Colors.grey[300],
          label: _minRating.toStringAsFixed(1),
          onChanged: (value) {
            setState(() {
              _minRating = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildAvailabilityFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.check_circle, color: Color(0xFF4C53A5), size: 20),
            SizedBox(width: 8),
            Text(
              'التوفر',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4C53A5),
              ),
            ),
          ],
        ),
        SizedBox(height: 12),
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Row(
            children: [
              Switch(
                value: _availableOnly,
                onChanged: (value) {
                  setState(() {
                    _availableOnly = value;
                  });
                },
                activeColor: Color(0xFF4C53A5),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المنتجات المتوفرة فقط',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      'إظهار المنتجات المتوفرة للطلب فقط',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildButtons() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // زر إعادة التعيين
            Expanded(
              child: OutlinedButton(
                onPressed: _resetFilters,
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Color(0xFF4C53A5)),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  'إعادة تعيين',
                  style: TextStyle(
                    color: Color(0xFF4C53A5),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),

            SizedBox(width: 12),

            // زر التطبيق
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _applyFilters,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color(0xFF4C53A5),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: EdgeInsets.symmetric(vertical: 16),
                  elevation: 2,
                ),
                child: Text(
                  'تطبيق الفلاتر',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _resetFilters() {
    setState(() {
      _priceRange = RangeValues(0, 200);
      _minRating = 0.0;
      _availableOnly = false;
      _selectedCategories.clear();
    });
  }

  void _applyFilters() {
    final filters = FilterOptions(
      minPrice: _priceRange.start > 0 ? _priceRange.start : null,
      maxPrice: _priceRange.end < 200 ? _priceRange.end : null,
      categories: _selectedCategories,
      minRating: _minRating > 0 ? _minRating : null,
      availableOnly: _availableOnly,
    );

    widget.onApplyFilters(filters);
  }
}
