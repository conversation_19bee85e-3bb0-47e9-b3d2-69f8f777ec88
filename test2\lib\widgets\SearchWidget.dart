import 'package:flutter/material.dart';
import '../utils/SearchManager.dart';
import '../utils/ResponsiveHelper.dart';

class SearchWidget extends StatefulWidget {
  final String hintText;
  final Function(String) onSearchChanged;
  final Function(String)? onSearchSubmitted;
  final bool showSuggestions;
  final String suggestionType;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconPressed;

  const SearchWidget({
    Key? key,
    required this.hintText,
    required this.onSearchChanged,
    this.onSearchSubmitted,
    this.showSuggestions = false,
    this.suggestionType = 'items',
    this.prefixIcon = Icons.search,
    this.suffixIcon,
    this.onSuffixIconPressed,
  }) : super(key: key);

  @override
  _SearchWidgetState createState() => _SearchWidgetState();
}

class _SearchWidgetState extends State<SearchWidget> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<String> _suggestions = [];
  bool _showSuggestions = false;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        setState(() {
          _showSuggestions = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    widget.onSearchChanged(query);

    if (widget.showSuggestions && query.isNotEmpty) {
      setState(() {
        _suggestions =
            SearchManager.getSearchSuggestions(query, widget.suggestionType);
        _showSuggestions = _suggestions.isNotEmpty;
      });
    } else {
      setState(() {
        _showSuggestions = false;
      });
    }
  }

  void _onSuggestionTapped(String suggestion) {
    _controller.text = suggestion;
    widget.onSearchChanged(suggestion);
    setState(() {
      _showSuggestions = false;
    });
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: ResponsiveHelper.responsiveEdgeInsets(
            context,
            mobile: 12,
            tablet: 15,
            desktop: 18,
          ),
          padding: ResponsiveHelper.responsiveEdgeInsets(
            context,
            mobile: 12,
            tablet: 15,
            desktop: 18,
          ),
          height: ResponsiveHelper.responsiveSize(
            context,
            mobile: 45,
            tablet: 50,
            desktop: 55,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(
              ResponsiveHelper.responsiveBorderRadius(
                context,
                mobile: 25,
                tablet: 30,
                desktop: 35,
              ),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: ResponsiveHelper.responsiveSize(
                  context,
                  mobile: 1,
                  tablet: 1.5,
                  desktop: 2,
                ),
                blurRadius: ResponsiveHelper.responsiveSize(
                  context,
                  mobile: 2,
                  tablet: 3,
                  desktop: 4,
                ),
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              if (widget.prefixIcon != null)
                Icon(
                  widget.prefixIcon,
                  color: Colors.grey,
                  size: ResponsiveHelper.responsiveIconSize(
                    context,
                    mobile: 20,
                    tablet: 24,
                    desktop: 28,
                  ),
                ),
              SizedBox(
                width: ResponsiveHelper.responsiveSpacing(
                  context,
                  mobile: 8,
                  tablet: 10,
                  desktop: 12,
                ),
              ),
              Expanded(
                child: TextField(
                  controller: _controller,
                  focusNode: _focusNode,
                  textAlign: TextAlign.right,
                  onChanged: _onSearchChanged,
                  onSubmitted: widget.onSearchSubmitted,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.responsiveFontSize(
                      context,
                      mobile: 14,
                      tablet: 16,
                      desktop: 18,
                    ),
                  ),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: widget.hintText,
                    hintStyle: TextStyle(
                      color: Colors.grey,
                      fontSize: ResponsiveHelper.responsiveFontSize(
                        context,
                        mobile: 13,
                        tablet: 14,
                        desktop: 16,
                      ),
                    ),
                  ),
                ),
              ),
              if (widget.suffixIcon != null)
                GestureDetector(
                  onTap: widget.onSuffixIconPressed ??
                      () {
                        _controller.clear();
                        widget.onSearchChanged('');
                        setState(() {
                          _showSuggestions = false;
                        });
                      },
                  child: Icon(
                    widget.suffixIcon,
                    color: Colors.grey,
                    size: ResponsiveHelper.responsiveIconSize(
                      context,
                      mobile: 20,
                      tablet: 24,
                      desktop: 28,
                    ),
                  ),
                ),
            ],
          ),
        ),

        // اقتراحات البحث
        if (_showSuggestions && widget.showSuggestions)
          Container(
            margin: ResponsiveHelper.responsiveEdgeInsets(
              context,
              mobile: 12,
              tablet: 15,
              desktop: 18,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(
                ResponsiveHelper.responsiveBorderRadius(
                  context,
                  mobile: 8,
                  tablet: 10,
                  desktop: 12,
                ),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: ResponsiveHelper.responsiveSize(
                    context,
                    mobile: 1,
                    tablet: 1.5,
                    desktop: 2,
                  ),
                  blurRadius: ResponsiveHelper.responsiveSize(
                    context,
                    mobile: 2,
                    tablet: 3,
                    desktop: 4,
                  ),
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Column(
              children: _suggestions.map((suggestion) {
                return ListTile(
                  dense: true,
                  leading: Icon(
                    Icons.search,
                    color: Colors.grey,
                    size: ResponsiveHelper.responsiveIconSize(
                      context,
                      mobile: 18,
                      tablet: 20,
                      desktop: 22,
                    ),
                  ),
                  title: Text(
                    suggestion,
                    style: TextStyle(
                      fontSize: ResponsiveHelper.responsiveFontSize(
                        context,
                        mobile: 13,
                        tablet: 14,
                        desktop: 16,
                      ),
                    ),
                    textAlign: TextAlign.right,
                  ),
                  onTap: () => _onSuggestionTapped(suggestion),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }
}

// مكون بحث مبسط للاستخدام السريع
class SimpleSearchBar extends StatelessWidget {
  final String hintText;
  final Function(String) onSearchChanged;
  final IconData? icon;

  const SimpleSearchBar({
    Key? key,
    required this.hintText,
    required this.onSearchChanged,
    this.icon = Icons.search,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: ResponsiveHelper.responsiveEdgeInsetsCustom(
        context,
        horizontal: 12,
        vertical: 8,
      ),
      padding: ResponsiveHelper.responsiveEdgeInsets(
        context,
        mobile: 12,
        tablet: 15,
        desktop: 18,
      ),
      height: ResponsiveHelper.responsiveSize(
        context,
        mobile: 40,
        tablet: 45,
        desktop: 50,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.responsiveBorderRadius(
            context,
            mobile: 20,
            tablet: 25,
            desktop: 30,
          ),
        ),
        border: Border.all(
          color: Colors.grey.shade300,
          width: ResponsiveHelper.responsiveBorderWidth(context),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: Colors.grey,
            size: ResponsiveHelper.responsiveIconSize(
              context,
              mobile: 18,
              tablet: 20,
              desktop: 22,
            ),
          ),
          SizedBox(
            width: ResponsiveHelper.responsiveSpacing(
              context,
              mobile: 8,
              tablet: 10,
              desktop: 12,
            ),
          ),
          Expanded(
            child: TextField(
              textAlign: TextAlign.right,
              onChanged: onSearchChanged,
              style: TextStyle(
                fontSize: ResponsiveHelper.responsiveFontSize(
                  context,
                  mobile: 13,
                  tablet: 15,
                  desktop: 17,
                ),
              ),
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: hintText,
                hintStyle: TextStyle(
                  color: Colors.grey,
                  fontSize: ResponsiveHelper.responsiveFontSize(
                    context,
                    mobile: 12,
                    tablet: 13,
                    desktop: 15,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// مكون بحث متقدم مع فلاتر
class AdvancedSearchWidget extends StatefulWidget {
  final Function(String, Map<String, dynamic>) onSearchChanged;
  final List<String> categories;
  final String hintText;

  const AdvancedSearchWidget({
    Key? key,
    required this.onSearchChanged,
    required this.categories,
    this.hintText = "البحث...",
  }) : super(key: key);

  @override
  _AdvancedSearchWidgetState createState() => _AdvancedSearchWidgetState();
}

class _AdvancedSearchWidgetState extends State<AdvancedSearchWidget> {
  final TextEditingController _controller = TextEditingController();
  String _selectedCategory = "الكل";
  String _sortBy = "الاسم";

  void _onSearchChanged() {
    widget.onSearchChanged(_controller.text, {
      'category': _selectedCategory,
      'sortBy': _sortBy,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // شريط البحث
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 15),
          padding: const EdgeInsets.symmetric(horizontal: 15),
          height: 50,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              const Icon(Icons.search, color: Colors.grey),
              const SizedBox(width: 10),
              Expanded(
                child: TextField(
                  controller: _controller,
                  textAlign: TextAlign.right,
                  onChanged: (value) => _onSearchChanged(),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: widget.hintText,
                    hintStyle: const TextStyle(color: Colors.grey),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 10),

        // فلاتر البحث
        Row(
          children: [
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 15),
                padding: const EdgeInsets.symmetric(horizontal: 10),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _selectedCategory,
                    isExpanded: true,
                    items: widget.categories.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(
                          category,
                          style: const TextStyle(fontSize: 12),
                          textAlign: TextAlign.right,
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value!;
                      });
                      _onSearchChanged();
                    },
                  ),
                ),
              ),
            ),
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 15),
                padding: const EdgeInsets.symmetric(horizontal: 10),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _sortBy,
                    isExpanded: true,
                    items: const [
                      DropdownMenuItem(
                          value: "الاسم",
                          child: Text("الاسم", style: TextStyle(fontSize: 12))),
                      DropdownMenuItem(
                          value: "السعر",
                          child: Text("السعر", style: TextStyle(fontSize: 12))),
                      DropdownMenuItem(
                          value: "التقييم",
                          child:
                              Text("التقييم", style: TextStyle(fontSize: 12))),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _sortBy = value!;
                      });
                      _onSearchChanged();
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
