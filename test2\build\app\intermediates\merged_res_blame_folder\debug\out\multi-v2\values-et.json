{"logs": [{"outputFile": "com.example.test2.app-mergeDebugResources-44:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\88e3d0d424506a1c1cef1c33bd4edbb3\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-et\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4554", "endColumns": "139", "endOffsets": "4689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4f9d4625548a64e0918456efe245c9bb\\transformed\\core-1.13.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "29,30,31,32,33,34,35,59", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2827,2922,3024,3122,3225,3331,3436,6232", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "2917,3019,3117,3220,3326,3431,3551,6328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\47ac933c65727cbf2c22795a6a720497\\transformed\\preference-1.2.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,180,267,343,481,650,733", "endColumns": "74,86,75,137,168,82,77", "endOffsets": "175,262,338,476,645,728,806"}, "to": {"startLines": "54,55,56,57,60,61,62", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5774,5849,5936,6012,6333,6502,6585", "endColumns": "74,86,75,137,168,82,77", "endOffsets": "5844,5931,6007,6145,6497,6580,6658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\07c8d82ce007794779a5c5ed16d7cd9e\\transformed\\jetified-play-services-base-18.3.0\\res\\values-et\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,450,573,677,823,948,1060,1159,1315,1419,1579,1707,1858,1999,2058,2119", "endColumns": "98,157,122,103,145,124,111,98,155,103,159,127,150,140,58,60,83", "endOffsets": "291,449,572,676,822,947,1059,1158,1314,1418,1578,1706,1857,1998,2057,2118,2202"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3556,3659,3821,3948,4056,4206,4335,4451,4694,4854,4962,5126,5258,5413,5558,5621,5686", "endColumns": "102,161,126,107,149,128,115,102,159,107,163,131,154,144,62,64,87", "endOffsets": "3654,3816,3943,4051,4201,4330,4446,4549,4849,4957,5121,5253,5408,5553,5616,5681,5769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2fb1c87eb9bd4c095f30b9ec565cd4a6\\transformed\\appcompat-1.1.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,885,977,1070,1166,1268,1378,1472,1573,1667,1759,1852,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,77,91,92,95,101,109,93,100,93,91,92,81,110,103,98,109,101,98,165,101,81", "endOffsets": "206,305,416,502,604,721,802,880,972,1065,1161,1263,1373,1467,1568,1662,1754,1847,1929,2040,2144,2243,2353,2455,2554,2720,2822,2904"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,885,977,1070,1166,1268,1378,1472,1573,1667,1759,1852,1934,2045,2149,2248,2358,2460,2559,2725,6150", "endColumns": "105,98,110,85,101,116,80,77,91,92,95,101,109,93,100,93,91,92,81,110,103,98,109,101,98,165,101,81", "endOffsets": "206,305,416,502,604,721,802,880,972,1065,1161,1263,1373,1467,1568,1662,1754,1847,1929,2040,2144,2243,2353,2455,2554,2720,2822,6227"}}]}]}