import 'package:flutter/material.dart';
import 'package:test2/utils/ResponsiveHelper.dart';

class ResponsiveText extends StatelessWidget {
  final String text;
  final double mobileFontSize;
  final double? tabletFontSize;
  final double? desktopFontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextStyle? style;

  const ResponsiveText(
    this.text, {
    Key? key,
    required this.mobileFontSize,
    this.tabletFontSize,
    this.desktopFontSize,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.style,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final fontSize = ResponsiveHelper.responsiveFontSize(
      context,
      mobile: mobileFontSize,
      tablet: tabletFontSize,
      desktop: desktopFontSize,
    );

    return Text(
      text,
      style: (style ?? const TextStyle()).copyWith(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.ellipsis,
    );
  }
}

class ResponsiveRichText extends StatelessWidget {
  final List<TextSpan> textSpans;
  final double mobileFontSize;
  final double? tabletFontSize;
  final double? desktopFontSize;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveRichText({
    Key? key,
    required this.textSpans,
    required this.mobileFontSize,
    this.tabletFontSize,
    this.desktopFontSize,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final fontSize = ResponsiveHelper.responsiveFontSize(
      context,
      mobile: mobileFontSize,
      tablet: tabletFontSize,
      desktop: desktopFontSize,
    );

    return RichText(
      text: TextSpan(
        style: TextStyle(fontSize: fontSize),
        children: textSpans,
      ),
      textAlign: textAlign ?? TextAlign.start,
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.ellipsis,
    );
  }
}

// Widget للعناوين
class ResponsiveTitle extends StatelessWidget {
  final String title;
  final Color? color;
  final FontWeight? fontWeight;
  final TextAlign? textAlign;

  const ResponsiveTitle(
    this.title, {
    Key? key,
    this.color,
    this.fontWeight,
    this.textAlign,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ResponsiveText(
      title,
      mobileFontSize: 20,
      tabletFontSize: 24,
      desktopFontSize: 28,
      fontWeight: fontWeight ?? FontWeight.bold,
      color: color,
      textAlign: textAlign,
    );
  }
}

// Widget للعناوين الفرعية
class ResponsiveSubtitle extends StatelessWidget {
  final String subtitle;
  final Color? color;
  final FontWeight? fontWeight;
  final TextAlign? textAlign;

  const ResponsiveSubtitle(
    this.subtitle, {
    Key? key,
    this.color,
    this.fontWeight,
    this.textAlign,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ResponsiveText(
      subtitle,
      mobileFontSize: 16,
      tabletFontSize: 18,
      desktopFontSize: 20,
      fontWeight: fontWeight ?? FontWeight.w600,
      color: color,
      textAlign: textAlign,
    );
  }
}

// Widget للنصوص العادية
class ResponsiveBody extends StatelessWidget {
  final String text;
  final Color? color;
  final FontWeight? fontWeight;
  final TextAlign? textAlign;
  final int? maxLines;

  const ResponsiveBody(
    this.text, {
    Key? key,
    this.color,
    this.fontWeight,
    this.textAlign,
    this.maxLines,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ResponsiveText(
      text,
      mobileFontSize: 14,
      tabletFontSize: 16,
      desktopFontSize: 18,
      fontWeight: fontWeight,
      color: color,
      textAlign: textAlign,
      maxLines: maxLines,
    );
  }
}

// Widget للنصوص الصغيرة
class ResponsiveCaption extends StatelessWidget {
  final String text;
  final Color? color;
  final FontWeight? fontWeight;
  final TextAlign? textAlign;

  const ResponsiveCaption(
    this.text, {
    Key? key,
    this.color,
    this.fontWeight,
    this.textAlign,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ResponsiveText(
      text,
      mobileFontSize: 12,
      tabletFontSize: 14,
      desktopFontSize: 16,
      fontWeight: fontWeight,
      color: color,
      textAlign: textAlign,
    );
  }
}
