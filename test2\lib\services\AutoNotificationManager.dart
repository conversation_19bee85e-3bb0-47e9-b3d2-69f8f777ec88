import 'dart:async';
import 'dart:math';
import 'package:test2/services/NotificationService.dart';

class AutoNotificationManager {
  static final AutoNotificationManager _instance =
      AutoNotificationManager._internal();
  factory AutoNotificationManager() => _instance;
  AutoNotificationManager._internal();

  Timer? _notificationTimer;
  final NotificationService _notificationService = NotificationService();
  final Random _random = Random();

  // قائمة العروض الجديدة المحتملة
  final List<Map<String, dynamic>> _potentialOffers = [
    {
      'title': 'خصم 30%',
      'subtitle': 'على جميع البرجر',
      'storeName': 'مطعم الشرق',
      'discountPercentage': '30',
    },
    {
      'title': 'خصم 25%',
      'subtitle': 'على المشتريات فوق 100 ريال',
      'storeName': 'سوبرماركت الأمانة',
      'discountPercentage': '25',
    },
    {
      'title': 'خصم 40%',
      'subtitle': 'على جميع الأدوية',
      'storeName': 'صيدلية الصحة',
      'discountPercentage': '40',
    },
    {
      'title': 'خصم 35%',
      'subtitle': 'على الملابس الشتوية',
      'storeName': 'متجر الأناقة',
      'discountPercentage': '35',
    },
    {
      'title': 'خصم 20%',
      'subtitle': 'على الأجهزة الإلكترونية',
      'storeName': 'متجر التقنية',
      'discountPercentage': '20',
    },
  ];

  // قائمة المتاجر الجديدة المحتملة
  final List<Map<String, dynamic>> _potentialStores = [
    {
      'name': 'مطعم البحر الأبيض',
      'category': 'مطاعم',
    },
    {
      'name': 'صيدلية النهضة',
      'category': 'صيدليات',
    },
    {
      'name': 'متجر الإلكترونيات الحديثة',
      'category': 'إلكترونيات',
    },
    {
      'name': 'مخبز الأصالة',
      'category': 'مخابز',
    },
    {
      'name': 'حلويات الشام',
      'category': 'حلويات',
    },
  ];

  // قائمة المنتجات الجديدة المحتملة
  final List<Map<String, dynamic>> _potentialProducts = [
    {
      'name': 'برجر دجاج مشوي',
      'storeName': 'مطعم الشرق',
      'price': '28',
    },
    {
      'name': 'عسل طبيعي يمني',
      'storeName': 'سوبرماركت الأمانة',
      'price': '85',
    },
    {
      'name': 'فيتامين د3',
      'storeName': 'صيدلية الصحة',
      'price': '45',
    },
    {
      'name': 'قميص قطني فاخر',
      'storeName': 'متجر الأناقة',
      'price': '120',
    },
    {
      'name': 'سماعات بلوتوث',
      'storeName': 'متجر التقنية',
      'price': '180',
    },
  ];

  // بدء الإشعارات التلقائية
  void startAutoNotifications() {
    // إيقاف أي مؤقت سابق
    stopAutoNotifications();

    // بدء مؤقت جديد - إشعار كل 30 ثانية للاختبار (يمكن تغييره لساعات في الإنتاج)
    _notificationTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      _sendRandomNotification();
    });

    // إرسال إشعار ترحيبي فوري
    _sendWelcomeNotification();
  }

  // إيقاف الإشعارات التلقائية
  void stopAutoNotifications() {
    _notificationTimer?.cancel();
    _notificationTimer = null;
  }

  // إرسال إشعار عشوائي
  void _sendRandomNotification() {
    final notificationType = _random.nextInt(3); // 0: عرض، 1: متجر، 2: منتج

    switch (notificationType) {
      case 0:
        _sendRandomOfferNotification();
        break;
      case 1:
        _sendRandomStoreNotification();
        break;
      case 2:
        _sendRandomProductNotification();
        break;
    }
  }

  // إرسال إشعار عرض عشوائي
  void _sendRandomOfferNotification() {
    final offer = _potentialOffers[_random.nextInt(_potentialOffers.length)];

    _notificationService.showNewOfferNotification(
      offerTitle: offer['title'],
      storeName: offer['storeName'],
      discountPercentage: offer['discountPercentage'],
    );
  }

  // إرسال إشعار متجر عشوائي
  void _sendRandomStoreNotification() {
    final store = _potentialStores[_random.nextInt(_potentialStores.length)];

    _notificationService.showNewStoreNotification(
      storeName: store['name'],
      storeCategory: store['category'],
    );
  }

  // إرسال إشعار منتج عشوائي
  void _sendRandomProductNotification() {
    final product =
        _potentialProducts[_random.nextInt(_potentialProducts.length)];

    _notificationService.showNewProductNotification(
      productName: product['name'],
      storeName: product['storeName'],
      price: product['price'],
    );
  }

  // إرسال إشعار ترحيبي
  void _sendWelcomeNotification() {
    Future.delayed(Duration(seconds: 2), () {
      _notificationService.showWelcomeNotification();
    });
  }

  // إرسال إشعار عرض محدد
  void sendOfferNotification({
    required String offerTitle,
    required String storeName,
    required String discountPercentage,
  }) {
    _notificationService.showNewOfferNotification(
      offerTitle: offerTitle,
      storeName: storeName,
      discountPercentage: discountPercentage,
    );
  }

  // إرسال إشعار متجر محدد
  void sendStoreNotification({
    required String storeName,
    required String storeCategory,
  }) {
    _notificationService.showNewStoreNotification(
      storeName: storeName,
      storeCategory: storeCategory,
    );
  }

  // إرسال إشعار منتج محدد
  void sendProductNotification({
    required String productName,
    required String storeName,
    required String price,
  }) {
    _notificationService.showNewProductNotification(
      productName: productName,
      storeName: storeName,
      price: price,
    );
  }

  // جدولة إشعارات يومية
  void scheduleDailyNotifications() {
    _notificationService.scheduleDailyOffersNotification();
  }

  // إلغاء جميع الإشعارات
  void cancelAllNotifications() {
    _notificationService.cancelAllNotifications();
  }

  // تنظيف الموارد
  void dispose() {
    stopAutoNotifications();
  }
}
