import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/AddressProvider.dart';
import '../models/Address.dart';
import '../pages/AddEditAddressPage.dart';
import '../widgets/CustomSnackBars.dart';

class AddressesPage extends StatefulWidget {
  @override
  _AddressesPageState createState() => _AddressesPageState();
}

class _AddressesPageState extends State<AddressesPage> {
  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  void _loadAddresses() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AddressProvider>(context, listen: false).loadAddresses();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color.fromARGB(255, 219, 55, 80),
      appBar: AppBar(
        backgroundColor: Color.fromARGB(255, 219, 55, 80),
        elevation: 0,
        centerTitle: true,
        title: Text(
          "عناوين التوصيل",
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Consumer<AddressProvider>(
        builder: (context, addressProvider, child) {
          if (addressProvider.isLoading) {
            return Center(
              child: CircularProgressIndicator(
                color: Color.fromARGB(255, 219, 55, 80),
              ),
            );
          }

          return Column(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Color(0xFFEDECF2),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(25),
                      topRight: Radius.circular(25),
                    ),
                  ),
                  child: addressProvider.addresses.isEmpty
                      ? _buildEmptyState()
                      : _buildAddressesList(addressProvider.addresses),
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddAddressDialog,
        backgroundColor: Color.fromARGB(255, 219, 55, 80),
        child: Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 80,
            color: Colors.grey,
          ),
          SizedBox(height: 20),
          Text(
            "لا توجد عناوين محفوظة",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 10),
          Text(
            "أضف عنوان جديد لتسهيل عملية التوصيل",
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 30),
          ElevatedButton.icon(
            onPressed: _showAddAddressDialog,
            icon: Icon(Icons.add),
            label: Text("إضافة عنوان جديد"),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color.fromARGB(255, 219, 55, 80),
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressesList(List<Address> addresses) {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: addresses.length,
      itemBuilder: (context, index) {
        return _buildAddressCard(addresses[index]);
      },
    );
  }

  Widget _buildAddressCard(Address address) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: address.isDefault
            ? Border.all(color: Color.fromARGB(255, 219, 55, 80), width: 2)
            : null,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getAddressIcon(address.title),
                  color: Color.fromARGB(255, 219, 55, 80),
                  size: 24,
                ),
                SizedBox(width: 10),
                Expanded(
                  child: Text(
                    address.title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 219, 55, 80),
                    ),
                  ),
                ),
                if (address.isDefault)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Color.fromARGB(255, 219, 55, 80),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      "افتراضي",
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleAddressAction(value, address),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: "edit",
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 20),
                          SizedBox(width: 8),
                          Text("تعديل"),
                        ],
                      ),
                    ),
                    if (!address.isDefault)
                      PopupMenuItem(
                        value: "default",
                        child: Row(
                          children: [
                            Icon(Icons.star, size: 20),
                            SizedBox(width: 8),
                            Text("جعل افتراضي"),
                          ],
                        ),
                      ),
                    PopupMenuItem(
                      value: "delete",
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 20, color: Colors.red),
                          SizedBox(width: 8),
                          Text("حذف", style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 10),
            Text(
              address.formattedAddress,
              style: TextStyle(fontSize: 14, color: Colors.grey[700]),
            ),
            if (address.landmark != null && address.landmark!.isNotEmpty) ...[
              SizedBox(height: 5),
              Text(
                'علامة مميزة: ${address.landmark}',
                style: TextStyle(fontSize: 12, color: Colors.grey[500]),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getAddressIcon(String title) {
    switch (title) {
      case 'المنزل':
        return Icons.home;
      case 'العمل':
        return Icons.work;
      case 'الأصدقاء':
        return Icons.people;
      case 'العائلة':
        return Icons.family_restroom;
      default:
        return Icons.location_on;
    }
  }

  void _handleAddressAction(String action, Address address) {
    switch (action) {
      case 'edit':
        _showEditAddressDialog(address);
        break;
      case 'default':
        _setDefaultAddress(address.id);
        break;
      case 'delete':
        _showDeleteConfirmation(address);
        break;
    }
  }

  void _setDefaultAddress(String addressId) async {
    final addressProvider =
        Provider.of<AddressProvider>(context, listen: false);
    final success = await addressProvider.setDefaultAddress(addressId);

    if (success) {
      CustomSnackBars.showSuccess(
        context,
        message: 'تم تعيين العنوان كافتراضي',
        subtitle: 'سيتم استخدام هذا العنوان في الطلبات',
      );
    } else {
      CustomSnackBars.showError(
        context,
        message: 'فشل في تعيين العنوان',
        subtitle: 'يرجى المحاولة مرة أخرى',
      );
    }
  }

  void _showDeleteConfirmation(Address address) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف العنوان'),
        content: Text('هل أنت متأكد من رغبتك في حذف هذا العنوان؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteAddress(address.id);
            },
            child: Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _deleteAddress(String addressId) async {
    final addressProvider =
        Provider.of<AddressProvider>(context, listen: false);
    final success = await addressProvider.deleteAddress(addressId);

    if (success) {
      CustomSnackBars.showSuccess(
        context,
        message: 'تم حذف العنوان بنجاح',
        subtitle: 'العنوان لم يعد متاحاً',
      );
    } else {
      CustomSnackBars.showError(
        context,
        message: 'فشل في حذف العنوان',
        subtitle: 'يرجى المحاولة مرة أخرى',
      );
    }
  }

  void _showAddAddressDialog() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditAddressPage(),
      ),
    );

    if (result == true) {
      _loadAddresses();
    }
  }

  void _showEditAddressDialog(Address address) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditAddressPage(address: address),
      ),
    );

    if (result == true) {
      _loadAddresses();
    }
  }
}
