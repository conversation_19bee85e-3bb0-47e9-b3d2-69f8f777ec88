import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:test2/utils/AppColors.dart';

class AddressesPage extends StatefulWidget {
  @override
  _AddressesPageState createState() => _AddressesPageState();
}

class _AddressesPageState extends State<AddressesPage> {
  List<Map<String, dynamic>> addresses = [
    {
      "id": "1",
      "title": "المنزل",
      "address": "شارع الزبيري، حي السبعين، صنعاء",
      "details": "بجانب مسجد النور، الطابق الثاني",
      "phone": "+967 777 123 456",
      "isDefault": true,
      "type": "home",
    },
    {
      "id": "2",
      "title": "العمل",
      "address": "شارع الستين، حي التحرير، صنعاء",
      "details": "مبنى الأعمال، الطابق الخامس، مكتب 502",
      "phone": "+967 777 654 321",
      "isDefault": false,
      "type": "work",
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.backgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppDimensions.borderRadiusXLarge),
                  topRight: Radius.circular(AppDimensions.borderRadiusXLarge),
                ),
              ),
              child: addresses.isEmpty ? _buildEmptyState() : _buildAddressesList(),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddAddressDialog,
        backgroundColor: AppColors.primaryColor,
        child: Icon(Icons.add, color: AppColors.whiteColor),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryColor,
      elevation: 0,
      centerTitle: true,
      title: Text(
        "عناوين التوصيل",
        style: AppTextStyles.appBarTitle,
      ),
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.whiteColor,
        ),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 80.sp,
            color: AppColors.textSecondaryColor,
          ),
          SizedBox(height: 20.h),
          Text(
            "لا توجد عناوين محفوظة",
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.textSecondaryColor,
            ),
          ),
          SizedBox(height: 10.h),
          Text(
            "أضف عنوان جديد لتسهيل عملية التوصيل",
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 30.h),
          ElevatedButton.icon(
            onPressed: _showAddAddressDialog,
            icon: Icon(Icons.add),
            label: Text("إضافة عنوان جديد"),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingLarge,
                vertical: AppDimensions.paddingMedium,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressesList() {
    return ListView.builder(
      padding: EdgeInsets.all(AppDimensions.paddingMedium),
      itemCount: addresses.length,
      itemBuilder: (context, index) {
        return _buildAddressCard(addresses[index]);
      },
    );
  }

  Widget _buildAddressCard(Map<String, dynamic> address) {
    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.marginMedium),
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        border: address["isDefault"] 
            ? Border.all(color: AppColors.primaryColor, width: 2)
            : null,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 8,
            spreadRadius: 1,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          textDirection: TextDirection.rtl,
          children: [
            // العنوان والنوع
            Row(
              textDirection: TextDirection.rtl,
              children: [
                Icon(
                  _getAddressIcon(address["type"]),
                  color: AppColors.primaryColor,
                  size: 24.sp,
                ),
                SizedBox(width: 10.w),
                Expanded(
                  child: Text(
                    address["title"],
                    style: AppTextStyles.titleSmall,
                  ),
                ),
                if (address["isDefault"])
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      "افتراضي",
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: AppColors.whiteColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleAddressAction(value, address),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: "edit",
                      child: Row(
                        textDirection: TextDirection.rtl,
                        children: [
                          Icon(Icons.edit, size: 20.sp),
                          SizedBox(width: 8.w),
                          Text("تعديل"),
                        ],
                      ),
                    ),
                    if (!address["isDefault"])
                      PopupMenuItem(
                        value: "default",
                        child: Row(
                          textDirection: TextDirection.rtl,
                          children: [
                            Icon(Icons.star, size: 20.sp),
                            SizedBox(width: 8.w),
                            Text("جعل افتراضي"),
                          ],
                        ),
                      ),
                    PopupMenuItem(
                      value: "delete",
                      child: Row(
                        textDirection: TextDirection.rtl,
                        children: [
                          Icon(Icons.delete, size: 20.sp, color: AppColors.errorColor),
                          SizedBox(width: 8.w),
                          Text("حذف", style: TextStyle(color: AppColors.errorColor)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            SizedBox(height: 10.h),
            
            // العنوان التفصيلي
            Text(
              address["address"],
              style: AppTextStyles.bodyMedium,
            ),
            
            if (address["details"] != null && address["details"].isNotEmpty) ...[
              SizedBox(height: 5.h),
              Text(
                address["details"],
                style: AppTextStyles.bodySmall,
              ),
            ],
            
            SizedBox(height: 10.h),
            
            // رقم الهاتف
            Row(
              textDirection: TextDirection.rtl,
              children: [
                Icon(
                  Icons.phone,
                  color: AppColors.textSecondaryColor,
                  size: 16.sp,
                ),
                SizedBox(width: 5.w),
                Text(
                  address["phone"],
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getAddressIcon(String type) {
    switch (type) {
      case "home":
        return Icons.home;
      case "work":
        return Icons.work;
      default:
        return Icons.location_on;
    }
  }

  void _handleAddressAction(String action, Map<String, dynamic> address) {
    switch (action) {
      case "edit":
        _showEditAddressDialog(address);
        break;
      case "default":
        _setDefaultAddress(address["id"]);
        break;
      case "delete":
        _showDeleteConfirmation(address);
        break;
    }
  }

  void _setDefaultAddress(String addressId) {
    setState(() {
      for (var address in addresses) {
        address["isDefault"] = address["id"] == addressId;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text("تم تعيين العنوان كافتراضي"),
        backgroundColor: AppColors.successColor,
      ),
    );
  }

  void _showDeleteConfirmation(Map<String, dynamic> address) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          "حذف العنوان",
          textAlign: TextAlign.right,
          style: AppTextStyles.titleMedium,
        ),
        content: Text(
          "هل أنت متأكد من رغبتك في حذف هذا العنوان؟",
          textAlign: TextAlign.right,
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text("إلغاء"),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteAddress(address["id"]);
            },
            child: Text(
              "حذف",
              style: TextStyle(color: AppColors.errorColor),
            ),
          ),
        ],
      ),
    );
  }

  void _deleteAddress(String addressId) {
    setState(() {
      addresses.removeWhere((address) => address["id"] == addressId);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text("تم حذف العنوان"),
        backgroundColor: AppColors.successColor,
      ),
    );
  }

  void _showAddAddressDialog() {
    _showAddressDialog();
  }

  void _showEditAddressDialog(Map<String, dynamic> address) {
    _showAddressDialog(address: address);
  }

  void _showAddressDialog({Map<String, dynamic>? address}) {
    final isEditing = address != null;
    final titleController = TextEditingController(text: address?["title"] ?? "");
    final addressController = TextEditingController(text: address?["address"] ?? "");
    final detailsController = TextEditingController(text: address?["details"] ?? "");
    final phoneController = TextEditingController(text: address?["phone"] ?? "");
    String selectedType = address?["type"] ?? "home";

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(
            isEditing ? "تعديل العنوان" : "إضافة عنوان جديد",
            textAlign: TextAlign.right,
            style: AppTextStyles.titleMedium,
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // نوع العنوان
                DropdownButtonFormField<String>(
                  value: selectedType,
                  decoration: InputDecoration(
                    labelText: "نوع العنوان",
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    DropdownMenuItem(value: "home", child: Text("المنزل")),
                    DropdownMenuItem(value: "work", child: Text("العمل")),
                    DropdownMenuItem(value: "other", child: Text("أخرى")),
                  ],
                  onChanged: (value) {
                    setDialogState(() {
                      selectedType = value!;
                    });
                  },
                ),
                
                SizedBox(height: 15.h),
                
                // عنوان العنوان
                TextField(
                  controller: titleController,
                  textAlign: TextAlign.right,
                  decoration: InputDecoration(
                    labelText: "اسم العنوان",
                    border: OutlineInputBorder(),
                  ),
                ),
                
                SizedBox(height: 15.h),
                
                // العنوان التفصيلي
                TextField(
                  controller: addressController,
                  textAlign: TextAlign.right,
                  maxLines: 2,
                  decoration: InputDecoration(
                    labelText: "العنوان التفصيلي",
                    border: OutlineInputBorder(),
                  ),
                ),
                
                SizedBox(height: 15.h),
                
                // تفاصيل إضافية
                TextField(
                  controller: detailsController,
                  textAlign: TextAlign.right,
                  maxLines: 2,
                  decoration: InputDecoration(
                    labelText: "تفاصيل إضافية (اختياري)",
                    border: OutlineInputBorder(),
                  ),
                ),
                
                SizedBox(height: 15.h),
                
                // رقم الهاتف
                TextField(
                  controller: phoneController,
                  textAlign: TextAlign.right,
                  keyboardType: TextInputType.phone,
                  decoration: InputDecoration(
                    labelText: "رقم الهاتف",
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text("إلغاء"),
            ),
            ElevatedButton(
              onPressed: () {
                if (titleController.text.isNotEmpty &&
                    addressController.text.isNotEmpty &&
                    phoneController.text.isNotEmpty) {
                  
                  final newAddress = {
                    "id": isEditing ? address["id"] : DateTime.now().millisecondsSinceEpoch.toString(),
                    "title": titleController.text,
                    "address": addressController.text,
                    "details": detailsController.text,
                    "phone": phoneController.text,
                    "type": selectedType,
                    "isDefault": isEditing ? address["isDefault"] : addresses.isEmpty,
                  };

                  setState(() {
                    if (isEditing) {
                      final index = addresses.indexWhere((a) => a["id"] == address["id"]);
                      addresses[index] = newAddress;
                    } else {
                      addresses.add(newAddress);
                    }
                  });

                  Navigator.pop(context);
                  
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(isEditing ? "تم تحديث العنوان" : "تم إضافة العنوان"),
                      backgroundColor: AppColors.successColor,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
              ),
              child: Text(isEditing ? "تحديث" : "إضافة"),
            ),
          ],
        ),
      ),
    );
  }
}
