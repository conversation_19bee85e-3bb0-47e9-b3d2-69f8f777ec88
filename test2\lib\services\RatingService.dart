import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/Rating.dart';

/// خدمة التقييمات المتقدمة
class RatingService {
  static final RatingService _instance = RatingService._internal();
  factory RatingService() => _instance;
  RatingService._internal();

  // مفاتيح التخزين
  static const String _ratingsKey = 'ratings';
  static const String _userRatingsKey = 'user_ratings';
  static const String _ratingStatsKey = 'rating_stats';

  List<Rating> _ratings = [];
  List<UserRating> _userRatings = [];
  Map<String, RatingStats> _ratingStats = {};

  // Getters
  List<Rating> get ratings => List.unmodifiable(_ratings);
  List<UserRating> get userRatings => List.unmodifiable(_userRatings);
  Map<String, RatingStats> get ratingStats => Map.unmodifiable(_ratingStats);

  /// تهيئة خدمة التقييمات
  Future<void> initialize() async {
    try {
      await _loadRatings();
      await _loadUserRatings();
      await _loadRatingStats();
      
      // إضافة تقييمات تجريبية إذا لم توجد
      if (_ratings.isEmpty) {
        await _addSampleRatings();
      }
    } catch (e) {
      print('خطأ في تهيئة خدمة التقييمات: $e');
    }
  }

  /// إضافة تقييم جديد
  Future<bool> addRating({
    required String itemId,
    required RatingType itemType,
    required double rating,
    required String comment,
    String? userId,
    String? userName,
  }) async {
    try {
      // التحقق من صحة التقييم
      if (rating < 1 || rating > 5) {
        throw Exception('التقييم يجب أن يكون بين 1 و 5');
      }

      final newRating = Rating(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        itemId: itemId,
        itemType: itemType,
        rating: rating,
        comment: comment,
        userId: userId ?? 'anonymous',
        userName: userName ?? 'مستخدم مجهول',
        timestamp: DateTime.now(),
      );

      _ratings.insert(0, newRating);

      // إضافة للتقييمات الشخصية
      final userRating = UserRating(
        id: newRating.id,
        itemId: itemId,
        itemType: itemType,
        rating: rating,
        comment: comment,
        timestamp: DateTime.now(),
      );

      _userRatings.insert(0, userRating);

      // تحديث الإحصائيات
      await _updateRatingStats(itemId, itemType);

      // حفظ البيانات
      await _saveRatings();
      await _saveUserRatings();
      await _saveRatingStats();

      return true;
    } catch (e) {
      print('خطأ في إضافة التقييم: $e');
      return false;
    }
  }

  /// تحديث تقييم موجود
  Future<bool> updateRating({
    required String ratingId,
    required double rating,
    required String comment,
  }) async {
    try {
      final index = _ratings.indexWhere((r) => r.id == ratingId);
      if (index == -1) {
        throw Exception('التقييم غير موجود');
      }

      final oldRating = _ratings[index];
      final updatedRating = oldRating.copyWith(
        rating: rating,
        comment: comment,
        timestamp: DateTime.now(),
      );

      _ratings[index] = updatedRating;

      // تحديث التقييم الشخصي
      final userIndex = _userRatings.indexWhere((r) => r.id == ratingId);
      if (userIndex != -1) {
        _userRatings[userIndex] = _userRatings[userIndex].copyWith(
          rating: rating,
          comment: comment,
          timestamp: DateTime.now(),
        );
      }

      // تحديث الإحصائيات
      await _updateRatingStats(oldRating.itemId, oldRating.itemType);

      // حفظ البيانات
      await _saveRatings();
      await _saveUserRatings();
      await _saveRatingStats();

      return true;
    } catch (e) {
      print('خطأ في تحديث التقييم: $e');
      return false;
    }
  }

  /// حذف تقييم
  Future<bool> deleteRating(String ratingId) async {
    try {
      final index = _ratings.indexWhere((r) => r.id == ratingId);
      if (index == -1) {
        throw Exception('التقييم غير موجود');
      }

      final rating = _ratings[index];
      _ratings.removeAt(index);

      // حذف من التقييمات الشخصية
      _userRatings.removeWhere((r) => r.id == ratingId);

      // تحديث الإحصائيات
      await _updateRatingStats(rating.itemId, rating.itemType);

      // حفظ البيانات
      await _saveRatings();
      await _saveUserRatings();
      await _saveRatingStats();

      return true;
    } catch (e) {
      print('خطأ في حذف التقييم: $e');
      return false;
    }
  }

  /// الحصول على تقييمات عنصر معين
  List<Rating> getRatingsForItem(String itemId, RatingType itemType) {
    return _ratings
        .where((r) => r.itemId == itemId && r.itemType == itemType)
        .toList();
  }

  /// الحصول على تقييم المستخدم لعنصر معين
  UserRating? getUserRatingForItem(String itemId, RatingType itemType) {
    try {
      return _userRatings.firstWhere(
        (r) => r.itemId == itemId && r.itemType == itemType,
      );
    } catch (e) {
      return null;
    }
  }

  /// التحقق من وجود تقييم للمستخدم
  bool hasUserRatedItem(String itemId, RatingType itemType) {
    return getUserRatingForItem(itemId, itemType) != null;
  }

  /// الحصول على إحصائيات التقييم لعنصر معين
  RatingStats? getRatingStats(String itemId, RatingType itemType) {
    final key = '${itemType.toString()}_$itemId';
    return _ratingStats[key];
  }

  /// الحصول على متوسط التقييم لعنصر معين
  double getAverageRating(String itemId, RatingType itemType) {
    final stats = getRatingStats(itemId, itemType);
    return stats?.averageRating ?? 0.0;
  }

  /// الحصول على عدد التقييمات لعنصر معين
  int getRatingCount(String itemId, RatingType itemType) {
    final stats = getRatingStats(itemId, itemType);
    return stats?.totalCount ?? 0;
  }

  /// الحصول على التقييمات الأخيرة
  List<Rating> getRecentRatings({int limit = 10}) {
    return _ratings.take(limit).toList();
  }

  /// الحصول على أفضل التقييمات
  List<Rating> getTopRatings({int limit = 10}) {
    final sortedRatings = List<Rating>.from(_ratings);
    sortedRatings.sort((a, b) => b.rating.compareTo(a.rating));
    return sortedRatings.take(limit).toList();
  }

  /// الحصول على تقييمات المستخدم
  List<UserRating> getUserRatings({int limit = 20}) {
    return _userRatings.take(limit).toList();
  }

  /// البحث في التقييمات
  List<Rating> searchRatings(String query) {
    if (query.trim().isEmpty) return [];

    final lowerQuery = query.toLowerCase();
    return _ratings.where((rating) {
      return rating.comment.toLowerCase().contains(lowerQuery) ||
             rating.userName.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// تحديث إحصائيات التقييم
  Future<void> _updateRatingStats(String itemId, RatingType itemType) async {
    final itemRatings = getRatingsForItem(itemId, itemType);
    
    if (itemRatings.isEmpty) {
      final key = '${itemType.toString()}_$itemId';
      _ratingStats.remove(key);
      return;
    }

    final totalRating = itemRatings.fold<double>(0, (sum, r) => sum + r.rating);
    final averageRating = totalRating / itemRatings.length;

    // حساب توزيع النجوم
    final starDistribution = <int, int>{};
    for (int i = 1; i <= 5; i++) {
      starDistribution[i] = itemRatings.where((r) => r.rating.round() == i).length;
    }

    final stats = RatingStats(
      itemId: itemId,
      itemType: itemType,
      totalCount: itemRatings.length,
      averageRating: averageRating,
      starDistribution: starDistribution,
      lastUpdated: DateTime.now(),
    );

    final key = '${itemType.toString()}_$itemId';
    _ratingStats[key] = stats;
  }

  /// تحميل التقييمات
  Future<void> _loadRatings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ratingsJson = prefs.getStringList(_ratingsKey) ?? [];
      
      _ratings = ratingsJson
          .map((json) => Rating.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      print('خطأ في تحميل التقييمات: $e');
      _ratings = [];
    }
  }

  /// حفظ التقييمات
  Future<void> _saveRatings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ratingsJson = _ratings
          .map((rating) => jsonEncode(rating.toJson()))
          .toList();
      
      await prefs.setStringList(_ratingsKey, ratingsJson);
    } catch (e) {
      print('خطأ في حفظ التقييمات: $e');
    }
  }

  /// تحميل التقييمات الشخصية
  Future<void> _loadUserRatings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userRatingsJson = prefs.getStringList(_userRatingsKey) ?? [];
      
      _userRatings = userRatingsJson
          .map((json) => UserRating.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      print('خطأ في تحميل التقييمات الشخصية: $e');
      _userRatings = [];
    }
  }

  /// حفظ التقييمات الشخصية
  Future<void> _saveUserRatings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userRatingsJson = _userRatings
          .map((rating) => jsonEncode(rating.toJson()))
          .toList();
      
      await prefs.setStringList(_userRatingsKey, userRatingsJson);
    } catch (e) {
      print('خطأ في حفظ التقييمات الشخصية: $e');
    }
  }

  /// تحميل إحصائيات التقييمات
  Future<void> _loadRatingStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = prefs.getString(_ratingStatsKey);
      
      if (statsJson != null) {
        final Map<String, dynamic> statsMap = jsonDecode(statsJson);
        _ratingStats = statsMap.map((key, value) =>
            MapEntry(key, RatingStats.fromJson(value)));
      }
    } catch (e) {
      print('خطأ في تحميل إحصائيات التقييمات: $e');
      _ratingStats = {};
    }
  }

  /// حفظ إحصائيات التقييمات
  Future<void> _saveRatingStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsMap = _ratingStats.map((key, value) =>
          MapEntry(key, value.toJson()));
      
      await prefs.setString(_ratingStatsKey, jsonEncode(statsMap));
    } catch (e) {
      print('خطأ في حفظ إحصائيات التقييمات: $e');
    }
  }

  /// إضافة تقييمات تجريبية
  Future<void> _addSampleRatings() async {
    final sampleRatings = [
      Rating(
        id: '1',
        itemId: '1',
        itemType: RatingType.product,
        rating: 4.5,
        comment: 'منتج ممتاز وجودة عالية، أنصح بشرائه',
        userId: 'user1',
        userName: 'أحمد محمد',
        timestamp: DateTime.now().subtract(Duration(hours: 2)),
      ),
      Rating(
        id: '2',
        itemId: '1',
        itemType: RatingType.store,
        rating: 4.8,
        comment: 'خدمة رائعة وتوصيل سريع',
        userId: 'user2',
        userName: 'فاطمة علي',
        timestamp: DateTime.now().subtract(Duration(hours: 5)),
      ),
      Rating(
        id: '3',
        itemId: '2',
        itemType: RatingType.product,
        rating: 3.5,
        comment: 'المنتج جيد لكن يمكن تحسينه',
        userId: 'user3',
        userName: 'محمد سالم',
        timestamp: DateTime.now().subtract(Duration(days: 1)),
      ),
    ];

    _ratings.addAll(sampleRatings);
    
    // تحديث الإحصائيات لكل عنصر
    for (final rating in sampleRatings) {
      await _updateRatingStats(rating.itemId, rating.itemType);
    }

    await _saveRatings();
    await _saveRatingStats();
  }
}
