{"logs": [{"outputFile": "com.example.test2.app-mergeDebugResources-44:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\88e3d0d424506a1c1cef1c33bd4edbb3\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-cs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4523", "endColumns": "142", "endOffsets": "4661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2fb1c87eb9bd4c095f30b9ec565cd4a6\\transformed\\appcompat-1.1.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,509,614,731,809,886,977,1069,1164,1258,1353,1446,1541,1638,1729,1820,1903,2007,2119,2218,2324,2435,2537,2700,2798", "endColumns": "106,101,108,85,104,116,77,76,90,91,94,93,94,92,94,96,90,90,82,103,111,98,105,110,101,162,97,81", "endOffsets": "207,309,418,504,609,726,804,881,972,1064,1159,1253,1348,1441,1536,1633,1724,1815,1898,2002,2114,2213,2319,2430,2532,2695,2793,2875"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,509,614,731,809,886,977,1069,1164,1258,1353,1446,1541,1638,1729,1820,1903,2007,2119,2218,2324,2435,2537,2700,6177", "endColumns": "106,101,108,85,104,116,77,76,90,91,94,93,94,92,94,96,90,90,82,103,111,98,105,110,101,162,97,81", "endOffsets": "207,309,418,504,609,726,804,881,972,1064,1159,1253,1348,1441,1536,1633,1724,1815,1898,2002,2114,2213,2319,2430,2532,2695,2793,6254"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\47ac933c65727cbf2c22795a6a720497\\transformed\\preference-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,276,354,501,670,754", "endColumns": "72,97,77,146,168,83,80", "endOffsets": "173,271,349,496,665,749,830"}, "to": {"startLines": "54,55,56,57,60,61,62", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5781,5854,5952,6030,6360,6529,6613", "endColumns": "72,97,77,146,168,83,80", "endOffsets": "5849,5947,6025,6172,6524,6608,6689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\07c8d82ce007794779a5c5ed16d7cd9e\\transformed\\jetified-play-services-base-18.3.0\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,572,678,827,950,1058,1155,1326,1433,1593,1717,1874,2025,2089,2152", "endColumns": "101,155,120,105,148,122,107,96,170,106,159,123,156,150,63,62,81", "endOffsets": "294,450,571,677,826,949,1057,1154,1325,1432,1592,1716,1873,2024,2088,2151,2233"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3529,3635,3795,3920,4030,4183,4310,4422,4666,4841,4952,5116,5244,5405,5560,5628,5695", "endColumns": "105,159,124,109,152,126,111,100,174,110,163,127,160,154,67,66,85", "endOffsets": "3630,3790,3915,4025,4178,4305,4417,4518,4836,4947,5111,5239,5400,5555,5623,5690,5776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4f9d4625548a64e0918456efe245c9bb\\transformed\\core-1.13.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "29,30,31,32,33,34,35,59", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2798,2896,2998,3099,3198,3303,3410,6259", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "2891,2993,3094,3193,3298,3405,3524,6355"}}]}]}