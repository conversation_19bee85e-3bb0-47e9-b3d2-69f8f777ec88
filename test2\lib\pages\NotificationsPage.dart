import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/NotificationProvider.dart';
import '../models/Notification.dart';
import '../widgets/NotificationCard.dart';
import '../widgets/CustomSnackBars.dart';
import '../pages/NotificationSettingsPage.dart';

/// صفحة الإشعارات
class NotificationsPage extends StatefulWidget {
  @override
  _NotificationsPageState createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // تهيئة الإشعارات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final notificationProvider =
          Provider.of<NotificationProvider>(context, listen: false);
      notificationProvider.initialize();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: Consumer<NotificationProvider>(
        builder: (context, notificationProvider, child) {
          if (notificationProvider.isLoading) {
            return _buildLoadingState();
          }

          if (notificationProvider.error != null) {
            return _buildErrorState(notificationProvider);
          }

          if (!notificationProvider.hasNotifications) {
            return _buildEmptyState();
          }

          return Column(
            children: [
              // إحصائيات سريعة
              _buildQuickStats(notificationProvider),

              // التبويبات
              _buildTabBar(notificationProvider),

              // محتوى التبويبات
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildAllNotifications(notificationProvider),
                    _buildUnreadNotifications(notificationProvider),
                    _buildOrderNotifications(notificationProvider),
                    _buildOfferNotifications(notificationProvider),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Color(0xFF4C53A5),
      elevation: 0,
      title: Text(
        'الإشعارات',
        style: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        // زر الإعدادات
        IconButton(
          icon: Icon(Icons.settings, color: Colors.white),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => NotificationSettingsPage(),
              ),
            );
          },
        ),

        // قائمة الخيارات
        Consumer<NotificationProvider>(
          builder: (context, notificationProvider, child) {
            return PopupMenuButton<String>(
              icon: Icon(Icons.more_vert, color: Colors.white),
              onSelected: (value) {
                switch (value) {
                  case 'mark_all_read':
                    _markAllAsRead(notificationProvider);
                    break;
                  case 'clear_all':
                    _showClearAllDialog(notificationProvider);
                    break;
                  case 'refresh':
                    notificationProvider.refresh();
                    break;
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'mark_all_read',
                  child: Row(
                    children: [
                      Icon(Icons.done_all, color: Colors.grey[600]),
                      SizedBox(width: 8),
                      Text('قراءة الكل'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'clear_all',
                  child: Row(
                    children: [
                      Icon(Icons.clear_all, color: Colors.grey[600]),
                      SizedBox(width: 8),
                      Text('مسح الكل'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'refresh',
                  child: Row(
                    children: [
                      Icon(Icons.refresh, color: Colors.grey[600]),
                      SizedBox(width: 8),
                      Text('تحديث'),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildQuickStats(NotificationProvider notificationProvider) {
    final stats = notificationProvider.getNotificationStats();

    return Container(
      padding: EdgeInsets.all(16),
      margin: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildStatItem(
            icon: Icons.notifications,
            label: 'الإجمالي',
            value: stats['total']?.toString() ?? '0',
            color: Color(0xFF4C53A5),
          ),
          _buildStatItem(
            icon: Icons.mark_email_unread,
            label: 'غير مقروء',
            value: stats['unread']?.toString() ?? '0',
            color: Colors.red,
          ),
          _buildStatItem(
            icon: Icons.shopping_bag,
            label: 'الطلبات',
            value: ((stats['${NotificationType.orderConfirmed}'] ?? 0) +
                    (stats['${NotificationType.orderPreparing}'] ?? 0) +
                    (stats['${NotificationType.orderReady}'] ?? 0) +
                    (stats['${NotificationType.orderDelivered}'] ?? 0))
                .toString(),
            color: Colors.green,
          ),
          _buildStatItem(
            icon: Icons.local_offer,
            label: 'العروض',
            value: stats['${NotificationType.offer}']?.toString() ?? '0',
            color: Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Expanded(
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(NotificationProvider notificationProvider) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: Color(0xFF4C53A5),
          borderRadius: BorderRadius.circular(25),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey[600],
        labelStyle: TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
        tabs: [
          Tab(text: 'الكل (${notificationProvider.notifications.length})'),
          Tab(text: 'غير مقروء (${notificationProvider.unreadCount})'),
          Tab(text: 'الطلبات'),
          Tab(text: 'العروض'),
        ],
      ),
    );
  }

  Widget _buildAllNotifications(NotificationProvider notificationProvider) {
    final groupedNotifications =
        notificationProvider.getNotificationsGroupedByDate();

    if (groupedNotifications.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: groupedNotifications.length,
      itemBuilder: (context, index) {
        final dateKey = groupedNotifications.keys.elementAt(index);
        final notifications = groupedNotifications[dateKey]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان التاريخ
            Padding(
              padding: EdgeInsets.symmetric(vertical: 8),
              child: Text(
                dateKey,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[600],
                ),
              ),
            ),

            // الإشعارات
            ...notifications.map((notification) => NotificationCard(
                  notification: notification,
                  onTap: () =>
                      _onNotificationTap(notification, notificationProvider),
                  onDelete: () => _deleteNotification(
                      notification.id, notificationProvider),
                )),

            SizedBox(height: 16),
          ],
        );
      },
    );
  }

  Widget _buildUnreadNotifications(NotificationProvider notificationProvider) {
    final unreadNotifications = notificationProvider.getUnreadNotifications();

    if (unreadNotifications.isEmpty) {
      return _buildEmptyState(message: 'لا توجد إشعارات غير مقروءة');
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: unreadNotifications.length,
      itemBuilder: (context, index) {
        final notification = unreadNotifications[index];
        return NotificationCard(
          notification: notification,
          onTap: () => _onNotificationTap(notification, notificationProvider),
          onDelete: () =>
              _deleteNotification(notification.id, notificationProvider),
        );
      },
    );
  }

  Widget _buildOrderNotifications(NotificationProvider notificationProvider) {
    final orderNotifications = notificationProvider.notifications
        .where((n) =>
            n.type == NotificationType.orderConfirmed ||
            n.type == NotificationType.orderPreparing ||
            n.type == NotificationType.orderReady ||
            n.type == NotificationType.orderDelivered)
        .toList();

    if (orderNotifications.isEmpty) {
      return _buildEmptyState(message: 'لا توجد إشعارات طلبات');
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: orderNotifications.length,
      itemBuilder: (context, index) {
        final notification = orderNotifications[index];
        return NotificationCard(
          notification: notification,
          onTap: () => _onNotificationTap(notification, notificationProvider),
          onDelete: () =>
              _deleteNotification(notification.id, notificationProvider),
        );
      },
    );
  }

  Widget _buildOfferNotifications(NotificationProvider notificationProvider) {
    final offerNotifications =
        notificationProvider.getNotificationsByType(NotificationType.offer);

    if (offerNotifications.isEmpty) {
      return _buildEmptyState(message: 'لا توجد إشعارات عروض');
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: offerNotifications.length,
      itemBuilder: (context, index) {
        final notification = offerNotifications[index];
        return NotificationCard(
          notification: notification,
          onTap: () => _onNotificationTap(notification, notificationProvider),
          onDelete: () =>
              _deleteNotification(notification.id, notificationProvider),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4C53A5)),
          ),
          SizedBox(height: 16),
          Text(
            'جاري تحميل الإشعارات...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(NotificationProvider notificationProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[300],
          ),
          SizedBox(height: 16),
          Text(
            'حدث خطأ',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red[700],
            ),
          ),
          SizedBox(height: 8),
          Text(
            notificationProvider.error!,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              notificationProvider.clearError();
              notificationProvider.initialize();
            },
            child: Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFF4C53A5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState({String? message}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            message ?? 'لا توجد إشعارات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'ستظهر الإشعارات هنا عند وصولها',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _onNotificationTap(
      AppNotification notification, NotificationProvider notificationProvider) {
    // قراءة الإشعار إذا لم يكن مقروءاً
    if (!notification.isRead) {
      notificationProvider.markAsRead(notification.id);
    }

    // التنقل حسب نوع الإشعار
    if (notification.actionUrl != null) {
      // يمكن إضافة منطق التنقل هنا
      print('التنقل إلى: ${notification.actionUrl}');
    }
  }

  void _deleteNotification(
      String notificationId, NotificationProvider notificationProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف الإشعار'),
        content: Text('هل أنت متأكد من حذف هذا الإشعار؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              notificationProvider.deleteNotification(notificationId);
              Navigator.pop(context);
              CustomSnackBars.showSuccess(context, message: 'تم حذف الإشعار');
            },
            child: Text('حذف'),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
          ),
        ],
      ),
    );
  }

  void _markAllAsRead(NotificationProvider notificationProvider) {
    notificationProvider.markAllAsRead();
    CustomSnackBars.showSuccess(context, message: 'تم قراءة جميع الإشعارات');
  }

  void _showClearAllDialog(NotificationProvider notificationProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('مسح جميع الإشعارات'),
        content: Text(
            'هل أنت متأكد من مسح جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              notificationProvider.clearAllNotifications();
              Navigator.pop(context);
              CustomSnackBars.showSuccess(context,
                  message: 'تم مسح جميع الإشعارات');
            },
            child: Text('مسح الكل'),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
          ),
        ],
      ),
    );
  }
}
