# شرح سلوك التمرير للعروض
## Scroll Behavior Explanation for Offers

## 🎯 **السلوك المطلوب والمطبق**

### **📱 السلوك الحالي (يعمل كما هو مطلوب):**

#### **🔼 عند التمرير لأعلى:**
- **scrollOffset يزيد** (من 0 إلى 220+)
- **ارتفاع العروض يقل** (من 220px إلى 0px)
- **العروض تختفي تدريجياً**
- **عنوان العروض يختفي**
- **عنوان المطاعم يظهر**

#### **🔽 عند التمرير لأسفل (العكس):**
- **scrollOffset يقل** (من 220+ إلى 0)
- **ارتفاع العروض يزيد** (من 0px إلى 220px)
- **العروض تظهر تدريجياً**
- **عنوان العروض يظهر**
- **عنوان المطاعم يختفي**

#### **🏠 في الأعلى (الوضع الطبيعي):**
- **scrollOffset = 0**
- **ارتفاع العروض = 220px** (كامل)
- **العروض ظاهرة بالكامل**
- **عنوان العروض ظاهر**
- **عنوان المطاعم مخفي**

## 🔧 **المعادلة الرياضية**

### **المعادلة الأساسية:**
```dart
double newHeight = _maxOffersHeight - (scrollOffset * changeRate);
```

### **تفسير المعادلة:**
- **_maxOffersHeight = 220px** (الارتفاع الأقصى)
- **scrollOffset** = موضع التمرير (يزيد عند التمرير لأعلى)
- **changeRate = 1.0** (معدل التغيير)

### **أمثلة عملية:**

#### **🏠 في الأعلى:**
```dart
scrollOffset = 0
newHeight = 220 - (0 * 1.0) = 220px ✅ العروض كاملة
```

#### **🔼 تمرير قليل لأعلى:**
```dart
scrollOffset = 50
newHeight = 220 - (50 * 1.0) = 170px ✅ العروض تقل قليلاً
```

#### **🔼 تمرير متوسط لأعلى:**
```dart
scrollOffset = 110
newHeight = 220 - (110 * 1.0) = 110px ✅ العروض نصف الحجم
```

#### **🔼 تمرير كثير لأعلى:**
```dart
scrollOffset = 220
newHeight = 220 - (220 * 1.0) = 0px ✅ العروض مختفية
```

#### **🔼 تمرير أكثر لأعلى:**
```dart
scrollOffset = 300
newHeight = 220 - (300 * 1.0) = -80px → 0px ✅ العروض مختفية (محدودة بـ clamp)
```

## 📊 **مراحل التحول**

### **المرحلة 1: البداية (scrollOffset: 0-55)**
- **ارتفاع العروض:** 220px → 165px
- **عنوان العروض:** شفافية 100% → 75%
- **عنوان المطاعم:** شفافية 0% → 25%
- **حالة عنوان المطاعم:** مخفي في الأعلى

### **المرحلة 2: التحول (scrollOffset: 55-110)**
- **ارتفاع العروض:** 165px → 110px
- **عنوان العروض:** شفافية 75% → 50%
- **عنوان المطاعم:** شفافية 25% → 50%
- **حالة عنوان المطاعم:** يبدأ بالظهور في الأعلى

### **المرحلة 3: نقطة التحول (scrollOffset: 110)**
- **ارتفاع العروض:** 110px (50% من الحجم الأصلي)
- **عنوان العروض:** شفافية 50%
- **عنوان المطاعم:** شفافية 50%
- **حالة عنوان المطاعم:** يصبح ثابت في الأعلى ✅

### **المرحلة 4: الاختفاء (scrollOffset: 110-220)**
- **ارتفاع العروض:** 110px → 0px
- **عنوان العروض:** شفافية 50% → 0%
- **عنوان المطاعم:** شفافية 50% → 100%
- **حالة عنوان المطاعم:** ثابت في الأعلى

### **المرحلة 5: الاختفاء الكامل (scrollOffset: 220+)**
- **ارتفاع العروض:** 0px
- **عنوان العروض:** شفافية 0% (مختفي)
- **عنوان المطاعم:** شفافية 100% (ثابت في الأعلى)
- **حالة عنوان المطاعم:** ثابت ومرئي بالكامل

## 🔄 **العملية العكسية (التمرير لأسفل)**

### **من المرحلة 5 إلى المرحلة 1:**
- **scrollOffset يقل** من 220+ إلى 0
- **جميع القيم تعود بالعكس**
- **العروض تظهر تدريجياً**
- **عنوان العروض يعود**
- **عنوان المطاعم يختفي من الأعلى**

## ⚙️ **معدل التغيير (changeRate)**

### **القيم المختلفة:**
```dart
changeRate = 0.5  // تغيير بطيء جداً (يحتاج تمرير أكثر)
changeRate = 1.0  // تغيير متوسط (الحالي) ✅
changeRate = 1.5  // تغيير سريع
changeRate = 2.0  // تغيير سريع جداً
```

### **تأثير معدل التغيير:**
- **changeRate أقل:** العروض تحتاج تمرير أكثر لتختفي
- **changeRate أكبر:** العروض تختفي بتمرير أقل

## 🎯 **نقاط التحكم الرئيسية**

### **1. نقطة ظهور عنوان المطاعم:**
```dart
bool newShowRestaurantsTitle = newHeight < (_maxOffersHeight * 0.5);
// عندما ارتفاع العروض أقل من 110px (50%)
```

### **2. شفافية عنوان العروض:**
```dart
double newOffersTitleOpacity = (newHeight / _maxOffersHeight).clamp(0.0, 1.0);
// نسبة مئوية من الارتفاع الحالي للارتفاع الأقصى
```

### **3. شفافية عنوان المطاعم:**
```dart
double newRestaurantsTitleOpacity = (1.0 - (newHeight / _maxOffersHeight)).clamp(0.0, 1.0);
// عكس شفافية عنوان العروض
```

## 🚀 **تحسينات الأداء**

### **التحديث الشرطي:**
```dart
if ((_offersHeight - newHeight).abs() > 2 ||
    (_offersTitleOpacity - newOffersTitleOpacity).abs() > 0.05 ||
    _showRestaurantsTitle != newShowRestaurantsTitle) {
  setState(() { ... });
}
```

### **فوائد التحديث الشرطي:**
- **تقليل استهلاك المعالج:** تحديث فقط عند الحاجة
- **منع الوميض:** عدم تحديث مستمر
- **سلاسة الحركة:** انتقالات طبيعية

## 📱 **تجربة المستخدم**

### **السيناريو الكامل:**
1. **المستخدم في الأعلى:** يرى العروض كاملة مع عنوانها
2. **يبدأ التمرير لأعلى:** العروض تقل تدريجياً
3. **يستمر التمرير:** عنوان العروض يختفي وعنوان المطاعم يظهر
4. **يصل لنقطة التحول:** عنوان المطاعم يثبت في الأعلى
5. **يكمل التمرير:** العروض تختفي تماماً
6. **يمرر لأسفل:** العملية تحدث بالعكس
7. **يعود للأعلى:** كل شيء يرجع للوضع الطبيعي

## 🎨 **التصميم البصري**

### **الانتقالات:**
- **المدة:** 150ms (سريع وسلس)
- **المنحنى:** `Curves.easeInOut` (طبيعي)
- **التزامن:** جميع العناصر تتحرك معاً

### **الشفافية:**
- **تدرج سلس:** من 0% إلى 100%
- **عدم قفز:** بفضل clamp()
- **انتقال طبيعي:** بين العناوين

## ✅ **التأكيد على السلوك المطلوب**

### **✅ يعمل حالياً:**
- 🔼 **التمرير لأعلى:** العروض تقل ✅
- 🔽 **التمرير لأسفل:** العروض تزيد ✅
- 🏠 **العودة للأعلى:** العروض ترجع للوضع الطبيعي ✅
- 🔄 **العملية عكسية:** تماماً كما هو مطلوب ✅

### **🎯 النتيجة:**
السلوك المطلوب **يعمل بالفعل** والكود صحيح!

## 🔧 **إعدادات قابلة للتخصيص**

### **لتغيير سرعة التفاعل:**
```dart
double changeRate = 1.0; // يمكن تغييرها حسب الحاجة
```

### **لتغيير نقطة التحول:**
```dart
bool newShowRestaurantsTitle = newHeight < (_maxOffersHeight * 0.5); // 50%
// يمكن تغيير 0.5 إلى 0.3 (30%) أو 0.7 (70%)
```

### **لتغيير حساسية التحديث:**
```dart
if ((_offersHeight - newHeight).abs() > 2) // يمكن تغيير 2 إلى 1 أو 3
```

🎉 **السلوك يعمل تماماً كما هو مطلوب!**
