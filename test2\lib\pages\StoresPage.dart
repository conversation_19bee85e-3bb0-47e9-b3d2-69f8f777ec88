import 'package:flutter/material.dart';
import 'package:test2/widgets/StoresAppBar.dart';
import 'package:test2/pages/StoreDetailsPage.dart';
import 'package:test2/widgets/CustomBottomNavBar.dart';
import 'package:test2/utils/DataManager.dart';
import 'package:test2/utils/SearchManager.dart';
import 'package:test2/widgets/SearchWidget.dart';

class StoresPage extends StatefulWidget {
  @override
  _StoresPageState createState() => _StoresPageState();
}

class _StoresPageState extends State<StoresPage> {
  String selectedCategory = "الكل"; // التصنيف المختار
  String searchQuery = ""; // نص البحث

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFEDECF2),
      appBar: StoresAppBar(),
      body: Column(
        children: [
          // الجزء الثابت - البحث والتصنيفات
          Container(
            color: Color(0xFFEDECF2),
            padding: EdgeInsets.all(15),
            child: Column(
              textDirection: TextDirection.rtl,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SearchWidget(
                  hintText: "ابحث عن متجر...",
                  onSearchChanged: (query) {
                    setState(() {
                      searchQuery = query;
                    });
                  },
                  showSuggestions: true,
                  suggestionType: 'stores',
                  suffixIcon: searchQuery.isNotEmpty ? Icons.clear : null,
                ),
                SizedBox(height: 10),
                _buildCategoriesRow(),
                SizedBox(height: 10),
              ],
            ),
          ),

          // الجزء القابل للتمرير - قائمة المتاجر
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 15),
              child: _buildStoresList(context),
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: Offset(0, -2),
              ),
            ],
          ),
          child: FlatBottomNavBar(
            currentIndex: 1, // صفحة المتاجر
            onTap: (index) {
              NavigationHelper.navigateToPage(context, index);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesRow() {
    // استخدام البيانات من DataManager
    const categories = DataManager.storeCategories;

    // فصل "الكل" عن باقي التصنيفات
    final allCategory = categories.first; // "الكل" هو العنصر الأول
    final otherCategories = categories.skip(1).toList(); // باقي التصنيفات

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      textDirection: TextDirection.rtl,
      children: [
        // عنوان التصنيفات
        // Text(
        //   "التصنيفات",
        //   style: TextStyle(
        //     fontSize: 16,
        //     fontWeight: FontWeight.bold,
        //     color: Color(0xFF4C53A5),
        //   ),
        //   textAlign: TextAlign.right,
        // ),
        // SizedBox(height: 8),
        // صف التصنيفات مع "الكل" ثابت
        SizedBox(
          height: 90,
          child: Row(
            textDirection: TextDirection.rtl,
            children: [
              // تصنيف "الكل" ثابت
              StoreCategoryItem(
                key: ValueKey('store_category_${allCategory["name"]}'),
                category: allCategory,
                isSelected: selectedCategory == allCategory["name"],
                onTap: () {
                  setState(() {
                    selectedCategory = allCategory["name"];
                  });
                },
              ),

              SizedBox(width: 8),

              // باقي التصنيفات قابلة للتمرير
              Expanded(
                child: ListView.builder(
                  reverse: true,
                  scrollDirection: Axis.horizontal,
                  itemCount: otherCategories.length,
                  itemBuilder: (context, index) {
                    final category = otherCategories[index];
                    final isSelected = selectedCategory == category["name"];

                    return StoreCategoryItem(
                      key: ValueKey('store_category_${category["name"]}'),
                      category: category,
                      isSelected: isSelected,
                      onTap: () {
                        setState(() {
                          selectedCategory = category["name"];
                        });
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStoresList(BuildContext context) {
    // استخدام SearchManager أو DataManager حسب وجود البحث
    final filteredStores = searchQuery.isNotEmpty
        ? SearchManager.searchStores(searchQuery,
            category: selectedCategory != "الكل" ? selectedCategory : null)
        : DataManager.getFilteredStores(selectedCategory);

    return ListView.builder(
      itemCount: filteredStores.length,
      itemBuilder: (context, index) {
        final store = filteredStores[index];
        return GestureDetector(
          onTap: () {
            // التنقل إلى صفحة المتجر
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => StoreDetailsPage(store: store),
              ),
            );
          },
          child: Container(
            margin: EdgeInsets.only(bottom: 5),
            padding: EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              textDirection: TextDirection.rtl,
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    image: DecorationImage(
                      image: AssetImage(store["image"]),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SizedBox(width: 15),
                Expanded(
                  child: Column(
                    textDirection: TextDirection.rtl,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        textDirection: TextDirection.rtl,
                        children: [
                          Text(
                            store["name"],
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF4C53A5),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: (store["isOpen"] ?? false)
                                  ? Colors.green // أخضر للمفتوح
                                  : Colors.red, // أحمر للمغلق
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              (store["isOpen"] ?? false) ? "مفتوح" : "مغلق",
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white, // نص أبيض
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      // SizedBox(height: 5),
                      // Text(
                      //   store["category"],
                      //   style: TextStyle(
                      //     fontSize: 14,
                      //     color: Colors.grey,
                      //   ),
                      // ),
                      // SizedBox(height: 5),
                      Row(
                        textDirection: TextDirection.rtl,
                        children: [
                          Icon(Icons.star, color: Colors.amber, size: 18),
                          SizedBox(width: 5),
                          Text(
                            "${store["rating"]}",
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(width: 15),
                          Icon(Icons.access_time, color: Colors.grey, size: 18),
                          SizedBox(width: 5),
                          Text(
                            store["deliveryTime"],
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      // SizedBox(height: 8),
                      // حالة المحل (مفتوح/مغلق)
                    ],
                  ),
                ),
                // Icon(
                //   Icons.arrow_back_ios,
                //   color: Color(0xFF4C53A5),
                //   size: 20,
                // ),
              ],
            ),
          ),
        );
      },
    );
  }
}

// مكون منفصل لعنصر تصنيف المتجر لتحسين الأداء
class StoreCategoryItem extends StatelessWidget {
  final Map<String, dynamic> category;
  final bool isSelected;
  final VoidCallback onTap;

  const StoreCategoryItem({
    Key? key,
    required this.category,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        width: 80,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF4C53A5) : Colors.white,
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: isSelected ? const Color(0xFF4C53A5) : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isSelected
                  ? const Color(0xFF4C53A5).withOpacity(0.3)
                  : Colors.grey.withOpacity(0.1),
              spreadRadius: isSelected ? 2 : 1,
              blurRadius: isSelected ? 8 : 3,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              category["icon"],
              color: isSelected ? Colors.white : const Color(0xFF4C53A5),
              size: 24,
            ),
            const SizedBox(height: 6),
            Text(
              category["name"],
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.white : const Color(0xFF4C53A5),
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
