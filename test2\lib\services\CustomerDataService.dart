import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/UserProfile.dart';
import '../models/Address.dart';

/// خدمة إدارة بيانات العميل الموحدة باستخدام SharedPreferences
/// هذه الخدمة هي المصدر الوحيد لجميع بيانات العميل في النظام
class CustomerDataService {
  // Singleton pattern
  static final CustomerDataService _instance = CustomerDataService._internal();
  factory CustomerDataService() => _instance;
  CustomerDataService._internal();

  // مفاتيح التخزين
  static const String _customerProfileKey = 'customer_profile';
  static const String _isFirstTimeKey = 'is_first_time';
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _lastUpdateKey = 'last_update';

  // بيانات العميل المحملة في الذاكرة
  UserProfile? _customerProfile;
  bool _isLoaded = false;

  /// الحصول على بيانات العميل الحالية
  UserProfile? get customerProfile => _customerProfile;

  /// التحقق من تسجيل الدخول
  bool get isLoggedIn => _customerProfile != null;

  /// التحقق من أول مرة فتح التطبيق
  Future<bool> isFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isFirstTimeKey) ?? true;
  }

  /// تعيين أن التطبيق ليس أول مرة
  Future<void> setNotFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isFirstTimeKey, false);
  }

  /// تحميل بيانات العميل من SharedPreferences
  Future<bool> loadCustomerData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = prefs.getString(_customerProfileKey);
      
      if (profileJson != null && profileJson.isNotEmpty) {
        final profileMap = jsonDecode(profileJson) as Map<String, dynamic>;
        _customerProfile = UserProfile.fromMap(profileMap);
        _isLoaded = true;
        return true;
      } else {
        // إذا لم توجد بيانات، إنشاء بيانات تجريبية
        await _createSampleCustomerData();
        return true;
      }
    } catch (e) {
      print('خطأ في تحميل بيانات العميل: $e');
      // في حالة الخطأ، إنشاء بيانات تجريبية
      await _createSampleCustomerData();
      return false;
    }
  }

  /// حفظ بيانات العميل في SharedPreferences
  Future<bool> saveCustomerData(UserProfile profile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = jsonEncode(profile.toMap());
      
      await prefs.setString(_customerProfileKey, profileJson);
      await prefs.setBool(_isLoggedInKey, true);
      await prefs.setString(_lastUpdateKey, DateTime.now().toIso8601String());
      
      _customerProfile = profile;
      _isLoaded = true;
      
      return true;
    } catch (e) {
      print('خطأ في حفظ بيانات العميل: $e');
      return false;
    }
  }

  /// تحديث بيانات العميل
  Future<bool> updateCustomerData({
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? avatar,
    DateTime? dateOfBirth,
    String? gender,
    List<Address>? addresses,
    String? defaultAddressId,
    Map<String, dynamic>? preferences,
  }) async {
    if (_customerProfile == null) {
      await loadCustomerData();
    }

    if (_customerProfile == null) return false;

    try {
      final updatedProfile = UserProfile(
        id: _customerProfile!.id,
        firstName: firstName ?? _customerProfile!.firstName,
        lastName: lastName ?? _customerProfile!.lastName,
        email: email ?? _customerProfile!.email,
        phone: phone ?? _customerProfile!.phone,
        avatar: avatar ?? _customerProfile!.avatar,
        dateOfBirth: dateOfBirth ?? _customerProfile!.dateOfBirth,
        gender: gender ?? _customerProfile!.gender,
        addresses: addresses ?? _customerProfile!.addresses,
        defaultAddressId: defaultAddressId ?? _customerProfile!.defaultAddressId,
        preferences: preferences ?? _customerProfile!.preferences,
        createdAt: _customerProfile!.createdAt,
        updatedAt: DateTime.now(),
      );

      return await saveCustomerData(updatedProfile);
    } catch (e) {
      print('خطأ في تحديث بيانات العميل: $e');
      return false;
    }
  }

  /// إضافة عنوان جديد
  Future<bool> addAddress(Address address) async {
    if (_customerProfile == null) {
      await loadCustomerData();
    }

    if (_customerProfile == null) return false;

    try {
      final updatedAddresses = List<Address>.from(_customerProfile!.addresses);
      updatedAddresses.add(address);

      return await updateCustomerData(addresses: updatedAddresses);
    } catch (e) {
      print('خطأ في إضافة العنوان: $e');
      return false;
    }
  }

  /// تحديث عنوان موجود
  Future<bool> updateAddress(Address updatedAddress) async {
    if (_customerProfile == null) {
      await loadCustomerData();
    }

    if (_customerProfile == null) return false;

    try {
      final updatedAddresses = _customerProfile!.addresses.map((address) {
        return address.id == updatedAddress.id ? updatedAddress : address;
      }).toList();

      return await updateCustomerData(addresses: updatedAddresses);
    } catch (e) {
      print('خطأ في تحديث العنوان: $e');
      return false;
    }
  }

  /// حذف عنوان
  Future<bool> deleteAddress(String addressId) async {
    if (_customerProfile == null) {
      await loadCustomerData();
    }

    if (_customerProfile == null) return false;

    try {
      final updatedAddresses = _customerProfile!.addresses
          .where((address) => address.id != addressId)
          .toList();

      // إذا كان العنوان المحذوف هو الافتراضي، تعيين أول عنوان كافتراضي
      String? newDefaultId = _customerProfile!.defaultAddressId;
      if (newDefaultId == addressId && updatedAddresses.isNotEmpty) {
        newDefaultId = updatedAddresses.first.id;
      } else if (updatedAddresses.isEmpty) {
        newDefaultId = null;
      }

      return await updateCustomerData(
        addresses: updatedAddresses,
        defaultAddressId: newDefaultId,
      );
    } catch (e) {
      print('خطأ في حذف العنوان: $e');
      return false;
    }
  }

  /// تعيين العنوان الافتراضي
  Future<bool> setDefaultAddress(String addressId) async {
    if (_customerProfile == null) {
      await loadCustomerData();
    }

    if (_customerProfile == null) return false;

    try {
      // التحقق من وجود العنوان
      final addressExists = _customerProfile!.addresses
          .any((address) => address.id == addressId);

      if (!addressExists) return false;

      return await updateCustomerData(defaultAddressId: addressId);
    } catch (e) {
      print('خطأ في تعيين العنوان الافتراضي: $e');
      return false;
    }
  }

  /// الحصول على العنوان الافتراضي
  Address? getDefaultAddress() {
    if (_customerProfile == null) return null;

    if (_customerProfile!.defaultAddressId != null) {
      return _customerProfile!.addresses.firstWhere(
        (address) => address.id == _customerProfile!.defaultAddressId,
        orElse: () => _customerProfile!.addresses.isNotEmpty 
            ? _customerProfile!.addresses.first 
            : Address.empty(),
      );
    }

    return _customerProfile!.addresses.isNotEmpty 
        ? _customerProfile!.addresses.first 
        : null;
  }

  /// الحصول على جميع العناوين
  List<Address> getAllAddresses() {
    return _customerProfile?.addresses ?? [];
  }

  /// الحصول على الاسم الكامل
  String getFullName() {
    if (_customerProfile == null) return 'عميل';
    return _customerProfile!.fullName.isNotEmpty 
        ? _customerProfile!.fullName 
        : 'عميل';
  }

  /// الحصول على البريد الإلكتروني
  String getEmail() {
    return _customerProfile?.email ?? '';
  }

  /// الحصول على رقم الهاتف
  String getPhone() {
    return _customerProfile?.phone ?? '';
  }

  /// الحصول على معرف العميل
  String getCustomerId() {
    return _customerProfile?.id ?? '';
  }

  /// التحقق من اكتمال الملف الشخصي
  bool isProfileComplete() {
    if (_customerProfile == null) return false;
    return _customerProfile!.isProfileComplete;
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_customerProfileKey);
      await prefs.setBool(_isLoggedInKey, false);
      
      _customerProfile = null;
      _isLoaded = false;
    } catch (e) {
      print('خطأ في تسجيل الخروج: $e');
    }
  }

  /// حذف جميع بيانات العميل
  Future<void> deleteAllCustomerData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_customerProfileKey);
      await prefs.remove(_isLoggedInKey);
      await prefs.remove(_lastUpdateKey);
      
      _customerProfile = null;
      _isLoaded = false;
    } catch (e) {
      print('خطأ في حذف بيانات العميل: $e');
    }
  }

  /// إنشاء بيانات تجريبية للعميل
  Future<void> _createSampleCustomerData() async {
    final sampleProfile = MockUserData.getSampleUser();
    await saveCustomerData(sampleProfile);
  }

  /// التحقق من صحة رقم الهاتف
  bool isValidPhone(String phone) {
    // تحقق من رقم الهاتف السعودي
    final saudiPhoneRegex = RegExp(r'^(\+966|966|0)?5[0-9]{8}$');
    return saudiPhoneRegex.hasMatch(phone.replaceAll(' ', '').replaceAll('-', ''));
  }

  /// التحقق من صحة البريد الإلكتروني
  bool isValidEmail(String email) {
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return emailRegex.hasMatch(email);
  }

  /// الحصول على معلومات العميل للعرض
  Map<String, String> getCustomerDisplayInfo() {
    if (_customerProfile == null) {
      return {
        'name': 'عميل',
        'email': '',
        'phone': '',
        'address': 'لم يتم تحديد العنوان',
      };
    }

    final defaultAddress = getDefaultAddress();
    
    return {
      'name': getFullName(),
      'email': getEmail(),
      'phone': getPhone(),
      'address': defaultAddress?.formattedAddress ?? 'لم يتم تحديد العنوان',
    };
  }

  /// إعادة تحميل البيانات من SharedPreferences
  Future<bool> refreshCustomerData() async {
    _isLoaded = false;
    _customerProfile = null;
    return await loadCustomerData();
  }

  /// التحقق من تحميل البيانات
  bool get isDataLoaded => _isLoaded;

  /// ضمان تحميل البيانات
  Future<void> ensureDataLoaded() async {
    if (!_isLoaded) {
      await loadCustomerData();
    }
  }
}
