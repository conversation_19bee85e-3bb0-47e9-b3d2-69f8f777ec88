import 'package:flutter/material.dart';

class OffersManager {
  // أنواع العروض
  static const String DISCOUNT = 'discount';
  static const String NEW_ITEM = 'new_item';
  static const String NEW_DISH = 'new_dish';
  static const String SPECIAL_OFFER = 'special_offer';
  static const String FREE_DELIVERY = 'free_delivery';
  static const String BUY_ONE_GET_ONE = 'bogo';

  // بيانات العروض الوهمية
  static final List<Map<String, dynamic>> _offers = [
    {
      "id": "offer_1",
      "title": "خصم 50%",
      "subtitle": "على جميع البرجر",
      "description":
          "خصم كبير على جميع أنواع البرجر والوجبات السريعة لفترة محدودة",
      "type": DISCOUNT,
      "discountPercentage": 50,
      "storeName": "مطعم الشرق",
      "storeId": "مطعم الشرق",
      "image": "images/img/1.png",
      "backgroundImage": "images/img/1.png", // استخدام نفس صورة المنتج كخلفية
      "validUntil": "2025-01-31",
      "isActive": true,
      "backgroundColor": const Color(0xFFE53E3E),
      "textColor": Colors.white,
      "icon": Icons.local_offer,
    },
    {
      "id": "offer_2",
      "title": "طبق جديد",
      "subtitle": "مندي الدجاج الأصيل",
      "description":
          "جربوا طبقنا الجديد المندي بالدجاج مع الأرز البسمتي والسلطات",
      "type": NEW_DISH,
      "storeName": "مطعم الشرق",
      "storeId": "مطعم الشرق",
      "image": "images/img/2.png",
      "backgroundImage": "images/img/2.png", // استخدام نفس صورة المنتج كخلفية
      "validUntil": "2025-01-25",
      "isActive": true,
      "backgroundColor": const Color(0xFF38A169),
      "textColor": Colors.white,
      "icon": Icons.restaurant_menu,
    },
    {
      "id": "offer_3",
      "title": "توصيل مجاني",
      "subtitle": "للطلبات فوق 100 ريال",
      "description":
          "استمتع بالتوصيل المجاني لجميع الطلبات التي تزيد عن 100 ريال",
      "type": FREE_DELIVERY,
      "minOrderAmount": 100,
      "storeName": "سوبرماركت الأمانة",
      "storeId": "سوبرماركت الأمانة",
      "image": "images/img/3.png",
      "backgroundImage": "images/img/3.png", // استخدام نفس صورة المنتج كخلفية
      "validUntil": "2025-01-30",
      "isActive": true,
      "backgroundColor": const Color(0xFF3182CE),
      "textColor": Colors.white,
      "icon": Icons.delivery_dining,
    },
    {
      "id": "offer_4",
      "title": "1+1 مجاناً",
      "subtitle": "على المشروبات الغازية",
      "description": "اشتري أي مشروب غازي واحصل على آخر مجاناً من نفس النوع",
      "type": BUY_ONE_GET_ONE,
      "storeName": "مطعم البرجر",
      "storeId": "مطعم البرجر",
      "image": "images/img/4.png",
      "backgroundImage": "images/img/4.png", // استخدام نفس صورة المنتج كخلفية
      "validUntil": "2025-01-28",
      "isActive": true,
      "backgroundColor": const Color(0xFFED8936),
      "textColor": Colors.white,
      "icon": Icons.local_drink,
    },
    {
      "id": "offer_5",
      "title": "منتج جديد",
      "subtitle": "عسل طبيعي يمني",
      "description":
          "وصل حديثاً عسل طبيعي 100% من أجود المناحل اليمنية الأصيلة",
      "type": NEW_ITEM,
      "storeName": "سوبرماركت الأمانة",
      "storeId": "سوبرماركت الأمانة",
      "image": "images/img/5.png",
      "backgroundImage": "images/img/5.png", // استخدام نفس صورة المنتج كخلفية
      "validUntil": "2025-01-29",
      "isActive": true,
      "backgroundColor": const Color(0xFFD69E2E),
      "textColor": Colors.white,
      "icon": Icons.new_releases,
    },
    {
      "id": "offer_6",
      "title": "خصم 30%",
      "subtitle": "على الأدوية والمكملات",
      "description": "خصم خاص على جميع الأدوية والمكملات الغذائية والفيتامينات",
      "type": SPECIAL_OFFER,
      "discountPercentage": 30,
      "storeName": "صيدلية الصحة",
      "storeId": "صيدلية الصحة",
      "image": "images/img/6.png",
      "backgroundImage": "images/img/6.png", // استخدام نفس صورة المنتج كخلفية
      "validUntil": "2025-01-27",
      "isActive": true,
      "backgroundColor": const Color(0xFF319795),
      "textColor": Colors.white,
      "icon": Icons.medical_services,
    },
    {
      "id": "offer_7",
      "title": "عرض الجمعة",
      "subtitle": "خصم 40% على البيتزا",
      "description": "عرض خاص ليوم الجمعة فقط - خصم 40% على جميع أنواع البيتزا",
      "type": SPECIAL_OFFER,
      "discountPercentage": 40,
      "storeName": "مطعم البرجر",
      "storeId": "مطعم البرجر",
      "image": "images/img/7.png",
      "backgroundImage": "images/img/7.png", // استخدام نفس صورة المنتج كخلفية
      "validUntil": "2025-01-26",
      "isActive": true,
      "backgroundColor": const Color(0xFF9F7AEA),
      "textColor": Colors.white,
      "icon": Icons.local_pizza,
    },
    {
      "id": "offer_8",
      "title": "وجبة العائلة",
      "subtitle": "4 أشخاص بـ 150 ريال",
      "description": "وجبة عائلية كاملة تكفي 4 أشخاص مع المشروبات والحلويات",
      "type": SPECIAL_OFFER,
      "storeName": "مطعم الشرق",
      "storeId": "مطعم الشرق",
      "image": "images/img/8.png",
      "backgroundImage": "images/img/8.png", // استخدام نفس صورة المنتج كخلفية
      "validUntil": "2025-02-15",
      "isActive": true,
      "backgroundColor": const Color(0xFFE53E3E),
      "textColor": Colors.white,
      "icon": Icons.family_restroom,
    },
  ];

  // الحصول على جميع العروض النشطة
  static List<Map<String, dynamic>> getActiveOffers() {
    final activeOffers =
        _offers.where((offer) => offer['isActive'] == true).toList();

    return activeOffers;
  }

  // الحصول على العروض حسب النوع
  static List<Map<String, dynamic>> getOffersByType(String type) {
    return _offers
        .where((offer) => offer['type'] == type && offer['isActive'] == true)
        .toList();
  }

  // الحصول على العروض حسب المتجر
  static List<Map<String, dynamic>> getOffersByStore(String storeId) {
    return _offers
        .where(
            (offer) => offer['storeId'] == storeId && offer['isActive'] == true)
        .toList();
  }

  // البحث في العروض
  static List<Map<String, dynamic>> searchOffers(String query) {
    if (query.isEmpty) return getActiveOffers();

    final searchQuery = query.toLowerCase();
    return _offers.where((offer) {
      final title = offer['title']?.toString().toLowerCase() ?? '';
      final description = offer['description']?.toString().toLowerCase() ?? '';
      final storeName = offer['storeName']?.toString().toLowerCase() ?? '';

      return (title.contains(searchQuery) ||
              description.contains(searchQuery) ||
              storeName.contains(searchQuery)) &&
          offer['isActive'] == true;
    }).toList();
  }

  // الحصول على عرض محدد
  static Map<String, dynamic>? getOfferById(String offerId) {
    try {
      return _offers.firstWhere((offer) => offer['id'] == offerId);
    } catch (e) {
      return null;
    }
  }

  // الحصول على أيقونة حسب نوع العرض
  static IconData getOfferIcon(String type) {
    switch (type) {
      case DISCOUNT:
        return Icons.local_offer;
      case NEW_ITEM:
        return Icons.new_releases;
      case NEW_DISH:
        return Icons.restaurant_menu;
      case SPECIAL_OFFER:
        return Icons.star;
      case FREE_DELIVERY:
        return Icons.delivery_dining;
      case BUY_ONE_GET_ONE:
        return Icons.redeem;
      default:
        return Icons.local_offer;
    }
  }

  // الحصول على لون حسب نوع العرض
  static Color getOfferColor(String type) {
    switch (type) {
      case DISCOUNT:
        return Colors.red;
      case NEW_ITEM:
        return Colors.green;
      case NEW_DISH:
        return Colors.orange;
      case SPECIAL_OFFER:
        return Colors.purple;
      case FREE_DELIVERY:
        return Colors.blue;
      case BUY_ONE_GET_ONE:
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  // الحصول على نص نوع العرض
  static String getOfferTypeText(String type) {
    switch (type) {
      case DISCOUNT:
        return "خصم";
      case NEW_ITEM:
        return "منتج جديد";
      case NEW_DISH:
        return "طبق جديد";
      case SPECIAL_OFFER:
        return "عرض خاص";
      case FREE_DELIVERY:
        return "توصيل مجاني";
      case BUY_ONE_GET_ONE:
        return "اشتري واحد واحصل على آخر";
      default:
        return "عرض";
    }
  }

  // التحقق من صلاحية العرض
  static bool isOfferValid(Map<String, dynamic> offer) {
    if (!offer['isActive']) return false;

    final validUntil = offer['validUntil'];
    if (validUntil == null) return true;

    try {
      final expiryDate = DateTime.parse(validUntil);
      return DateTime.now().isBefore(expiryDate);
    } catch (e) {
      return true;
    }
  }

  // الحصول على العروض الصالحة فقط
  static List<Map<String, dynamic>> getValidOffers() {
    final validOffers = _offers.where((offer) => isOfferValid(offer)).toList();

    return validOffers;
  }

  // الحصول على عدد الأيام المتبقية للعرض
  static int getDaysRemaining(Map<String, dynamic> offer) {
    final validUntil = offer['validUntil'];
    if (validUntil == null) return -1;

    try {
      final expiryDate = DateTime.parse(validUntil);
      final now = DateTime.now();
      return expiryDate.difference(now).inDays;
    } catch (e) {
      return -1;
    }
  }

  // تصفية العروض حسب معايير متعددة
  static List<Map<String, dynamic>> filterOffers({
    String? type,
    String? storeId,
    bool? isActive,
    bool validOnly = true,
  }) {
    var filtered = _offers.where((offer) {
      if (type != null && offer['type'] != type) return false;
      if (storeId != null && offer['storeId'] != storeId) return false;
      if (isActive != null && offer['isActive'] != isActive) return false;
      if (validOnly && !isOfferValid(offer)) return false;
      return true;
    }).toList();

    return filtered;
  }

  // ترتيب العروض حسب الأولوية
  static List<Map<String, dynamic>> sortOffersByPriority(
      List<Map<String, dynamic>> offers) {
    _offers.sort((a, b) {
      // ترتيب حسب نوع العرض (الخصومات أولاً)
      final typeOrder = {
        DISCOUNT: 1,
        SPECIAL_OFFER: 2,
        BUY_ONE_GET_ONE: 3,
        FREE_DELIVERY: 4,
        NEW_DISH: 5,
        NEW_ITEM: 6,
      };

      final aOrder = typeOrder[a['type']] ?? 999;
      final bOrder = typeOrder[b['type']] ?? 999;

      if (aOrder != bOrder) {
        return aOrder.compareTo(bOrder);
      }

      // ترتيب حسب الأيام المتبقية (الأقرب للانتهاء أولاً)
      final aDays = getDaysRemaining(a);
      final bDays = getDaysRemaining(b);

      if (aDays != bDays) {
        return aDays.compareTo(bDays);
      }

      return 0;
    });

    return _offers;
  }
}
