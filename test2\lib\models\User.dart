class User {
  final String id;
  final String name;
  final String phone;
  final String? email;
  final String? address;
  final String? city;
  final double? latitude;
  final double? longitude;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isGuest;

  User({
    required this.id,
    required this.name,
    required this.phone,
    this.email,
    this.address,
    this.city,
    this.latitude,
    this.longitude,
    required this.createdAt,
    this.updatedAt,
    this.isGuest = false,
  });

  // تحويل إلى Map للحفظ
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'address': address,
      'city': city,
      'latitude': latitude,
      'longitude': longitude,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isGuest': isGuest,
    };
  }

  // إنشاء من Map
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'],
      name: map['name'],
      phone: map['phone'],
      email: map['email'],
      address: map['address'],
      city: map['city'],
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
      isGuest: map['isGuest'] ?? false,
    );
  }

  // نسخ مع تعديل
  User copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    String? address,
    String? city,
    double? latitude,
    double? longitude,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isGuest,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      city: city ?? this.city,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isGuest: isGuest ?? this.isGuest,
    );
  }

  // إنشاء مستخدم ضيف
  factory User.guest() {
    return User(
      id: 'guest_${DateTime.now().millisecondsSinceEpoch}',
      name: 'ضيف',
      phone: '',
      createdAt: DateTime.now(),
      isGuest: true,
    );
  }

  // التحقق من اكتمال البيانات
  bool get isComplete {
    return name.isNotEmpty && 
           phone.isNotEmpty && 
           address != null && 
           address!.isNotEmpty;
  }

  // الحصول على الاسم المعروض
  String get displayName {
    if (isGuest) return 'ضيف';
    return name.isNotEmpty ? name : 'مستخدم';
  }

  // الحصول على العنوان المختصر
  String get shortAddress {
    if (address == null || address!.isEmpty) return 'لم يتم تحديد العنوان';
    if (address!.length <= 30) return address!;
    return '${address!.substring(0, 30)}...';
  }

  @override
  String toString() {
    return 'User{id: $id, name: $name, phone: $phone, isGuest: $isGuest}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
