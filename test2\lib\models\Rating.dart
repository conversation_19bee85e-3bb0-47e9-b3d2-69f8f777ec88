/// نموذج التقييم
class Rating {
  final String id;
  final String itemId;
  final RatingType itemType;
  final double rating;
  final String comment;
  final String userId;
  final String userName;
  final DateTime timestamp;
  final bool isVerified;
  final int helpfulCount;
  final List<String> images;

  Rating({
    required this.id,
    required this.itemId,
    required this.itemType,
    required this.rating,
    required this.comment,
    required this.userId,
    required this.userName,
    required this.timestamp,
    this.isVerified = false,
    this.helpfulCount = 0,
    this.images = const [],
  });

  /// تحويل من JSON
  factory Rating.fromJson(Map<String, dynamic> json) {
    return Rating(
      id: json['id'] ?? '',
      itemId: json['itemId'] ?? '',
      itemType: RatingType.values.firstWhere(
        (e) => e.toString() == json['itemType'],
        orElse: () => RatingType.product,
      ),
      rating: (json['rating'] ?? 0.0).toDouble(),
      comment: json['comment'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      isVerified: json['isVerified'] ?? false,
      helpfulCount: json['helpfulCount'] ?? 0,
      images: List<String>.from(json['images'] ?? []),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'itemId': itemId,
      'itemType': itemType.toString(),
      'rating': rating,
      'comment': comment,
      'userId': userId,
      'userName': userName,
      'timestamp': timestamp.toIso8601String(),
      'isVerified': isVerified,
      'helpfulCount': helpfulCount,
      'images': images,
    };
  }

  /// نسخ مع تعديل
  Rating copyWith({
    String? id,
    String? itemId,
    RatingType? itemType,
    double? rating,
    String? comment,
    String? userId,
    String? userName,
    DateTime? timestamp,
    bool? isVerified,
    int? helpfulCount,
    List<String>? images,
  }) {
    return Rating(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      itemType: itemType ?? this.itemType,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      timestamp: timestamp ?? this.timestamp,
      isVerified: isVerified ?? this.isVerified,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      images: images ?? this.images,
    );
  }

  /// الحصول على الوقت المنسق
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  /// الحصول على النجوم كنص
  String get starsText {
    return '⭐' * rating.round();
  }

  /// الحصول على تقييم الجودة
  String get qualityText {
    if (rating >= 4.5) {
      return 'ممتاز';
    } else if (rating >= 3.5) {
      return 'جيد جداً';
    } else if (rating >= 2.5) {
      return 'جيد';
    } else if (rating >= 1.5) {
      return 'مقبول';
    } else {
      return 'ضعيف';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Rating && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Rating(id: $id, itemId: $itemId, rating: $rating, userName: $userName)';
  }
}

/// تقييم المستخدم
class UserRating {
  final String id;
  final String itemId;
  final RatingType itemType;
  final double rating;
  final String comment;
  final DateTime timestamp;
  final List<String> images;

  UserRating({
    required this.id,
    required this.itemId,
    required this.itemType,
    required this.rating,
    required this.comment,
    required this.timestamp,
    this.images = const [],
  });

  /// تحويل من JSON
  factory UserRating.fromJson(Map<String, dynamic> json) {
    return UserRating(
      id: json['id'] ?? '',
      itemId: json['itemId'] ?? '',
      itemType: RatingType.values.firstWhere(
        (e) => e.toString() == json['itemType'],
        orElse: () => RatingType.product,
      ),
      rating: (json['rating'] ?? 0.0).toDouble(),
      comment: json['comment'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      images: List<String>.from(json['images'] ?? []),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'itemId': itemId,
      'itemType': itemType.toString(),
      'rating': rating,
      'comment': comment,
      'timestamp': timestamp.toIso8601String(),
      'images': images,
    };
  }

  /// نسخ مع تعديل
  UserRating copyWith({
    String? id,
    String? itemId,
    RatingType? itemType,
    double? rating,
    String? comment,
    DateTime? timestamp,
    List<String>? images,
  }) {
    return UserRating(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      itemType: itemType ?? this.itemType,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      timestamp: timestamp ?? this.timestamp,
      images: images ?? this.images,
    );
  }

  /// الحصول على الوقت المنسق
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}

/// إحصائيات التقييم
class RatingStats {
  final String itemId;
  final RatingType itemType;
  final int totalCount;
  final double averageRating;
  final Map<int, int> starDistribution;
  final DateTime lastUpdated;

  RatingStats({
    required this.itemId,
    required this.itemType,
    required this.totalCount,
    required this.averageRating,
    required this.starDistribution,
    required this.lastUpdated,
  });

  /// تحويل من JSON
  factory RatingStats.fromJson(Map<String, dynamic> json) {
    return RatingStats(
      itemId: json['itemId'] ?? '',
      itemType: RatingType.values.firstWhere(
        (e) => e.toString() == json['itemType'],
        orElse: () => RatingType.product,
      ),
      totalCount: json['totalCount'] ?? 0,
      averageRating: (json['averageRating'] ?? 0.0).toDouble(),
      starDistribution: Map<int, int>.from(json['starDistribution'] ?? {}),
      lastUpdated: DateTime.parse(json['lastUpdated'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'itemId': itemId,
      'itemType': itemType.toString(),
      'totalCount': totalCount,
      'averageRating': averageRating,
      'starDistribution': starDistribution,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// الحصول على نسبة النجوم
  double getStarPercentage(int stars) {
    if (totalCount == 0) return 0.0;
    final count = starDistribution[stars] ?? 0;
    return (count / totalCount) * 100;
  }

  /// الحصول على التقييم المنسق
  String get formattedRating {
    return averageRating.toStringAsFixed(1);
  }

  /// الحصول على النجوم كنص
  String get starsText {
    return '⭐' * averageRating.round();
  }

  /// الحصول على تقييم الجودة
  String get qualityText {
    if (averageRating >= 4.5) {
      return 'ممتاز';
    } else if (averageRating >= 3.5) {
      return 'جيد جداً';
    } else if (averageRating >= 2.5) {
      return 'جيد';
    } else if (averageRating >= 1.5) {
      return 'مقبول';
    } else {
      return 'ضعيف';
    }
  }
}

/// أنواع التقييم
enum RatingType {
  product,    // منتج
  store,      // متجر
  delivery,   // توصيل
  service,    // خدمة
  app,        // التطبيق
}

/// معايير التقييم
class RatingCriteria {
  static const Map<RatingType, List<String>> criteria = {
    RatingType.product: [
      'الجودة',
      'الطعم',
      'السعر',
      'التغليف',
      'المظهر',
    ],
    RatingType.store: [
      'الخدمة',
      'السرعة',
      'النظافة',
      'التنوع',
      'الأسعار',
    ],
    RatingType.delivery: [
      'السرعة',
      'الدقة',
      'حالة الطلب',
      'التعامل',
      'التوقيت',
    ],
    RatingType.service: [
      'الاستجابة',
      'الحل',
      'الود',
      'الاحترافية',
      'المتابعة',
    ],
    RatingType.app: [
      'سهولة الاستخدام',
      'السرعة',
      'التصميم',
      'الوظائف',
      'الاستقرار',
    ],
  };

  /// الحصول على معايير التقييم لنوع معين
  static List<String> getCriteriaForType(RatingType type) {
    return criteria[type] ?? [];
  }
}
