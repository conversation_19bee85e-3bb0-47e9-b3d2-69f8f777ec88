import 'package:flutter/material.dart';

class ItemAppBar extends StatelessWidget implements PreferredSizeWidget{
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFC3243B),
      padding: EdgeInsets.only(top: 25,left: 25,right: 25,bottom: 5),
      margin: EdgeInsets.only(top: 15),
      child: Row(
        children: [
          Icon(
            Icons.favorite,
            size: 30,
            color: Color.fromARGB(255, 245, 170, 73),
          ),

          Spacer(),
          Padding(
            padding:EdgeInsets.only(right: 20),
            child: Text(
              "اسم المنتج",
              style: TextStyle(
                fontSize: 23,
                fontWeight: FontWeight.bold,
                color: Colors.white
              ),
            ), 
          ),
          
          InkWell(
            onTap: (){
              Navigator.pop(context);
            },
            child: Icon(
              Icons.arrow_forward,
              size: 30,
              color: Colors.white,
            ),
          ),
          
        ],
      ),
    );
  }
  @override
  Size get preferredSize => Size.fromHeight(50);
}