import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:test2/pages/CartPage.dart';
import 'package:test2/utils/ResponsiveHelper.dart';
import 'package:test2/widgets/ResponsiveText.dart';
import 'package:test2/services/UserService.dart';
import 'package:test2/pages/WelcomePage.dart';
import 'package:test2/utils/AppState.dart';
import 'package:test2/utils/AppConfig.dart';
import 'package:test2/providers/CartProvider.dart';
import 'package:badges/badges.dart' as badges;

class HomeAppBar extends StatelessWidget implements PreferredSizeWidget {
  final UserService _userService = UserService();

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFC3243B),
      padding: ResponsiveHelper.responsiveEdgeInsets(
        context,
        mobile: 12,
        tablet: 15,
        desktop: 18,
      ),
      margin: ResponsiveHelper.responsiveEdgeInsetsCustom(
        context,
        top: 12,
      ),
      child: Row(
        children: [
          // InkWell(
          //   onTap: () {
          //     Navigator.push(
          //       context,
          //       MaterialPageRoute(builder: (context) => StoresPage()),
          //     );
          //   },
          //   child: Icon(
          //     Icons.store,
          //     size: 28,
          //     color: Colors.white,
          //   ),
          // ),
          // SizedBox(width: 5),
          // InkWell(
          //   onTap: () {
          //     Navigator.push(
          //       context,
          //       MaterialPageRoute(builder: (context) => OrdersPage()),
          //     );
          //   },
          //   child: Icon(
          //     Icons.shopping_bag,
          //     size: 28,
          //     color: Colors.white,
          //   ),
          // ),
          // SizedBox(width: 5),
          Consumer<CartProvider>(
            builder: (context, cartProvider, child) {
              return badges.Badge(
                badgeStyle: badges.BadgeStyle(
                  badgeColor: Color.fromARGB(255, 245, 170, 73),
                  padding: ResponsiveHelper.responsiveEdgeInsets(
                    context,
                    mobile: 5,
                    tablet: 7,
                    desktop: 9,
                  ),
                ),
                showBadge: cartProvider.totalQuantity > 0,
                badgeContent: ResponsiveText(
                  cartProvider.totalQuantity > 9
                      ? '9+'
                      : cartProvider.totalQuantity.toString(),
                  mobileFontSize: 10,
                  tabletFontSize: 12,
                  desktopFontSize: 14,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                child: InkWell(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => CartPage()),
                    );
                  },
                  child: Icon(
                    Icons.shopping_cart_outlined,
                    size: ResponsiveHelper.responsiveIconSize(
                      context,
                      mobile: 24,
                      tablet: 28,
                      desktop: 32,
                    ),
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),
          Spacer(),
          Padding(
            padding: ResponsiveHelper.responsiveEdgeInsetsCustom(
              context,
              left: 16,
            ),
            child: ResponsiveText(
              AppConfig.appName,
              mobileFontSize: 18,
              tabletFontSize: 23,
              desktopFontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Icon(
            Icons.sort,
            size: ResponsiveHelper.responsiveIconSize(
              context,
              mobile: 26,
              tablet: 30,
              desktop: 34,
            ),
            color: Colors.white,
          ),

          // زر التسجيل للضيوف
          // if (_userService.needsRegistration()) ...[
          //   SizedBox(
          //       width: ResponsiveHelper.responsiveSpacing(context, mobile: 8)),
          //   GestureDetector(
          //     onTap: () => _showGuestRegistrationDialog(context),
          //     child: Container(
          //       padding: ResponsiveHelper.responsiveEdgeInsets(
          //         context,
          //         mobile: 6,
          //         tablet: 8,
          //         desktop: 10,
          //       ),
          //       decoration: BoxDecoration(
          //         color: Colors.white.withOpacity(0.2),
          //         borderRadius: BorderRadius.circular(
          //           ResponsiveHelper.responsiveBorderRadius(context, mobile: 6),
          //         ),
          //         border: Border.all(
          //           color: Colors.white.withOpacity(0.5),
          //           width: 1,
          //         ),
          //       ),
          //       child: Row(
          //         mainAxisSize: MainAxisSize.min,
          //         children: [
          //           Icon(
          //             Icons.person_add,
          //             size: ResponsiveHelper.responsiveIconSize(
          //               context,
          //               mobile: 16,
          //               tablet: 18,
          //               desktop: 20,
          //             ),
          //             color: Colors.white,
          //           ),
          //           SizedBox(width: 4),
          //           ResponsiveText(
          //             'تسجيل',
          //             mobileFontSize: 12,
          //             tabletFontSize: 14,
          //             desktopFontSize: 16,
          //             color: Colors.white,
          //             fontWeight: FontWeight.bold,
          //           ),
          //         ],
          //       ),
          //     ),
          //   ),
          // ],
        ],
      ),
    );
  }

  // عرض حوار للضيوف للانتقال للتسجيل
  void _showGuestRegistrationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: ResponsiveText(
          'تسجيل حساب جديد',
          mobileFontSize: 18,
          tabletFontSize: 20,
          desktopFontSize: 22,
          fontWeight: FontWeight.bold,
          color: Color(0xFF4C53A5),
        ),
        content: ResponsiveText(
          'للحصول على تجربة أفضل وحفظ طلباتك، يمكنك تسجيل حساب جديد بسهولة.',
          mobileFontSize: 14,
          tabletFontSize: 16,
          desktopFontSize: 18,
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: ResponsiveText(
              'لاحقاً',
              mobileFontSize: 14,
              tabletFontSize: 16,
              desktopFontSize: 18,
              color: Colors.grey,
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // مسح بيانات الضيف والانتقال لصفحة الترحيب
              _userService.clearGuestData().then((_) {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => WelcomePage(),
                  ),
                );
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFF4C53A5),
            ),
            child: ResponsiveText(
              'تسجيل الآن',
              mobileFontSize: 14,
              tabletFontSize: 16,
              desktopFontSize: 18,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(70);
}
